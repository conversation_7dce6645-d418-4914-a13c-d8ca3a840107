package com.hualu.watermask.modules.config;

import org.springframework.context.annotation.Bean;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

//@Configuration
public class CorsConfig {
    /**
     * 解决跨域问题的过滤器
     */
    @Bean
    public CorsFilter corsFilter() {
        // 1.创建CORS配置对象
        CorsConfiguration config = new CorsConfiguration();
        // 允许的源，这里使用通配符，生产环境建议指定具体域名
        config.addAllowedOriginPattern("*");
        // 允许携带Cookie
        config.setAllowCredentials(true);
        // 允许的请求方法
        config.addAllowedMethod("*");
        // 允许的请求头
        config.addAllowedHeader("*");
        // 允许暴露的响应头（重要，比如token信息可能需要前端获取）
        config.addExposedHeader("*");
        // 预检请求的有效期，单位秒
        config.setMaxAge(3600L);

        // 2.添加地址映射
        UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
        source.registerCorsConfiguration("/**", config);

        // 3.返回CORS过滤器
        return new CorsFilter(source);
    }
}
