package com.hualu.watermask.modules.config;

import cn.dev33.satoken.stp.StpInterface;
import com.hualu.watermask.modules.common.datastore.HttpDataStore;
import com.hualu.watermask.modules.permission.service.WsPermissionsService;
import com.hualu.watermask.modules.role.service.WsRoleService;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Component;

@Component
public class StpInterfaceImpl implements StpInterface {

  @Resource
  private WsPermissionsService permissionsService;

  @Resource
  private WsRoleService roleService;

  @Override
  public List<String> getPermissionList(Object loginId, String loginType) {
    String teamId = HttpDataStore.getInstance().getTeamId();
    return permissionsService.getPermissions((String) loginId, teamId);
  }

  @Override
  public List<String> getRoleList(Object loginId, String loginType) {
    String teamId = HttpDataStore.getInstance().getTeamId();
    return roleService.getRoles((String) loginId, teamId);
  }

}