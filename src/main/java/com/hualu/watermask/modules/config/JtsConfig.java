package com.hualu.watermask.modules.config;

import org.locationtech.jts.geom.GeometryFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Jts配置
 *
 * <AUTHOR>
 * @since 2023-12-12 17:37
 */
@Configuration
public class JtsConfig {

  @Bean
  public GeometryFactory jtsGeometryFactory() {
    return new GeometryFactory();
  }

}
