package com.hualu.watermask.modules.config;

import cn.dev33.satoken.fun.strategy.SaCorsHandleFunction;
import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaHttpMethod;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class SaTokenConfigure implements WebMvcConfigurer {

    /**
     * CORS 跨域处理策略
     */
    @Bean
    public SaCorsHandleFunction corsHandle() {
        return (req, res, sto) -> {
            res.
                // 允许指定域访问跨域资源
                setHeader("Access-Control-Allow-Origin", "*")
                // 允许所有请求方式
                .setHeader("Access-Control-Allow-Methods", "*")
                // 有效时间
                .setHeader("Access-Control-Max-Age", "3600")
                // 允许的header参数
                .setHeader("Access-Control-Allow-Headers", "*")
                //允许暴露的响应头（重要，比如token信息可能需要前端获取）
                .setHeader("Access-Control-Expose-Headers", "*")
                //允许携带Cookie
                .setHeader("Access-Control-Allow-Credentials", "true");

            // 如果是预检请求，则立即返回到前端
            SaRouter.match(SaHttpMethod.OPTIONS)
                .free(r -> {})
                .back();
        };
    }

    // 解决跨域问题
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        // 允许所有跨域请求
        registry.addMapping("/**")
                .allowedOriginPatterns("*")  // 允许的来源，Spring Boot 2.4+ 推荐使用 allowedOriginPatterns
                .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS")  // 允许的请求方法
                .allowedHeaders("*")  // 允许的请求头
                .allowCredentials(true)  // 是否允许携带cookie
                .maxAge(3600);  // 预检请求的有效期，单位秒
    }

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，定义详细认证规则
        registry.addInterceptor(new SaInterceptor(handler -> {
            // 指定一条 match 规则
            SaRouter
                .match("/**")    // 拦截的 path 列表，可以写多个 */
                .notMatch("/user/login", "/api/template/list", "/api/template/page", "/user/register")        // 排除掉的 path 列表，可以写多个
                .check(r -> StpUtil.checkLogin());        // 要执行的校验动作，可以写完整的 lambda 表达式

            // 根据路由划分模块，不同模块不同鉴权
            //SaRouter.match("/user/**", r -> StpUtil.checkPermission("user"));
            //SaRouter.match("/admin/**", r -> StpUtil.checkPermission("admin"));
            //SaRouter.match("/goods/**", r -> StpUtil.checkPermission("goods"));
            //SaRouter.match("/orders/**", r -> StpUtil.checkPermission("orders"));
            //SaRouter.match("/notice/**", r -> StpUtil.checkPermission("notice"));
            //SaRouter.match("/comment/**", r -> StpUtil.checkPermission("comment"));
        })).addPathPatterns("/**");

        registry.addInterceptor(new CustomInterceptor())
            .addPathPatterns("/**");
    }
}
