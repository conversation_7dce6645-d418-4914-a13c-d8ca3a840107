package com.hualu.watermask.modules.permission.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.cache.constants.CacheNameSpace;
import com.hualu.watermask.modules.permission.entity.WsPermissions;
import com.hualu.watermask.modules.permission.mapper.WsPermissionsMapper;
import com.hualu.watermask.modules.permission.service.WsPermissionsService;
import com.hualu.watermask.modules.role.entity.WsRolePermissions;
import com.hualu.watermask.modules.role.service.WsRolePermissionsService;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import com.hualu.watermask.modules.user.service.WsUserTeamsService;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

@Service
@DS("master")
public class WsPermissionsServiceImpl extends ServiceImpl<WsPermissionsMapper, WsPermissions>
    implements WsPermissionsService {

  @Resource
  private WsRolePermissionsService rolePermissionsService;

  @Resource
  private WsUserTeamsService userTeamsService;

  @Override
  @Cacheable(
      value = CacheNameSpace.SA_TOKEN_PERMISSION,
      key = "'permission:' + #loginId + '_' + #teamId",
      unless = "#result == null || #result.isEmpty()"
  )
  public List<String> getPermissions(String loginId, String teamId) {
    List<WsUserTeams> userTeams = userTeamsService.lambdaQuery()
        .eq(WsUserTeams::getUserId, loginId)
        .eq(WsUserTeams::getTeamId, teamId)
        .list();
    if (CollUtil.isEmpty(userTeams)) {
      return new ArrayList<>();
    }

    List<String> roleIds = userTeams.stream()
        .map(WsUserTeams::getRoleId)
        .distinct()
        .collect(Collectors.toList());

    List<String> permissionIds = rolePermissionsService.lambdaQuery()
        .in(WsRolePermissions::getRoleId, roleIds)
        .list().stream()
        .map(WsRolePermissions::getPermissionId)
        .distinct()
        .collect(Collectors.toList());

    return listByIds(permissionIds).stream()
        .map(WsPermissions::getPermissionCode)
        .distinct()
        .collect(Collectors.toList());
  }
}
