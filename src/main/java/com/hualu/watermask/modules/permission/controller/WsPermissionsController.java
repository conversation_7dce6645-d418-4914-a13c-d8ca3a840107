package com.hualu.watermask.modules.permission.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.permission.entity.WsPermissions;
import com.hualu.watermask.modules.permission.service.WsPermissionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 权限管理控制器
 */
@RestController
@RequestMapping("/api/permission")
public class WsPermissionsController {

    @Autowired
    private WsPermissionsService wsPermissionsService;

    /**
     * 创建权限
     *
     * @param permission 权限信息
     * @return 创建结果
     */
    @PostMapping("/create")
    @OperationLog(businessType = "权限管理", operationContent = "创建权限", operationType = "INSERT")
    public RestResult<WsPermissions> create(@RequestBody WsPermissions permission) {
        // 设置初始值
        permission.setPermissionId(UUID.randomUUID().toString().replace("-", ""));
        permission.setCreationDate(new Date());
        permission.setDelFlag(new BigDecimal(0));

        boolean result = wsPermissionsService.save(permission);
        if (result) {
            return RestResult.success(permission, "创建权限成功");
        } else {
            return RestResult.error("创建权限失败");
        }
    }

    /**
     * 批量创建权限
     *
     * @param permissionList 权限列表
     * @return 创建结果
     */
    @PostMapping("/batch/create")
    @OperationLog(businessType = "权限管理", operationContent = "批量创建权限", operationType = "INSERT")
    public RestResult<List<WsPermissions>> createBatch(@RequestBody List<WsPermissions> permissionList) {
        // 设置初始值
        for (WsPermissions permission : permissionList) {
            permission.setPermissionId(UUID.randomUUID().toString().replace("-", ""));
            permission.setCreationDate(new Date());
            permission.setDelFlag(new BigDecimal(0));
        }

        boolean result = wsPermissionsService.saveBatch(permissionList);
        if (result) {
            return RestResult.success(permissionList, "批量创建权限成功");
        } else {
            return RestResult.error("批量创建权限失败");
        }
    }

    /**
     * 更新权限
     *
     * @param permissionId 权限ID
     * @param permission 更新的权限信息
     * @return 更新结果
     */
    @PutMapping("/update")
    @OperationLog(businessType = "权限管理", operationContent = "更新权限", operationType = "UPDATE")
    public RestResult<WsPermissions> update(@RequestParam String permissionId, @RequestBody WsPermissions permission) {
        permission.setPermissionId(permissionId);

        boolean result = wsPermissionsService.updateById(permission);
        if (result) {
            return RestResult.success(permission, "更新权限成功");
        } else {
            return RestResult.error("更新权限失败，权限可能不存在");
        }
    }

    /**
     * 删除权限（逻辑删除）
     *
     * @param permissionId 权限ID
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @OperationLog(businessType = "权限管理", operationContent = "删除权限", operationType = "DELETE")
    public RestResult<Object> delete(@RequestParam String permissionId) {
        WsPermissions permission = new WsPermissions();
        permission.setPermissionId(permissionId);
        permission.setDelFlag(new BigDecimal(1));

        boolean result = wsPermissionsService.updateById(permission);
        if (result) {
            return RestResult.success(null, "删除权限成功");
        } else {
            return RestResult.error("删除权限失败，权限可能不存在");
        }
    }

    /**
     * 获取权限详情
     *
     * @param permissionId 权限ID
     * @return 权限详情
     */
    @GetMapping("/get")
    @OperationLog(businessType = "权限管理", operationContent = "获取权限详情", operationType = "SELECT")
    public RestResult<WsPermissions> getById(@RequestParam String permissionId) {
        WsPermissions permission = wsPermissionsService.getById(permissionId);
        if (permission != null && permission.getDelFlag().compareTo(new BigDecimal(0)) == 0) {
            return RestResult.success(permission);
        } else {
            return RestResult.error("未找到指定的权限或权限已被删除");
        }
    }

    /**
     * 分页查询权限列表
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @param permissionName 权限名称（可选，模糊查询）
     * @param permissionCode 权限代码（可选，模糊查询）
     * @param category 权限分类（可选，精确查询）
     * @return 分页结果
     */
    @GetMapping("/list")
    @OperationLog(businessType = "权限管理", operationContent = "查询权限列表", operationType = "SELECT")
    public RestResult<List<WsPermissions>> list(
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestParam(required = false) String permissionName,
            @RequestParam(required = false) String permissionCode,
            @RequestParam(required = false) String category) {

        Page<WsPermissions> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<WsPermissions> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq(WsPermissions::getDelFlag, new BigDecimal(0));

        // 根据条件过滤
        if (permissionName != null && !permissionName.isEmpty()) {
            queryWrapper.like(WsPermissions::getPermissionName, permissionName);
        }

        if (permissionCode != null && !permissionCode.isEmpty()) {
            queryWrapper.like(WsPermissions::getPermissionCode, permissionCode);
        }

        if (category != null && !category.isEmpty()) {
            queryWrapper.eq(WsPermissions::getCategory, category);
        }

        // 按权限代码排序
        queryWrapper.orderByAsc(WsPermissions::getPermissionCode);

        IPage<WsPermissions> result = wsPermissionsService.page(pageParam, queryWrapper);
        return RestResult.success(result.getRecords(), result.getTotal(), page, pageSize);
    }

    /**
     * 查询所有权限分类
     *
     * @return 权限分类列表
     */
    @GetMapping("/categories")
    public RestResult<List<String>> listCategories() {
        LambdaQueryWrapper<WsPermissions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsPermissions::getDelFlag, new BigDecimal(0));
        queryWrapper.select(WsPermissions::getCategory);
        queryWrapper.groupBy(WsPermissions::getCategory);

        List<WsPermissions> permissions = wsPermissionsService.list(queryWrapper);
        List<String> categories = permissions.stream()
                .map(WsPermissions::getCategory)
                .filter(category -> category != null && !category.isEmpty())
                .distinct()
                .sorted()
                .collect(Collectors.toList());

        return RestResult.success(categories);
    }

    /**
     * 按分类查询权限列表
     *
     * @param category 权限分类
     * @return 权限列表
     */
    @GetMapping("/getByCategory")
    public RestResult<List<WsPermissions>> getByCategory(@RequestParam String category) {
        LambdaQueryWrapper<WsPermissions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsPermissions::getCategory, category);
        queryWrapper.eq(WsPermissions::getDelFlag, new BigDecimal(0));
        queryWrapper.orderByAsc(WsPermissions::getPermissionCode);

        List<WsPermissions> permissions = wsPermissionsService.list(queryWrapper);
        return RestResult.success(permissions);
    }

    /**
     * 通过权限代码查询权限
     *
     * @param permissionCode 权限代码
     * @return 权限信息
     */
    @GetMapping("/getByCode")
    public RestResult<WsPermissions> getByCode(@RequestParam String permissionCode) {
        LambdaQueryWrapper<WsPermissions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsPermissions::getPermissionCode, permissionCode);
        queryWrapper.eq(WsPermissions::getDelFlag, new BigDecimal(0));

        WsPermissions permission = wsPermissionsService.getOne(queryWrapper);
        if (permission != null) {
            return RestResult.success(permission);
        } else {
            return RestResult.error("未找到指定代码的权限");
        }
    }
} 