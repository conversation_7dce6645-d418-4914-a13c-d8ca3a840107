package com.hualu.watermask.modules.permission.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 系统权限定义表，定义具体操作权限
 */
@TableName(value = "pmsdb.WS_PERMISSIONS")
public class WsPermissions {
    /**
     * 权限唯一标识符，主键
     */
    @TableId(value = "PERMISSION_ID")
    private String permissionId;

    /**
     * 权限代码（SYSTEM:ROOT, CODE:PUSH等）
     */
    @TableField(value = "PERMISSION_CODE")
    private String permissionCode;

    /**
     * 上级权限唯一标识符
     */
    @TableField(value = "PARENT_PERMISSION_ID")
    private String parentPermissionId;

    /**
     * 权限显示名称
     */
    @TableField(value = "PERMISSION_NAME")
    private String permissionName;

    /**
     * 权限详细描述信息
     */
    @TableField(value = "PERMISSION_DESCRIPTION")
    private String permissionDescription;

    /**
     * 菜单类别：目录1、菜单2、按钮3
     */
    @TableField(value = "CATEGORY")
    private String category;

    /**
     * 菜单路径
     */
    @TableField(value = "MENU_PATH")
    private String menuPath;

    /**
     * 权限创建者用户名
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 排序字段
     */
    @TableField(value = "SORT_ORDER")
    private Double sortOrder;

    /**
     * 1启用，0未启
     */
    @TableField(value = "STATUS")
    private Double status;

    /**
     * 权限创建时间
     */
    @TableField(value = "CREATION_DATE")
    private Date creationDate;

    /**
     * 是否删除1删除0未删除
     */
    @TableField(value = "DEL_FLAG")
    private BigDecimal delFlag;

    /**
     * 获取权限唯一标识符，主键
     *
     * @return PERMISSION_ID - 权限唯一标识符，主键
     */
    public String getPermissionId() {
        return permissionId;
    }

    /**
     * 设置权限唯一标识符，主键
     *
     * @param permissionId 权限唯一标识符，主键
     */
    public void setPermissionId(String permissionId) {
        this.permissionId = permissionId;
    }

    /**
     * 获取权限代码（SYSTEM:ROOT, CODE:PUSH等）
     *
     * @return PERMISSION_CODE - 权限代码（SYSTEM:ROOT, CODE:PUSH等）
     */
    public String getPermissionCode() {
        return permissionCode;
    }

    /**
     * 设置权限代码（SYSTEM:ROOT, CODE:PUSH等）
     *
     * @param permissionCode 权限代码（SYSTEM:ROOT, CODE:PUSH等）
     */
    public void setPermissionCode(String permissionCode) {
        this.permissionCode = permissionCode;
    }

    /**
     * 获取权限显示名称
     *
     * @return PERMISSION_NAME - 权限显示名称
     */
    public String getPermissionName() {
        return permissionName;
    }

    /**
     * 设置权限显示名称
     *
     * @param permissionName 权限显示名称
     */
    public void setPermissionName(String permissionName) {
        this.permissionName = permissionName;
    }

    /**
     * 获取权限详细描述信息
     *
     * @return PERMISSION_DESCRIPTION - 权限详细描述信息
     */
    public String getPermissionDescription() {
        return permissionDescription;
    }

    /**
     * 设置权限详细描述信息
     *
     * @param permissionDescription 权限详细描述信息
     */
    public void setPermissionDescription(String permissionDescription) {
        this.permissionDescription = permissionDescription;
    }

    /**
     * 获取权限分类（SYSTEM, CODE, DATA, REPORT等）
     *
     * @return CATEGORY - 权限分类（SYSTEM, CODE, DATA, REPORT等）
     */
    public String getCategory() {
        return category;
    }

    /**
     * 设置权限分类（SYSTEM, CODE, DATA, REPORT等）
     *
     * @param category 权限分类（SYSTEM, CODE, DATA, REPORT等）
     */
    public void setCategory(String category) {
        this.category = category;
    }

    /**
     * 获取权限创建者用户名
     *
     * @return CREATED_BY - 权限创建者用户名
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 设置权限创建者用户名
     *
     * @param createdBy 权限创建者用户名
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 获取权限创建时间
     *
     * @return CREATION_DATE - 权限创建时间
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     * 设置权限创建时间
     *
     * @param creationDate 权限创建时间
     */
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    /**
     * 获取是否删除1删除0未删除
     *
     * @return DEL_FLAG - 是否删除1删除0未删除
     */
    public BigDecimal getDelFlag() {
        return delFlag;
    }

    /**
     * 设置是否删除1删除0未删除
     *
     * @param delFlag 是否删除1删除0未删除
     */
    public void setDelFlag(BigDecimal delFlag) {
        this.delFlag = delFlag;
    }

    public String getParentPermissionId() {
        return parentPermissionId;
    }

    public void setParentPermissionId(String parentPermissionId) {
        this.parentPermissionId = parentPermissionId;
    }

    public String getMenuPath() {
        return menuPath;
    }

    public void setMenuPath(String menuPath) {
        this.menuPath = menuPath;
    }

    public Double getSortOrder() {
        return sortOrder;
    }

    public void setSortOrder(Double sortOrder) {
        this.sortOrder = sortOrder;
    }

    public Double getStatus() {
        return status;
    }

    public void setStatus(Double status) {
        this.status = status;
    }
}