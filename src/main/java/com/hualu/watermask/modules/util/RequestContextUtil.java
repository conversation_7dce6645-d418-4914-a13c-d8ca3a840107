package com.hualu.watermask.modules.util;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * 请求上下文工具类，用于在单次请求内存储和获取数据
 */
public class RequestContextUtil {

  /**
   * 设置请求属性（单次请求内有效）
   * @param key   属性名
   * @param value 属性值
   */
  public static void setAttribute(String key, Object value) {
    ServletRequestAttributes requestAttributes = getRequestAttributes();
    if (requestAttributes != null) {
      requestAttributes.setAttribute(key, value, RequestAttributes.SCOPE_REQUEST);
    }
  }

  /**
   * 获取请求属性
   * @param key 属性名
   * @return 属性值
   */
  public static Object getAttribute(String key) {
    ServletRequestAttributes requestAttributes = getRequestAttributes();
    return requestAttributes != null ?
        requestAttributes.getAttribute(key, RequestAttributes.SCOPE_REQUEST) : null;
  }

  /**
   * 获取请求参数
   * @param key 属性名
   * @return 属性值
   */
  public static Object getRequestParam(String key) {
    HttpServletRequest request = getRequest();
    if (request != null) {
      return request.getParameter(key);
    }
    return null;
  }

  /**
   * 获取请求属性（带类型转换）
   * @param key   属性名
   * @param clazz 目标类型
   * @param <T>   泛型
   * @return 属性值
   */
  @SuppressWarnings("unchecked")
  public static <T> T getAttribute(String key, Class<T> clazz) {
    return (T) getAttribute(key);
  }

  /**
   * 移除请求属性
   * @param key 属性名
   */
  public static void removeAttribute(String key) {
    ServletRequestAttributes requestAttributes = getRequestAttributes();
    if (requestAttributes != null) {
      requestAttributes.removeAttribute(key, RequestAttributes.SCOPE_REQUEST);
    }
  }

  /**
   * 获取当前请求的 HttpServletRequest
   * @return HttpServletRequest
   */
  public static HttpServletRequest getRequest() {
    ServletRequestAttributes requestAttributes = getRequestAttributes();
    return requestAttributes != null ? requestAttributes.getRequest() : null;
  }

  /**
   * 获取当前请求的 HttpServletResponse
   * @return HttpServletResponse
   */
  public static HttpServletResponse getResponse() {
    ServletRequestAttributes requestAttributes = getRequestAttributes();
    return requestAttributes != null ? requestAttributes.getResponse() : null;
  }

  // 获取当前请求的 ServletRequestAttributes
  private static ServletRequestAttributes getRequestAttributes() {
    RequestAttributes attributes = RequestContextHolder.getRequestAttributes();
    return attributes instanceof ServletRequestAttributes ?
        (ServletRequestAttributes) attributes : null;
  }

}