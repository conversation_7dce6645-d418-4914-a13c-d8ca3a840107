package com.hualu.watermask.modules.cache.impl;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Expiry;
import com.hualu.watermask.modules.cache.entity.TimedValue;
import com.hualu.watermask.modules.cache.service.CacheService;
import java.time.Duration;
import org.checkerframework.checker.index.qual.NonNegative;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.stereotype.Service;
@Service
public class CaffeineCacheServiceImpl implements CacheService {
    private final Cache<String, TimedValue<Object>> cache;

    public CaffeineCacheServiceImpl() {
        this.cache = Caffeine.newBuilder()
                .maximumSize(10000) // 最大缓存项数
                .expireAfter(new Expiry<String, TimedValue<Object>>() {
                    @Override
                    public long expireAfterCreate(
                            @NonNull String key, 
                            @NonNull TimedValue<Object> value, 
                            long currentTime
                    ) {
                        // 基于包装类中的过期时间计算存活时间（纳秒）
                        return value.getRemainingTimeInNanos();
                    }

                    @Override
                    public long expireAfterUpdate(
                            @NonNull String key, 
                            @NonNull TimedValue<Object> value, 
                            long currentTime, 
                            @NonNegative long currentDuration
                    ) {
                        // 更新时重新计算过期时间
                        return value.getRemainingTimeInNanos();
                    }

                    @Override
                    public long expireAfterRead(
                            @NonNull String key, 
                            @NonNull TimedValue<Object> value, 
                            long currentTime, 
                            @NonNegative long currentDuration
                    ) {
                        // 读取时不改变过期时间
                        return currentDuration;
                    }
                })
                .build();
    }

    @Override
    public <T> T get(String key, Class<T> type) {
        TimedValue<Object> timedValue = cache.getIfPresent(key);
        if (timedValue == null || timedValue.isExpired()) {
            return null;
        }
        return type.cast(timedValue.getValue());
    }

    @Override
    public void put(String key, Object value) {
        // 默认30分钟过期
        put(key, value, Duration.ofMinutes(30));
    }

    @Override
    public void put(String key, Object value, long ttlSeconds) {
        put(key, value, Duration.ofSeconds(ttlSeconds));
    }

    private void put(String key, Object value, Duration ttl) {
        cache.put(key, new TimedValue<>(value, ttl));
    }

    @Override
    public void delete(String key) {
        cache.invalidate(key);
    }
}