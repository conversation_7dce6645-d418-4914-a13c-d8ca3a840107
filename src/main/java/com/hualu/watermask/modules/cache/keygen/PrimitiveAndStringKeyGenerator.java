package com.hualu.watermask.modules.cache.keygen;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.StringJoiner;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.util.ClassUtils;

public class PrimitiveAndStringKeyGenerator implements KeyGenerator {

  @Override
  public Object generate(Object target, Method method, Object... params) {
    StringJoiner keyJoiner = new StringJoiner("_");

    // 添加类名+方法名作为基础前缀
    keyJoiner.add(ClassUtils.getShortName(target.getClass()))
        .add(method.getName());

    // 只处理基础类型和 String 类型的参数
    Arrays.stream(params)
        .filter(this::isPrimitiveOrString)
        .map(Object::toString)
        .forEach(keyJoiner::add);

    return keyJoiner.toString();
  }

  private boolean isPrimitiveOrString(Object obj) {
    if (obj == null) return false;

    Class<?> clazz = obj.getClass();
    return clazz.isPrimitive() ||      // 基础类型
        ClassUtils.isPrimitiveWrapper(clazz) || // 包装类型
        clazz == String.class;       // String 类型
  }
}