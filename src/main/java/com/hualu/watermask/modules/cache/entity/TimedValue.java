package com.hualu.watermask.modules.cache.entity;

import java.time.Duration;
import java.time.Instant;

public class TimedValue<T> {
    private final T value;
    private final Instant expireAt; // 过期时间点

    public TimedValue(T value, Duration ttl) {
        this.value = value;
        this.expireAt = Instant.now().plus(ttl);
    }

    public T getValue() {
        return value;
    }

    public long getRemainingTimeInNanos() {
        // 计算距离过期的剩余纳秒数
        return Duration.between(Instant.now(), expireAt).toNanos();
    }

    public boolean isExpired() {
        return Instant.now().isAfter(expireAt);
    }
}