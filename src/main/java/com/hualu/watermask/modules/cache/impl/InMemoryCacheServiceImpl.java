package com.hualu.watermask.modules.cache.impl;

import com.hualu.watermask.modules.cache.service.CacheService;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Service;

/**
 * 内存缓存
 */
@Service
public class InMemoryCacheServiceImpl implements CacheService {
    private final Map<String, CacheEntry> cache = new ConcurrentHashMap<>();

  public InMemoryCacheServiceImpl() {
        // 每5分钟批量检查并清理过期缓存
    ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    scheduler.scheduleAtFixedRate(this::cleanupExpiredEntries, 5, 5, TimeUnit.MINUTES);
    }

    @Override
    public <T> T get(String key, Class<T> type) {
        CacheEntry entry = cache.get(key);
        if (entry == null || entry.isExpired()) {
            cache.remove(key);
            return null;
        }
        return type.cast(entry.getValue());
    }

    @Override
    public void put(String key, Object value) {
        put(key, value, 30 * 60); // 默认30分钟
    }

    @Override
    public void put(String key, Object value, long ttlSeconds) {
        CacheEntry entry = new CacheEntry(value, System.currentTimeMillis() + (ttlSeconds * 1000));
        cache.put(key, entry);

        // 定时删除过期缓存
        //scheduler.schedule(() -> cache.remove(key), ttlSeconds, TimeUnit.SECONDS);
    }

    @Override
    public void delete(String key) {
        cache.remove(key);
    }

    // 批量清理过期缓存
    private void cleanupExpiredEntries() {
        long now = System.currentTimeMillis();
        cache.entrySet().removeIf(entry -> entry.getValue().isExpired(now));
    }

    // 缓存项内部类
    private static class CacheEntry {
        private final Object value;
        private final long expirationTime;

        public CacheEntry(Object value, long expirationTime) {
            this.value = value;
            this.expirationTime = expirationTime;
        }

        public boolean isExpired(long now) {
            return now > expirationTime;
        }

        public boolean isExpired() {
            return System.currentTimeMillis() > expirationTime;
        }

        public Object getValue() {
            return value;
        }
    }
}