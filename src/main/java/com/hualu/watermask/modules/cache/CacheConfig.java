package com.hualu.watermask.modules.cache;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.hualu.watermask.modules.cache.keygen.PrimitiveAndStringKeyGenerator;
import java.time.Duration;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 配置 Spring 缓存管理器，启用内存缓存：
 */
@Configuration
@EnableCaching
public class CacheConfig {
  //@Bean
  //public ConcurrentMapCacheManager cacheManager() {
  //  // 使用内存缓存（ConcurrentHashMap）
  //  return new ConcurrentMapCacheManager("sa-token-permission", "sa-token-role");
  //}

  @Bean
  public CacheManager cacheManager() {
    CaffeineCacheManager cacheManager = new CaffeineCacheManager();
    cacheManager.setCaffeine(caffeineConfig());
    return cacheManager;
  }

  private Caffeine<Object, Object> caffeineConfig() {
    return Caffeine.newBuilder()
        .expireAfterWrite(Duration.ofDays(30)) // 默认30天过期
        .maximumSize(10000); // 最大缓存项数
  }

  @Bean("primitiveKeyGenerator")
  public KeyGenerator primitiveKeyGenerator() {
    return new PrimitiveAndStringKeyGenerator();
  }

}