package com.hualu.watermask.modules.cache.impl;// cache/impl/RedisCacheServiceImpl.java (未来实现)
import com.hualu.watermask.modules.cache.service.CacheService;
import org.springframework.stereotype.Service;

/**
 * redis缓存
 */
@Service
public class RedisCacheServiceImpl implements CacheService {
    //private final RedisTemplate<String, Object> redisTemplate;
    //
    //public RedisCacheServiceImpl(RedisTemplate<String, Object> redisTemplate) {
    //    this.redisTemplate = redisTemplate;
    //}

    @Override
    public <T> T get(String key, Class<T> type) {
        //return (T) redisTemplate.opsForValue().get(key);
        return null;
    }

    @Override
    public void put(String key, Object value) {
        //redisTemplate.opsForValue().set(key, value, 30, TimeUnit.MINUTES);
    }

    @Override
    public void put(String key, Object value, long ttlSeconds) {
        //redisTemplate.opsForValue().set(key, value, ttlSeconds, TimeUnit.SECONDS);
    }

    @Override
    public void delete(String key) {
        //redisTemplate.delete(key);
    }
}