package com.hualu.watermask.modules.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface WsUserTeamsMapper extends BaseMapper<WsUserTeams> {
    
    /**
     * 获取今日团队拍照人数统计
     * 
     * @param teamIds 团队ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 团队ID与今日拍照人数的映射
     */
    List<Map<String, Object>> getPhotoPersonCounts(@Param("teamIds") List<String> teamIds, 
                                                   @Param("startDate") Date startDate, 
                                                   @Param("endDate") Date endDate);
    
    /**
     * 获取今日团队拍照数量统计
     * 
     * @param params 包含teamIds、startDate、endDate的参数Map
     * @return 团队ID与今日拍照数量的映射
     */
    List<Map<String, Object>> getTeamPhotoCountsToday(Map<String, Object> params);
    
    /**
     * 获取单个团队的今日拍照人数
     * 
     * @param teamId 团队ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 今日拍照人数
     */
    Integer getTeamPhotographerCount(@Param("teamId") String teamId, 
                                     @Param("startDate") Date startDate, 
                                     @Param("endDate") Date endDate);
    
    /**
     * 获取单个团队的今日拍照数量
     * 
     * @param teamId 团队ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 今日拍照数量
     */
    Integer getTeamPhotoCount(@Param("teamId") String teamId, 
                             @Param("startDate") Date startDate, 
                             @Param("endDate") Date endDate);
}