package com.hualu.watermask.modules.user.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.role.entity.WsRole;
import com.hualu.watermask.modules.role.service.WsRoleService;
import com.hualu.watermask.modules.team.entity.WsTeam;
import com.hualu.watermask.modules.team.service.WsTeamService;
import com.hualu.watermask.modules.user.entity.WsUser;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import com.hualu.watermask.modules.user.mapper.WsUserTeamsMapper;
import com.hualu.watermask.modules.user.service.WsUserTeamsService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class WsUserTeamsServiceImpl extends ServiceImpl<WsUserTeamsMapper, WsUserTeams> implements WsUserTeamsService{

    private static final Logger log = LoggerFactory.getLogger(WsUserTeamsServiceImpl.class);

    @Autowired
    private WsTeamService wsTeamService;

    @Autowired
    private com.hualu.watermask.modules.user.service.WsUserService wsUserService;
    
    @Autowired
    private WsRoleService wsRoleService;

    @Autowired
    private WsUserTeamsService wsUserTeamsService;

    /**
     * 申请加入团队
     *
     * @param teamCode 团队编码
     * @param userId 申请用户ID
     * @return 申请结果
     */
    @Override
    @Transactional
    public RestResult<Object> applyJoinTeam(String teamCode, String userId) {
        // 通过团队编码查询团队
        LambdaQueryWrapper<WsTeam> teamQuery = new LambdaQueryWrapper<>();
        teamQuery.eq(WsTeam::getTeamCode, teamCode);
        teamQuery.eq(WsTeam::getDelFlag, new BigDecimal(0));
        WsTeam team = wsTeamService.getOne(teamQuery);
        
        if (team == null) {
            return RestResult.error("团队不存在或已被删除");
        }
        
        // 检查用户是否已经申请或已在团队中
        LambdaQueryWrapper<WsUserTeams> checkQuery = new LambdaQueryWrapper<>();
        checkQuery.eq(WsUserTeams::getUserId, userId);
        checkQuery.eq(WsUserTeams::getTeamId, team.getTeamId());
        WsUserTeams existingRelation = this.getOne(checkQuery);
        
        if (existingRelation != null) {
            if (existingRelation.getStatus().compareTo(new BigDecimal(1)) == 0) {
                return RestResult.error("您已经是该团队成员");
            } else {
                return RestResult.error("您已申请加入该团队，请等待审核");
            }
        }
        
        // 创建申请记录
        WsUserTeams userTeam = new WsUserTeams();
        userTeam.setId(UUID.randomUUID().toString().replace("-", ""));
        userTeam.setUserId(userId);
        userTeam.setTeamId(team.getTeamId());
        userTeam.setAssignedDate(new Date());
        userTeam.setAssignedBy(userId); // 设置为申请人自己的ID
        userTeam.setStatus(new BigDecimal(0)); // 待加入状态
        
        boolean result = this.save(userTeam);
        if (result) {
            return RestResult.success(null, "已成功申请加入团队，请等待审核");
        } else {
            return RestResult.error("申请加入团队失败");
        }
    }

    // @Override  // 临时移除
    public RestResult<List<WsUserTeams>> getInviteTeamList(String userId, Long current, Long size) {
        try {
            // 查询用户收到的所有待审核邀请
            LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsUserTeams::getUserId, userId);
            queryWrapper.eq(WsUserTeams::getStatus, new BigDecimal(0)); // 待审核状态
            queryWrapper.orderByDesc(WsUserTeams::getAssignedDate); // 按邀请时间倒序

            IPage<WsUserTeams> page = new Page<>(current, size);
            List<WsUserTeams> inviteList = this.page(page, queryWrapper).getRecords();

            if (inviteList == null || inviteList.isEmpty()) {
                return RestResult.success(new ArrayList<>());
            }

            // 提取所有团队ID
            List<String> teamIds = inviteList.stream()
                    .map(WsUserTeams::getTeamId)
                    .collect(Collectors.toList());

            // 批量查询团队信息
            LambdaQueryWrapper<WsTeam> teamQuery = new LambdaQueryWrapper<>();
            teamQuery.in(WsTeam::getTeamId, teamIds);
            teamQuery.eq(WsTeam::getDelFlag, new BigDecimal(0));
            List<WsTeam> teams = wsTeamService.list(teamQuery);

            // 创建团队ID到团队信息的映射
            Map<String, WsTeam> teamMap = new HashMap<>();
            for (WsTeam team : teams) {
                teamMap.put(team.getTeamId(), team);
            }

            // 查询角色信息
            Set<String> roleIds = inviteList.stream()
                    .map(WsUserTeams::getRoleId)
                    .filter(roleId -> roleId != null && !roleId.isEmpty())
                    .collect(Collectors.toSet());

            Map<String, String> roleMap = new HashMap<>();
            if (!roleIds.isEmpty()) {
                LambdaQueryWrapper<WsRole> roleQuery = new LambdaQueryWrapper<>();
                roleQuery.in(WsRole::getRoleId, roleIds);
                List<WsRole> roles = wsRoleService.list(roleQuery);

                for (WsRole role : roles) {
                    roleMap.put(role.getRoleId(), role.getRoleName());
                }
            }

            // 为邀请记录设置团队相关信息
            for (WsUserTeams invite : inviteList) {
                WsTeam team = teamMap.get(invite.getTeamId());
                if (team != null) {
                    // 设置团队基本信息
                    invite.setTeamName(team.getTeamName());
                    invite.setTeamCode(team.getTeamCode());
                    invite.setTeamDescription(team.getTeamDescription());
                    invite.setParentTeamId(team.getParentTeamId());

                    // 获取顶级团队名称
                    String topLevelTeamId = wsTeamService.getTopLevelTeamId(team.getTeamId());
                    WsTeam topTeam = wsTeamService.getById(topLevelTeamId);
                    String topTeamName = (topTeam != null) ? topTeam.getTeamName() : "";
                    invite.setTopTeamName(topTeamName);

                    // 设置邀请的角色名称
                    String roleName = roleMap.getOrDefault(invite.getRoleId(), "待分配");
                    invite.setRoleName(roleName);

                    // 设置name字段为团队名称（保持兼容性）
                    invite.setName(team.getTeamName());
                }
            }

            return RestResult.success(inviteList);

        } catch (Exception e) {
            log.error("获取邀请团队列表失败", e);
            return RestResult.error("获取邀请团队列表失败：" + e.getMessage());
        }
    }

    /**
     * 获取待审核的团队申请列表
     *
     * @param teamId 当前团队ID
     * @param current 当前页
     * @param size 页大小
     * @return 待审核申请列表（包含用户名称、角色名称、团队名称）
     */
    @Override
    public RestResult<List<WsUserTeams>> getPendingApplications(String teamId, Long current, Long size) {
        try {
            // 查询指定团队的所有待审核申请
            LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsUserTeams::getTeamId, teamId);
            queryWrapper.eq(WsUserTeams::getStatus, new BigDecimal(0)); // 待审核状态
            queryWrapper.orderByDesc(WsUserTeams::getAssignedDate); // 按申请时间倒序

            IPage<WsUserTeams> page = new Page<>(current, size);
            List<WsUserTeams> pendingList = this.page(page, queryWrapper).getRecords();

            if (pendingList == null || pendingList.isEmpty()) {
                return RestResult.success(new ArrayList<>());
            }

            // 提取所有用户ID
            List<String> userIds = pendingList.stream()
                    .map(WsUserTeams::getUserId)
                    .collect(Collectors.toList());

            // 批量查询用户信息
            LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.in(WsUser::getUserId, userIds);
            userQuery.eq(WsUser::getDelFlag, new BigDecimal(0));
            List<WsUser> users = wsUserService.list(userQuery);

            // 创建用户ID到用户信息的映射
            Map<String, WsUser> userMap = new HashMap<>();
            for (WsUser user : users) {
                userMap.put(user.getUserId(), user);
            }

            // 查询团队信息
            WsTeam team = wsTeamService.getById(teamId);
            String teamName = team != null ? team.getTeamName() : "";

            // 查询角色信息
            Set<String> roleIds = pendingList.stream()
                    .map(WsUserTeams::getRoleId)
                    .filter(roleId -> roleId != null && !roleId.isEmpty())
                    .collect(Collectors.toSet());

            Map<String, String> roleMap = new HashMap<>();
            if (!roleIds.isEmpty()) {
                LambdaQueryWrapper<WsRole> roleQuery = new LambdaQueryWrapper<>();
                roleQuery.in(WsRole::getRoleId, roleIds);
                List<WsRole> roles = wsRoleService.list(roleQuery);

                for (WsRole role : roles) {
                    roleMap.put(role.getRoleId(), role.getRoleName());
                }
            }

            // 为WsUserTeams设置用户名称、角色名称和团队名称
            List<WsUserTeams> result = new ArrayList<>();
            for (WsUserTeams userTeam : pendingList) {
                // 设置用户名称
                WsUser user = userMap.get(userTeam.getUserId());
                if (user != null) {
                    userTeam.setUsername(user.getUsername());
                }

                // 设置角色名称
                String roleName = roleMap.getOrDefault(userTeam.getRoleId(), "普通成员");
                userTeam.setRoleName(roleName);

                // 设置团队名称
                userTeam.setTeamName(teamName);

                result.add(userTeam);
            }

            return RestResult.success(result);

        } catch (Exception e) {
            log.error("获取待审核团队申请列表失败", e);
            return RestResult.error("获取待审核申请列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 审核团队申请
     *
     * @param id 用户团队关联ID
     * @param approved 是否批准
     * @param operatorId 操作人ID
     * @return 审核结果
     */
    @Override
    @Transactional
    public RestResult<Object> reviewApplication(String id, Integer approved, String operatorId) {
        WsUserTeams userTeam = this.getById(id);
        if (userTeam == null) {
            return RestResult.error("申请记录不存在");
        }
        
        // 检查操作人是否有权限审核（应该是团队的管理员）
        // 这里简化处理，实际应该检查操作人在该团队中的角色
        
        if (approved == 1) {
            // 同意申请，更新状态为已加入
            userTeam.setStatus(new BigDecimal(1));
            userTeam.setAssignedBy(operatorId);
            boolean result = this.updateById(userTeam);
            if (result) {
                return RestResult.success(null, "已同意申请");
            } else {
                return RestResult.error("操作失败");
            }
        } else {
            // 不同意申请，删除记录
            boolean result = this.removeById(id);
            if (result) {
                return RestResult.success(null, "已拒绝申请");
            } else {
                return RestResult.error("操作失败");
            }
        }
    }
    
    /**
     * 获取用户所属的所有团队
     *
     * @param userId 用户ID
     * @return 团队列表
     */
    @Override
    public RestResult<List<WsUserTeams>> getUserTeams(String userId) {
        LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsUserTeams::getUserId, userId);
        queryWrapper.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 只获取已加入的团队
        
        List<WsUserTeams> teams = this.list(queryWrapper);
        return RestResult.success(teams);
    }
    
    /**
     * 获取团队的所有成员
     *
     * @param teamId 团队ID
     * @return 成员列表
     */
    @Override
    public RestResult<List<WsUserTeams>> getTeamMembers(String teamId) {
        LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsUserTeams::getTeamId, teamId);
        queryWrapper.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 只获取已加入的成员
        
        List<WsUserTeams> members = this.list(queryWrapper);
        return RestResult.success(members);
    }
    
    /**
     * 获取团队的所有成员用户信息
     *
     * @param teamId 团队ID
     * @param isAdmin 是否为管理员（1:是管理员, 0:非管理员, null:所有成员）
     * @return 成员用户列表
     */
    @Override
    public RestResult<List<WsUser>> getTeamMemberUsers(String teamId, Integer isAdmin) {
        try {
            // 首先获取所有角色，构建角色ID和角色代码映射
            List<WsRole> allRoles = wsRoleService.list();
            Map<String, WsRole> roleMap = new HashMap<>(allRoles.size() * 2);
            
            // 同时构建角色ID和角色代码的映射，提高后续查询效率
            for (WsRole role : allRoles) {
                roleMap.put(role.getRoleId(), role);
                roleMap.put(role.getRoleCode(), role);
            }
            
            // 构建查询条件
            LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsUserTeams::getTeamId, teamId);
            queryWrapper.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 只获取已加入的成员
            
            // 如果需要过滤管理员/非管理员
            if (isAdmin != null) {
                // 获取所有角色信息，确定哪些角色是管理员角色
                List<String> adminRoleCodes = Arrays.asList("admin", "mainAdmin"); // 管理员角色代码
                List<String> nonAdminRoleCodes = Arrays.asList("workers", "intern"); // 非管理员角色代码
                
                List<String> targetRoleIds = new ArrayList<>();
                
                // 根据isAdmin参数确定目标角色代码
                List<String> targetRoleCodes = (isAdmin == 1) ? adminRoleCodes : nonAdminRoleCodes;
                
                // 收集符合条件的角色ID
                for (WsRole role : allRoles) {
                    if (targetRoleCodes.contains(role.getRoleCode())) {
                        targetRoleIds.add(role.getRoleId());
                    }
                }
                
                if (!targetRoleIds.isEmpty()) {
                    queryWrapper.in(WsUserTeams::getRoleId, targetRoleIds);
                } else {
                    // 如果没有找到符合条件的角色，则返回空列表
                    return RestResult.success(new ArrayList<>());
                }
            }
            
            // 一次性查询所有符合条件的用户团队关联
            List<WsUserTeams> members = this.list(queryWrapper);
            
            if (members == null || members.isEmpty()) {
                return RestResult.success(new ArrayList<>());
            }
            
            // 提取所有用户ID
            List<String> userIds = members.stream()
                    .map(WsUserTeams::getUserId)
                    .collect(Collectors.toList());
            
            // 查询所有用户信息
            LambdaQueryWrapper<com.hualu.watermask.modules.user.entity.WsUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.in(com.hualu.watermask.modules.user.entity.WsUser::getUserId, userIds);
            userQuery.eq(com.hualu.watermask.modules.user.entity.WsUser::getDelFlag, new BigDecimal(0)); // 只查询未删除的用户
            
            List<com.hualu.watermask.modules.user.entity.WsUser> users = wsUserService.list(userQuery);
            
            // 创建用户ID到用户对象的映射，方便后续查找
            Map<String, com.hualu.watermask.modules.user.entity.WsUser> userMap = new HashMap<>(users.size());
            for (com.hualu.watermask.modules.user.entity.WsUser user : users) {
                userMap.put(user.getUserId(), user);
            }
            
            // 获取团队信息，用于设置dept字段
            com.hualu.watermask.modules.team.entity.WsTeam team = wsTeamService.getById(teamId);
            String teamName = team != null ? team.getTeamName() : "";
            
            // 设置角色名称和部门（团队名称）
            for (WsUserTeams member : members) {
                com.hualu.watermask.modules.user.entity.WsUser user = userMap.get(member.getUserId());
                if (user != null) {
                    String roleId = member.getRoleId();
                    // 默认type为空字符串
                    user.setType("");
                    // 默认设置dept为团队名称
                    user.setDept(teamName);

                    // 如果角色ID存在，设置角色名称
                    if (roleId != null && !roleId.isEmpty()) {
                        WsRole role = roleMap.get(roleId);
                        if (role != null) {
                            // 设置角色名称到用户的type字段
                            user.setType(role.getRoleName());
                        }
                    }
                }
            }
            
            return RestResult.success(users);
        } catch (Exception e) {
            log.error("获取团队成员用户信息失败", e);
            return RestResult.error("获取团队成员用户信息失败：" + e.getMessage());
        }
    }
    
    /**
     * 给用户授权角色
     *
     * @param teamId 团队ID
     * @param targetUserId 目标用户ID
     * @param roleId 角色ID
     * @param operatorId 操作人ID
     * @return 授权结果
     */
    @Override
    @Transactional
    public RestResult<Object> assignUserRole(String teamId, String targetUserId, String roleId, String operatorId) {
        // 检查团队是否存在
        WsTeam team = wsTeamService.getById(teamId);
        if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
            return RestResult.error("团队不存在或已被删除");
        }
        
        // 检查操作人权限（根据团队层级判断授权权限）
        LambdaQueryWrapper<WsUserTeams> operatorQuery = new LambdaQueryWrapper<>();
        operatorQuery.eq(WsUserTeams::getTeamId, teamId);
        operatorQuery.eq(WsUserTeams::getUserId, operatorId);
        operatorQuery.eq(WsUserTeams::getStatus, new BigDecimal(1));
        WsUserTeams operatorRelation = this.getOne(operatorQuery);

        if (operatorRelation == null) {
            return RestResult.error("您不是该团队成员，无权限进行角色授权");
        }

        // 获取操作人的角色
        String operatorRoleCode = wsTeamService.getUserRoleInTeam(operatorId, teamId);

        // 判断团队是否为顶级团队
        boolean isTopLevelTeam = (team.getParentTeamId() == null || team.getParentTeamId().isEmpty());

        if (isTopLevelTeam) {
            // 顶级团队：只有主管理员可以授权角色
            if (!"mainAdmin".equals(operatorRoleCode)) {
                return RestResult.error("顶级团队只有主管理员可以授权角色");
            }
        } else {
            // 非顶级团队：管理员和主管理员都可以授权角色
            if (!"admin".equals(operatorRoleCode) && !"mainAdmin".equals(operatorRoleCode)) {
                return RestResult.error("只有管理员或主管理员可以授权角色");
            }
        }
        
        // 检查目标用户是否为团队成员
        LambdaQueryWrapper<WsUserTeams> targetQuery = new LambdaQueryWrapper<>();
        targetQuery.eq(WsUserTeams::getTeamId, teamId);
        targetQuery.eq(WsUserTeams::getUserId, targetUserId);
        targetQuery.eq(WsUserTeams::getStatus, new BigDecimal(1));
        WsUserTeams targetRelation = this.getOne(targetQuery);
        
        if (targetRelation == null) {
            return RestResult.error("目标用户不是该团队成员");
        }
        
        // 更新用户角色
        targetRelation.setRoleId(roleId);
        targetRelation.setAssignedBy(operatorId);
        targetRelation.setAssignedDate(new Date());
        
        boolean result = this.updateById(targetRelation);
        if (result) {
            return RestResult.success(null, "角色授权成功");
        } else {
            return RestResult.error("角色授权失败");
        }
    }
    
    @Override
    @Transactional
    public RestResult<Map<String, Integer>> assignUserRoleBatch(String teamId, String targetUserIds, String roleId, String operatorId) {
        try {
            // 检查团队是否存在
            WsTeam team = wsTeamService.getById(teamId);
            if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                return RestResult.error("团队不存在或已被删除");
            }
            
            // 检查操作人权限（根据团队层级判断授权权限）
            LambdaQueryWrapper<WsUserTeams> operatorQuery = new LambdaQueryWrapper<>();
            operatorQuery.eq(WsUserTeams::getTeamId, teamId);
            operatorQuery.eq(WsUserTeams::getUserId, operatorId);
            operatorQuery.eq(WsUserTeams::getStatus, new BigDecimal(1));
            WsUserTeams operatorRelation = this.getOne(operatorQuery);

            if (operatorRelation == null) {
                return RestResult.error("您不是该团队成员，无权限进行角色授权");
            }

            // 获取操作人的角色
            String operatorRoleCode = wsTeamService.getUserRoleInTeam(operatorId, teamId);

            // 判断团队是否为顶级团队
            boolean isTopLevelTeam = (team.getParentTeamId() == null || team.getParentTeamId().isEmpty());

            if (isTopLevelTeam) {
                // 顶级团队：只有主管理员可以授权角色
                if (!"mainAdmin".equals(operatorRoleCode)) {
                    return RestResult.error("顶级团队只有主管理员可以批量授权角色");
                }
            } else {
                // 非顶级团队：管理员和主管理员都可以授权角色
                if (!"admin".equals(operatorRoleCode) && !"mainAdmin".equals(operatorRoleCode)) {
                    return RestResult.error("只有管理员或主管理员可以批量授权角色");
                }
            }
            
            // 解析用户ID列表
            if (StringUtils.isBlank(targetUserIds)) {
                return RestResult.error("目标用户ID不能为空");
            }
            
            String[] userIdArray = targetUserIds.split(",");
            List<String> userIds = Arrays.stream(userIdArray)
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());
                    
            if (userIds.isEmpty()) {
                return RestResult.error("目标用户ID不能为空");
            }
            
            // 查询团队中的用户关系
            LambdaQueryWrapper<WsUserTeams> targetQuery = new LambdaQueryWrapper<>();
            targetQuery.eq(WsUserTeams::getTeamId, teamId);
            targetQuery.in(WsUserTeams::getUserId, userIds);
            targetQuery.eq(WsUserTeams::getStatus, new BigDecimal(1));
            List<WsUserTeams> targetRelations = this.list(targetQuery);
            
            // 统计结果
            int successCount = 0;
            int failCount = 0;
            
            // 批量更新
            Date now = new Date();
            List<WsUserTeams> updateList = new ArrayList<>();
            
            for (WsUserTeams relation : targetRelations) {
                // 更新用户角色
                relation.setRoleId(roleId);
                relation.setAssignedBy(operatorId);
                relation.setAssignedDate(now);
                updateList.add(relation);
            }
            
            // 执行批量更新
            if (!updateList.isEmpty()) {
                boolean result = this.updateBatchById(updateList);
                if (result) {
                    successCount = updateList.size();
                } else {
                    failCount = updateList.size();
                }
            }
            
            // 检查是否有用户不是团队成员
            failCount += (userIds.size() - targetRelations.size());
            
            // 返回结果
            Map<String, Integer> resultMap = new HashMap<>(2);
            resultMap.put("successCount", successCount);
            resultMap.put("failCount", failCount);
            
            return RestResult.success(resultMap, "批量角色授权完成");
            
        } catch (Exception e) {
            log.error("批量角色授权失败", e);
            return RestResult.error("批量角色授权失败: " + e.getMessage());
        }
    }
    
    /**
     * 主管理员转让（与转让者互换角色）
     *
     * @param teamId 团队ID
     * @param targetUserId 目标用户ID
     * @param operatorId 操作人ID（主管理员）
     * @return 转让结果
     */
    @Override
    @Transactional
    public RestResult<Object> transferMainAdmin(String teamId, String targetUserId, String operatorId) {
        // 检查团队是否存在
        WsTeam team = wsTeamService.getById(teamId);
        if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
            return RestResult.error("团队不存在或已被删除");
        }
        
        // 检查操作人是否为主管理员
        LambdaQueryWrapper<WsUserTeams> operatorQuery = new LambdaQueryWrapper<>();
        operatorQuery.eq(WsUserTeams::getTeamId, teamId);
        operatorQuery.eq(WsUserTeams::getUserId, operatorId);
        operatorQuery.eq(WsUserTeams::getStatus, new BigDecimal(1));
        WsUserTeams operatorRelation = this.getOne(operatorQuery);
        
        if (operatorRelation == null || !"mainAdmin".equals(operatorRelation.getRoleId())) {
            return RestResult.error("只有主管理员可以转让权限");
        }
        
        // 检查目标用户是否为团队成员
        LambdaQueryWrapper<WsUserTeams> targetQuery = new LambdaQueryWrapper<>();
        targetQuery.eq(WsUserTeams::getTeamId, teamId);
        targetQuery.eq(WsUserTeams::getUserId, targetUserId);
        targetQuery.eq(WsUserTeams::getStatus, new BigDecimal(1));
        WsUserTeams targetRelation = this.getOne(targetQuery);
        
        if (targetRelation == null) {
            return RestResult.error("目标用户不是该团队成员");
        }
        
        // 不能转让给自己
        if (operatorId.equals(targetUserId)) {
            return RestResult.error("不能转让给自己");
        }
        
        // 互换角色
        String operatorRoleId = operatorRelation.getRoleId();
        String targetRoleId = targetRelation.getRoleId();
        
        operatorRelation.setRoleId(targetRoleId);
        operatorRelation.setAssignedBy(operatorId);
        operatorRelation.setAssignedDate(new Date());
        
        targetRelation.setRoleId(operatorRoleId);
        targetRelation.setAssignedBy(operatorId);
        targetRelation.setAssignedDate(new Date());
        
        // 批量更新
        boolean result1 = this.updateById(operatorRelation);
        boolean result2 = this.updateById(targetRelation);
        
        if (result1 && result2) {
            return RestResult.success(null, "主管理员转让成功");
        } else {
            return RestResult.error("主管理员转让失败");
        }
    }

    /**
     * 根据团队ID获取所有用户ID
     *
     * @param teamId 团队ID
     * @return 用户ID列表
     */
    @Override
    public List<String> getUserIdsByTeamId(String teamId) {
        LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsUserTeams::getTeamId, teamId)
                  .eq(WsUserTeams::getStatus, new BigDecimal(1)); // 只获取已批准的成员
        
        return this.list(queryWrapper).stream()
                .map(WsUserTeams::getUserId)
                .collect(Collectors.toList());
    }

    /**
     * 获取用户所属的所有团队
     *
     * @param userId 用户ID
     * @param flag 是否返回今日统计数据（1:返回统计数据，0或null:不返回）
     * @return 团队列表
     */
    @Override
    public RestResult<List<WsTeam>> getUserTeamList(String userId, Integer flag) {
        try {
            if (userId == null || userId.isEmpty()) {
                return RestResult.error("用户ID不能为空");
            }
            
            // 查询用户-团队关联
            LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsUserTeams::getUserId, userId);
            queryWrapper.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 只获取状态为1的记录
            
            List<WsUserTeams> userTeams = this.list(queryWrapper);
            
            if (userTeams == null || userTeams.isEmpty()) {
                return RestResult.success(new ArrayList<>());
            }
            
            // 提取团队ID列表
            List<String> teamIds = userTeams.stream()
                    .map(WsUserTeams::getTeamId)
                    .collect(Collectors.toList());
            
            // 创建团队ID到isSyn值的映射
            Map<String, BigDecimal> teamSyncMap = new HashMap<>();
            for (WsUserTeams userTeam : userTeams) {
                teamSyncMap.put(userTeam.getTeamId(), userTeam.getIsSyn());
            }
            
            // 查询团队详细信息
            LambdaQueryWrapper<WsTeam> teamQuery = new LambdaQueryWrapper<>();
            teamQuery.in(WsTeam::getTeamId, teamIds);
            teamQuery.eq(WsTeam::getDelFlag, new BigDecimal(0)); // 只查询未删除的团队
            teamQuery.orderByAsc(WsTeam::getTeamName); // 按团队名称排序
            
            List<WsTeam> teams = wsTeamService.list(teamQuery);
            
            // 设置isSyn值并添加新的字段信息
            for (WsTeam team : teams) {
                BigDecimal isSyn = teamSyncMap.get(team.getTeamId());
                team.setIsSyn(isSyn != null ? isSyn : new BigDecimal(0));

                // 获取顶级团队名称
                String topLevelTeamId = wsTeamService.getTopLevelTeamId(team.getTeamId());
                WsTeam topTeam = wsTeamService.getById(topLevelTeamId);
                String topTeamName = (topTeam != null) ? topTeam.getTeamName() : "";
                team.setTopTeamName(topTeamName);

                // 设置当前团队ID（用户所在的团队ID）
                team.setCurrentTeamId(team.getTeamId());

                // 获取用户在该团队的角色名称
                String userRoleCode = wsTeamService.getUserRoleInTeamForNull(userId, team.getTeamId());
                String roleName = getRoleNameByCode(userRoleCode);
                team.setRoleName(roleName);
            }
            
            // 如果flag为1，则查询今日拍照统计数据
            if (flag != null && flag == 1 && !teams.isEmpty()) {
                // 获取今天的开始时间（00:00:00）和结束时间（23:59:59）
                Calendar calendar = Calendar.getInstance();
                calendar.set(Calendar.HOUR_OF_DAY, 0);
                calendar.set(Calendar.MINUTE, 0);
                calendar.set(Calendar.SECOND, 0);
                calendar.set(Calendar.MILLISECOND, 0);
                Date startDate = calendar.getTime();

                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);
                Date endDate = calendar.getTime();

                // 为每个团队设置统计数据（包含子团队）
                for (WsTeam team : teams) {
                    String teamId = team.getTeamId();

                    // 获取当前团队及其所有子团队的ID列表
                    List<String> allSubTeamIds = wsTeamService.getAllTeamIdsInTree(teamId);

                    // 统计当前团队及其所有子团队的今日拍照人数
                    Map<String, Integer> teamPersonCountMap = batchGetTeamPhotoPersonCount(allSubTeamIds, startDate, endDate);
                    Integer todayPersonCount = teamPersonCountMap.values().stream().mapToInt(Integer::intValue).sum();
                    team.setTotalPerson(todayPersonCount);

                    // 统计当前团队及其所有子团队的今日拍照数量
                    Map<String, Integer> teamPhotoCountMap = batchGetTeamPhotoCount(allSubTeamIds, startDate, endDate);
                    Integer todayPhotoCount = teamPhotoCountMap.values().stream().mapToInt(Integer::intValue).sum();
                    team.setTotalPhoto(todayPhotoCount);
                }
            } else {
                // 不需要统计数据时，设置为null或0
                for (WsTeam team : teams) {
                    team.setTotalPerson(null);
                    team.setTotalPhoto(null);
                }
            }
            
            return RestResult.success(teams);
        } catch (Exception e) {
            log.error("获取用户团队列表失败", e);
            return RestResult.error("获取用户团队列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 批量获取今日团队的拍照人数
     *
     * @param teamIds 团队ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 团队ID到今日拍照人数的映射
     */
    private Map<String, Integer> batchGetTeamPhotoPersonCount(List<String> teamIds, Date startDate, Date endDate) {
        Map<String, Integer> result = new HashMap<>();
        if (teamIds == null || teamIds.isEmpty()) {
            return result;
        }

        try {
            // 使用Mapper中定义的批量查询方法
            List<Map<String, Object>> batchResult = baseMapper.getPhotoPersonCounts(teamIds, startDate, endDate);

            if (batchResult != null) {
                for (Map<String, Object> row : batchResult) {
                    String teamId = row.get("TEAM_ID").toString();
                    Object countObj = row.get("CNT");
                    Integer count = 0;
                    if (countObj != null) {
                        // 处理数据库返回的数字类型，可能是BigDecimal或其他类型
                        if (countObj instanceof BigDecimal) {
                            count = ((BigDecimal) countObj).intValue();
                        } else {
                            count = Integer.valueOf(countObj.toString());
                        }
                    }
                    result.put(teamId, count);
                }
            }
        } catch (Exception e) {
            log.error("批量获取今日团队拍照人数失败", e);
            
            // 失败时，尝试逐个查询（备用方案）
            for (String teamId : teamIds) {
                try {
                    // 使用Mapper中定义的单个查询方法
                    Integer count = baseMapper.getTeamPhotographerCount(teamId, startDate, endDate);
                    result.put(teamId, count != null ? count : 0);
                } catch (Exception ex) {
                    log.error("获取团队" + teamId + "拍照人数失败", ex);
                    result.put(teamId, 0);
                }
            }
        }

        // 确保所有团队ID都有对应的结果
        for (String teamId : teamIds) {
            if (!result.containsKey(teamId)) {
                result.put(teamId, 0);
            }
        }

        return result;
    }

    /**
     * 批量获取今日团队的拍照数量
     *
     * @param teamIds 团队ID列表
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 团队ID到今日拍照数量的映射
     */
    private Map<String, Integer> batchGetTeamPhotoCount(List<String> teamIds, Date startDate, Date endDate) {
        Map<String, Integer> result = new HashMap<>();
        if (teamIds == null || teamIds.isEmpty()) {
            return result;
        }

        try {
            // 准备批量参数查询
            Map<String, Object> params = new HashMap<>();
            params.put("teamIds", teamIds);
            params.put("startDate", startDate);
            params.put("endDate", endDate);
            
            // 使用XML中定义的SQL，避免在Java中拼接SQL
            List<Map<String, Object>> batchResult = baseMapper.getTeamPhotoCountsToday(params);
            
            if (batchResult != null) {
                for (Map<String, Object> row : batchResult) {
                    String teamId = row.get("TEAM_ID").toString();
                    Object countObj = row.get("CNT");
                    Integer count = 0;
                    if (countObj != null) {
                        // 处理数据库返回的数字类型，可能是BigDecimal或其他类型
                        if (countObj instanceof BigDecimal) {
                            count = ((BigDecimal) countObj).intValue();
                        } else {
                            count = Integer.valueOf(countObj.toString());
                        }
                    }
                    result.put(teamId, count);
                }
            }
        } catch (Exception e) {
            log.error("批量获取今日团队拍照数量失败", e);
            
            // 失败时，尝试逐个查询（备用方案）
            for (String teamId : teamIds) {
                try {
                    // 使用Mapper中定义的单个查询方法
                    Integer count = baseMapper.getTeamPhotoCount(teamId, startDate, endDate);
                    result.put(teamId, count != null ? count : 0);
                } catch (Exception ex) {
                    log.error("获取团队" + teamId + "拍照数量失败", ex);
                    result.put(teamId, 0);
                }
            }
        }

        // 确保所有团队ID都有对应的结果
        for (String teamId : teamIds) {
            if (!result.containsKey(teamId)) {
                result.put(teamId, 0);
            }
        }

        return result;
    }
    
    /**
     * 获取用户所属的需要同步的团队
     *
     * @param userId 用户ID
     * @return 需要同步的团队列表
     */
    @Override
    public List<WsUserTeams> getSyncTeams(String userId) {
        try {
            // 查询用户所属的需要同步的团队
            LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsUserTeams::getUserId, userId);
            queryWrapper.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            queryWrapper.eq(WsUserTeams::getIsSyn, new BigDecimal(1)); // 已设置同步
            
            return this.list(queryWrapper);
        } catch (Exception e) {
            log.error("获取用户同步团队失败", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 更新用户团队的同步设置
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param isSyn 是否同步（1:是 0:否）
     * @return 更新结果
     */
    @Override
    @Transactional
    public RestResult<Object> updateSyncSetting(String userId, String teamId, Integer isSyn) {
        try {
            // 验证同步设置值
            if (isSyn != 0 && isSyn != 1) {
                return RestResult.error("同步设置参数无效，必须为0或1");
            }
            
            // 查找用户与团队的关联记录
            LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsUserTeams::getUserId, userId);
            queryWrapper.eq(WsUserTeams::getTeamId, teamId);
            queryWrapper.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            
            WsUserTeams userTeam = this.getOne(queryWrapper);
            
            if (userTeam == null) {
                return RestResult.error("用户不是该团队成员或未成功加入团队");
            }
            
            // 更新同步设置
            LambdaUpdateWrapper<WsUserTeams> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(WsUserTeams::getId, userTeam.getId());
            updateWrapper.set(WsUserTeams::getIsSyn, new BigDecimal(isSyn));
            
            boolean result = this.update(updateWrapper);
            
            if (result) {
                return RestResult.success(null, "同步设置更新成功");
            } else {
                return RestResult.error("同步设置更新失败");
            }
        } catch (Exception e) {
            log.error("更新同步设置失败", e);
            return RestResult.error("更新同步设置失败: " + e.getMessage());
        }
    }

    /**
     * 获取团队成员树形结构
     *
     * @param teamId 团队ID
     * @param isAdmin 是否为管理员（1:是管理员, 0:非管理员, null:所有成员）
     * @param keyword 用户名关键字（模糊搜索）
     * @return 树形结构的团队和成员
     */
    @Override
    public RestResult<List<Map<String, Object>>> getTeamMembersTree(String teamId, Integer isAdmin, String keyword) {
        try {
            // 首先获取所有角色，构建角色ID和角色代码映射
            List<WsRole> allRoles = wsRoleService.list();
            Map<String, WsRole> roleMap = new HashMap<>(allRoles.size() * 2);
            
            // 同时构建角色ID和角色代码的映射，提高后续查询效率
            for (WsRole role : allRoles) {
                roleMap.put(role.getRoleId(), role);
                roleMap.put(role.getRoleCode(), role);
            }
            
            // 构建查询条件
            LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsUserTeams::getTeamId, teamId);
            queryWrapper.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 只获取已加入的成员
            
            // 如果需要过滤管理员/非管理员
            if (isAdmin != null) {
                // 获取所有角色信息，确定哪些角色是管理员角色
                List<String> adminRoleCodes = Arrays.asList("admin", "mainAdmin"); // 管理员角色代码
                List<String> nonAdminRoleCodes = Arrays.asList("workers", "intern"); // 非管理员角色代码
                
                List<String> targetRoleIds = new ArrayList<>();
                
                // 根据isAdmin参数确定目标角色代码
                List<String> targetRoleCodes = (isAdmin == 1) ? adminRoleCodes : nonAdminRoleCodes;
                
                // 收集符合条件的角色ID
                for (WsRole role : allRoles) {
                    if (targetRoleCodes.contains(role.getRoleCode())) {
                        targetRoleIds.add(role.getRoleId());
                    }
                }
                
                if (!targetRoleIds.isEmpty()) {
                    queryWrapper.in(WsUserTeams::getRoleId, targetRoleIds);
                } else {
                    // 如果没有找到符合条件的角色，则返回空列表
                    return RestResult.success(new ArrayList<>());
                }
            }
            
            // 一次性查询所有符合条件的用户团队关联
            List<WsUserTeams> members = this.list(queryWrapper);
            
            if (members == null || members.isEmpty()) {
                return RestResult.success(new ArrayList<>());
            }
            
            // 提取所有用户ID
            List<String> userIds = members.stream()
                    .map(WsUserTeams::getUserId)
                    .collect(Collectors.toList());
            
            // 查询所有用户信息
            LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.in(WsUser::getUserId, userIds);
            userQuery.eq(WsUser::getDelFlag, new BigDecimal(0)); // 只查询未删除的用户
            
            // 如果有关键字，添加模糊搜索条件
            if (keyword != null && !keyword.trim().isEmpty()) {
                userQuery.like(WsUser::getUsername, keyword.trim());
            }
            
            List<WsUser> users = wsUserService.list(userQuery);
            
            // 如果没有用户，返回空列表
            if (users.isEmpty()) {
                return RestResult.success(new ArrayList<>());
            }
            
            // 获取团队信息
            WsTeam currentTeam = wsTeamService.getById(teamId);
            if (currentTeam == null) {
                return RestResult.error("团队不存在");
            }
            
            // 查询子团队
            LambdaQueryWrapper<WsTeam> teamQuery = new LambdaQueryWrapper<>();
            teamQuery.eq(WsTeam::getParentTeamId, teamId);
            teamQuery.eq(WsTeam::getDelFlag, new BigDecimal(0));
            List<WsTeam> childTeams = wsTeamService.list(teamQuery);
            
            // 构建树形结构
            List<Map<String, Object>> result = new ArrayList<>();
            
            // 当前团队节点
            Map<String, Object> teamNode = new HashMap<>();
            teamNode.put("label", currentTeam.getTeamName() + "(" + users.size() + ")");
            teamNode.put("value", currentTeam.getTeamId());
            
            // 如果有子团队，递归构建子团队
            if (!childTeams.isEmpty()) {
                List<Map<String, Object>> childrenTeams = new ArrayList<>();
                for (WsTeam childTeam : childTeams) {
                    // 递归调用获取子团队成员
                    RestResult<List<Map<String, Object>>> childResult = getTeamMembersTree(childTeam.getTeamId(), isAdmin, keyword);
                    if (childResult.getCode() == RestResult.SUCCESS && childResult.getData() != null && !childResult.getData().isEmpty()) {
                        childrenTeams.addAll(childResult.getData());
                    }
                }
                
                if (!childrenTeams.isEmpty()) {
                    teamNode.put("children", childrenTeams);
                }
            }
            
            // 添加用户节点
            List<Map<String, Object>> userNodes = new ArrayList<>();
            for (WsUser user : users) {
                Map<String, Object> userNode = new HashMap<>();
                userNode.put("label", user.getUsername());
                userNode.put("value", user.getUserId());
                userNode.put("avatar", user.getAvatar() != null ? user.getAvatar() : "");
                userNodes.add(userNode);
            }
            
            // 如果有用户节点，添加到当前团队的children中
            if (!userNodes.isEmpty()) {
                if (teamNode.containsKey("children")) {
                    // 如果已经有子团队，将用户节点添加到子团队列表中
                    ((List<Map<String, Object>>) teamNode.get("children")).addAll(userNodes);
                } else {
                    // 如果没有子团队，直接设置用户节点为children
                    teamNode.put("children", userNodes);
                }
            }
            
            result.add(teamNode);
            return RestResult.success(result);
        } catch (Exception e) {
            log.error("获取团队成员树形结构失败", e);
            return RestResult.error("获取团队成员树形结构失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取常搜人列表
     *
     * @return 常搜人列表
     */
    @Override
    public RestResult<List<String>> getFrequentSearchUsers(String teamId) {
        try {
            // 这里应该是从数据库中查询常搜人列表
            // 由于没有相关表结构，这里模拟返回一些常用人名
            LambdaQueryWrapper<WsUserTeams> query = new LambdaQueryWrapper<>();
            query.eq(WsUserTeams::getStatus, new BigDecimal(1));
            query.eq(WsUserTeams::getTeamId, teamId);
            List<WsUserTeams> userTeams = wsUserTeamsService.list(query);
            List<String> userIds = userTeams.stream().map(WsUserTeams::getUserId).collect(Collectors.toList());
            List<String> limit = userIds.stream().limit(4).collect(Collectors.toList());
            LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.in(WsUser::getUserId, limit);
            List<WsUser> list = wsUserService.list(userQuery);
            List<String> frequentUsers = list.stream().map(WsUser::getUsername).collect(Collectors.toList());
            return RestResult.success(frequentUsers);
        } catch (Exception e) {
            log.error("获取常搜人列表失败", e);
            return RestResult.error("获取常搜人列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据类型获取团队成员列表（管理员或非管理员）
     *
     * @param teamId 团队ID
     * @param isAdmin 是否为管理员（1:管理员, 0:非管理员）
     * @return 成员列表（包含姓名、手机号、角色类型）
     */
    @Override
    public RestResult<List<Map<String, Object>>> getTeamMembersByType(String teamId, Integer isAdmin) {
        try {
            // 获取所有角色
            List<WsRole> allRoles = wsRoleService.list();
            Map<String, WsRole> roleMap = new HashMap<>(allRoles.size() * 2);
            
            // 构建角色ID到角色对象的映射
            for (WsRole role : allRoles) {
                roleMap.put(role.getRoleId(), role);
            }
            
            // 查询团队中的成员
            LambdaQueryWrapper<WsUserTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsUserTeams::getTeamId, teamId);
            queryWrapper.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            
            // 根据isAdmin参数区分管理员和非管理员
            if (isAdmin == 1) {
                // 获取admin角色ID（不包括mainAdmin）
                List<String> adminRoleIds = new ArrayList<>();
                for (WsRole role : allRoles) {
                    if ("admin".equals(role.getRoleCode()) || "mainAdmin".equals(role.getRoleCode())) {
                        adminRoleIds.add(role.getRoleId());
                    }
                }
                
                if (!adminRoleIds.isEmpty()) {
                    queryWrapper.in(WsUserTeams::getRoleId, adminRoleIds);
                } else {
                    // 如果没有找到符合条件的角色，则返回空列表
                    return RestResult.success(new ArrayList<>());
                }
            } else if (isAdmin == 0) {
                // 获取非管理员角色ID（不包括admin和mainAdmin）
                List<String> adminRoleCodes = Arrays.asList("admin", "mainAdmin");
                List<String> adminRoleIds = new ArrayList<>();
                
                for (WsRole role : allRoles) {
                    if (adminRoleCodes.contains(role.getRoleCode())) {
                        adminRoleIds.add(role.getRoleId());
                    }
                }
                
                if (!adminRoleIds.isEmpty()) {
                    queryWrapper.notIn(WsUserTeams::getRoleId, adminRoleIds);
                }
            }
            
            List<WsUserTeams> teamMembers = this.list(queryWrapper);
            
            if (teamMembers.isEmpty()) {
                return RestResult.success(new ArrayList<>());
            }
            
            // 获取用户ID
            List<String> userIds = teamMembers.stream()
                    .map(WsUserTeams::getUserId)
                    .collect(Collectors.toList());
            
            // 查询用户详细信息
            LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.in(WsUser::getUserId, userIds);
            userQuery.eq(WsUser::getDelFlag, new BigDecimal(0)); // 未删除
            
            List<WsUser> users = wsUserService.list(userQuery);
            
            // 构建返回结果
            List<Map<String, Object>> result = new ArrayList<>();
            for (WsUser user : users) {
                Map<String, Object> userInfo = new HashMap<>();
                userInfo.put("userId", user.getUserId());
                userInfo.put("username", user.getUsername());
                userInfo.put("phoneNumber", user.getPhoneNumber());
                userInfo.put("avatar", user.getAvatar());

                // 获取用户角色
                String roleType = "";
                for (WsUserTeams userTeam : teamMembers) {
                    if (userTeam.getUserId().equals(user.getUserId())) {
                        WsRole role = roleMap.get(userTeam.getRoleId());
                        if (role != null) {
                            roleType = role.getRoleName();
                        }
                        break;
                    }
                }
                
                userInfo.put("type", roleType);
                result.add(userInfo);
            }
            
            return RestResult.success(result);
        } catch (Exception e) {
            log.error("获取团队成员列表失败", e);
            return RestResult.error("获取团队成员列表失败: " + e.getMessage());
        }
    }

    /**
     * 根据角色代码获取角色名称
     *
     * @param roleCode 角色代码
     * @return 角色名称
     */
    private String getRoleNameByCode(String roleCode) {
        if (roleCode == null || roleCode.isEmpty()) {
            return "";
        }

        try {
            LambdaQueryWrapper<WsRole> query = new LambdaQueryWrapper<>();
            query.eq(WsRole::getRoleCode, roleCode);

            WsRole role = wsRoleService.getOne(query);
            return role != null ? role.getRoleName() : "";
        } catch (Exception e) {
            log.error("获取角色名称失败: {}", e.getMessage());
            return "";
        }
    }
}
