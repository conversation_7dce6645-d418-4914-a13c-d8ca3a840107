package com.hualu.watermask.modules.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 图片和团队关联表
 * @TableName WS_PHOTO_TEAMS
 */
@TableName(value ="WS_PHOTO_TEAMS")
public class WsPhotoTeams {
    /**
     * 
     */
    @TableId(value = "ID")
    private String id;

    /**
     * 图片ID，外键关联USERS表
     */
    @TableField(value = "PHOTO_ID")
    private String photoId;

    /**
     * 团队ID，外键关联TEAMS表
     */
    @TableField(value = "TEAM_ID")
    private String teamId;

    /**
     * 
     */
    public String getId() {
        return id;
    }

    /**
     * 
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 图片ID，外键关联USERS表
     */
    public String getPhotoId() {
        return photoId;
    }

    /**
     * 图片ID，外键关联USERS表
     */
    public void setPhotoId(String photoId) {
        this.photoId = photoId;
    }

    /**
     * 团队ID，外键关联TEAMS表
     */
    public String getTeamId() {
        return teamId;
    }

    /**
     * 团队ID，外键关联TEAMS表
     */
    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        WsPhotoTeams other = (WsPhotoTeams) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getPhotoId() == null ? other.getPhotoId() == null : this.getPhotoId().equals(other.getPhotoId()))
            && (this.getTeamId() == null ? other.getTeamId() == null : this.getTeamId().equals(other.getTeamId()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getPhotoId() == null) ? 0 : getPhotoId().hashCode());
        result = prime * result + ((getTeamId() == null) ? 0 : getTeamId().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", photoId=").append(photoId);
        sb.append(", teamId=").append(teamId);
        sb.append("]");
        return sb.toString();
    }
}