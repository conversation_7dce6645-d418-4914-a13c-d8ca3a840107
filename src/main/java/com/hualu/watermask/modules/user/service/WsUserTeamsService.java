package com.hualu.watermask.modules.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.team.entity.WsTeam;
import com.hualu.watermask.modules.user.entity.WsUser;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import java.util.List;
import java.util.Map;

public interface WsUserTeamsService extends IService<WsUserTeams>{

    /**
     * 申请加入团队
     *
     * @param teamCode 团队编码
     * @param userId 申请用户ID
     * @return 申请结果
     */
    RestResult<Object> applyJoinTeam(String teamCode, String userId);
    
    /**
     * 获取待审核的团队申请列表
     *
     * @param teamId 当前团队ID
     * @param current 当前页
     * @param size 页大小
     * @return 待审核申请列表（包含用户名称、角色名称、团队名称）
     */
    RestResult<List<WsUserTeams>> getPendingApplications(String teamId, Long current, Long size);
    
    /**
     * 审核团队申请
     *
     * @param id 用户团队关联ID
     * @param approved 是否批准
     * @param operatorId 操作人ID
     * @return 审核结果
     */
    RestResult<Object> reviewApplication(String id, Integer approved, String operatorId);
    
    /**
     * 获取用户所属的所有团队（返回用户团队关联信息）
     *
     * @param userId 用户ID
     * @return 团队关联列表
     */
    RestResult<List<WsUserTeams>> getUserTeams(String userId);
    
    /**
     * 获取用户所属的所有团队
     *
     * @param userId 用户ID
     * @param flag 是否返回今日统计数据（1:返回统计数据，0或null:不返回）
     * @return 团队列表
     */
    RestResult<List<WsTeam>> getUserTeamList(String userId, Integer flag);
    
    /**
     * 获取团队的所有成员
     *
     * @param teamId 团队ID
     * @return 成员列表
     */
    RestResult<List<WsUserTeams>> getTeamMembers(String teamId);
    
    /**
     * 获取团队的所有成员用户信息
     *
     * @param teamId 团队ID
     * @param isAdmin 是否为管理员（1:是管理员, 0:非管理员, null:所有成员）
     * @return 成员用户列表（WsUser的dept字段将包含团队名称）
     */
    RestResult<List<WsUser>> getTeamMemberUsers(String teamId, Integer isAdmin);
    
    /**
     * 给用户授权角色
     *
     * @param teamId 团队ID
     * @param targetUserId 目标用户ID
     * @param roleId 角色ID
     * @param operatorId 操作人ID
     * @return 授权结果
     */
    RestResult<Object> assignUserRole(String teamId, String targetUserId, String roleId, String operatorId);
    
    /**
     * 批量给用户授权角色
     *
     * @param teamId 团队ID
     * @param targetUserIds 多个目标用户ID（逗号分隔）
     * @param roleId 角色ID
     * @param operatorId 操作人ID
     * @return 授权结果，包含成功和失败数量
     */
    RestResult<Map<String, Integer>> assignUserRoleBatch(String teamId, String targetUserIds, String roleId, String operatorId);
    
    /**
     * 主管理员转让（与转让者互换角色）
     *
     * @param teamId 团队ID
     * @param targetUserId 目标用户ID
     * @param operatorId 操作人ID（主管理员）
     * @return 转让结果
     */
    RestResult<Object> transferMainAdmin(String teamId, String targetUserId, String operatorId);

    /**
     * 根据团队ID获取所有用户ID
     *
     * @param teamId 团队ID
     * @return 用户ID列表
     */
    List<String> getUserIdsByTeamId(String teamId);
    
    /**
     * 更新用户团队的同步设置
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @param isSyn 是否同步（1:是 0:否）
     * @return 更新结果
     */
    RestResult<Object> updateSyncSetting(String userId, String teamId, Integer isSyn);
    
    /**
     * 获取用户所属的需要同步的团队
     *
     * @param userId 用户ID
     * @return 需要同步的团队列表
     */
    List<WsUserTeams> getSyncTeams(String userId);
    
    /**
     * 获取团队成员树形结构
     *
     * @param teamId 团队ID
     * @param isAdmin 是否为管理员（1:是管理员, 0:非管理员, null:所有成员）
     * @param keyword 用户名关键字（模糊搜索）
     * @return 树形结构的团队和成员
     */
    RestResult<List<Map<String, Object>>> getTeamMembersTree(String teamId, Integer isAdmin, String keyword);
    
    /**
     * 获取常搜人列表
     *
     * @return 常搜人列表
     */
    RestResult<List<String>> getFrequentSearchUsers(String teamId);

    /**
     * 根据类型获取团队成员列表（管理员或非管理员）
     *
     * @param teamId 团队ID
     * @param isAdmin 是否为管理员（1:管理员, 0:非管理员）
     * @return 成员列表（包含姓名、手机号、角色类型）
     */
    RestResult<List<Map<String, Object>>> getTeamMembersByType(String teamId, Integer isAdmin);

    /**
     * 获取邀请用户的团队列表
     *
     * @param userId 用户ID
     * @param current 当前页
     * @param size 页大小
     * @return 邀请用户的团队列表
     */
    RestResult<List<WsUserTeams>> getInviteTeamList(String userId, Long current, Long size);
}
