package com.hualu.watermask.modules.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.watermask.modules.user.entity.WsUser;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.springframework.validation.annotation.Validated;

@Validated
public interface WsUserService extends IService<WsUser> {

  @NotNull WsUser getUser(
      @NotBlank(message = "用户名代码不能为空")
      @Size(min = 4, max = 20, message = "用户名长度必须在4-20之间")
      String userCode,
      @NotBlank(message = "密码不能为空")
      String password
  );

  WsUser register(
      @NotBlank(message = "用户名代码不能为空")
      @Size(min = 4, max = 20, message = "用户名长度必须在4-20之间")
      String userCode,
      @NotBlank(message = "姓名不能为空")
      @Size(min = 1, max = 8, message = "用户名长度必须在1-8之间")
      String userName,
      @NotBlank(message = "密码不能为空")
      String password
  );
}
