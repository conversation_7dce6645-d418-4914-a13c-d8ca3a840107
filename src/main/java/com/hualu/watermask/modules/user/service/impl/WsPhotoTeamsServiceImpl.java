package com.hualu.watermask.modules.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.user.entity.WsPhotoTeams;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import com.hualu.watermask.modules.user.mapper.WsPhotoTeamsMapper;
import com.hualu.watermask.modules.user.service.WsPhotoTeamsService;
import com.hualu.watermask.modules.user.service.WsUserTeamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
* <AUTHOR>
* @description 针对表【WS_PHOTO_TEAMS(图片和团队关联表)】的数据库操作Service实现
* @createDate 2025-07-23 15:14:20
*/
@Service
public class WsPhotoTeamsServiceImpl extends ServiceImpl<WsPhotoTeamsMapper, WsPhotoTeams>
    implements WsPhotoTeamsService {

    private static final Logger log = LoggerFactory.getLogger(WsPhotoTeamsServiceImpl.class);

    @Autowired
    private WsUserTeamsService wsUserTeamsService;

    /**
     * 添加照片与团队的关联
     *
     * @param photoId 照片ID
     * @param teamId 团队ID
     * @return 添加结果
     */
    @Override
    @Transactional
    public RestResult<WsPhotoTeams> addPhotoTeam(String photoId, String teamId) {
        try {
            // 检查是否已经存在关联
            LambdaQueryWrapper<WsPhotoTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsPhotoTeams::getPhotoId, photoId);
            queryWrapper.eq(WsPhotoTeams::getTeamId, teamId);
            
            WsPhotoTeams existingRelation = this.getOne(queryWrapper);
            if (existingRelation != null) {
                return RestResult.success(existingRelation, "该照片与团队已存在关联");
            }
            
            // 创建新的关联
            WsPhotoTeams photoTeam = new WsPhotoTeams();
            photoTeam.setId(UUID.randomUUID().toString().replace("-", ""));
            photoTeam.setPhotoId(photoId);
            photoTeam.setTeamId(teamId);
            
            boolean result = this.save(photoTeam);
            
            if (result) {
                return RestResult.success(photoTeam, "成功添加照片与团队的关联");
            } else {
                return RestResult.error("添加照片团队关联失败");
            }
        } catch (Exception e) {
            log.error("添加照片团队关联失败", e);
            return RestResult.error("添加照片团队关联失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量添加照片与团队的关联
     *
     * @param photoIds 照片ID列表
     * @param teamId 团队ID
     * @return 添加结果
     */
    @Override
    @Transactional
    public RestResult<Object> batchAddPhotoTeam(List<String> photoIds, String teamId) {
        try {
            int successCount = 0;
            
            for (String photoId : photoIds) {
                // 检查是否已经存在关联
                LambdaQueryWrapper<WsPhotoTeams> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(WsPhotoTeams::getPhotoId, photoId);
                queryWrapper.eq(WsPhotoTeams::getTeamId, teamId);
                
                WsPhotoTeams existingRelation = this.getOne(queryWrapper);
                if (existingRelation != null) {
                    continue; // 已存在关联，跳过
                }
                
                // 创建新的关联
                WsPhotoTeams photoTeam = new WsPhotoTeams();
                photoTeam.setId(UUID.randomUUID().toString().replace("-", ""));
                photoTeam.setPhotoId(photoId);
                photoTeam.setTeamId(teamId);
                
                boolean result = this.save(photoTeam);
                if (result) {
                    successCount++;
                }
            }
            
            return RestResult.success(successCount, "成功添加 " + successCount + " 个照片与团队的关联");
        } catch (Exception e) {
            log.error("批量添加照片团队关联失败", e);
            return RestResult.error("批量添加照片团队关联失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除照片与团队的关联
     *
     * @param photoId 照片ID
     * @param teamId 团队ID
     * @return 删除结果
     */
    @Override
    @Transactional
    public RestResult<Object> removePhotoTeam(String photoId, String teamId) {
        try {
            LambdaQueryWrapper<WsPhotoTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsPhotoTeams::getPhotoId, photoId);
            queryWrapper.eq(WsPhotoTeams::getTeamId, teamId);
            
            boolean result = this.remove(queryWrapper);
            
            if (result) {
                return RestResult.success(null, "成功删除照片与团队的关联");
            } else {
                return RestResult.error("删除照片团队关联失败，可能不存在该关联");
            }
        } catch (Exception e) {
            log.error("删除照片团队关联失败", e);
            return RestResult.error("删除照片团队关联失败: " + e.getMessage());
        }
    }
    
    /**
     * 批量删除照片与团队的关联
     *
     * @param photoIds 照片ID列表
     * @param teamId 团队ID
     * @return 删除结果
     */
    @Override
    @Transactional
    public RestResult<Object> batchRemovePhotoTeam(List<String> photoIds, String teamId) {
        try {
            LambdaQueryWrapper<WsPhotoTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsPhotoTeams::getTeamId, teamId);
            queryWrapper.in(WsPhotoTeams::getPhotoId, photoIds);
            
            boolean result = this.remove(queryWrapper);
            
            if (result) {
                return RestResult.success(null, "成功批量删除照片与团队的关联");
            } else {
                return RestResult.error("批量删除照片团队关联失败，可能不存在相关关联");
            }
        } catch (Exception e) {
            log.error("批量删除照片团队关联失败", e);
            return RestResult.error("批量删除照片团队关联失败: " + e.getMessage());
        }
    }
    
    /**
     * 为照片添加同步团队关联
     * 
     * @param photoId 照片ID
     * @param userId 用户ID
     */
    @Override
    public int syncPhotoToTeams(String photoId, String userId) {
        // 获取用户需要同步的团队
        List<WsUserTeams> syncTeams = wsUserTeamsService.getSyncTeams(userId);

        if (syncTeams == null || syncTeams.isEmpty()) {
            throw new RuntimeException("用户未配置需要同步的团队");
        }
        for (WsUserTeams team : syncTeams) {
            // 检查是否已经存在关联
            LambdaQueryWrapper<WsPhotoTeams> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsPhotoTeams::getPhotoId, photoId);
            queryWrapper.eq(WsPhotoTeams::getTeamId, team.getTeamId());

            WsPhotoTeams existingRelation = this.getOne(queryWrapper);
            if (existingRelation != null) {
                continue; // 已存在关联，跳过
            }

            // 创建照片-团队关联记录
            WsPhotoTeams photoTeam = new WsPhotoTeams();
            photoTeam.setId(UUID.randomUUID().toString().replace("-", ""));
            photoTeam.setPhotoId(photoId);
            photoTeam.setTeamId(team.getTeamId());

            // 保存关联记录
            this.save(photoTeam);
        }
        return syncTeams.size();
    }
}




