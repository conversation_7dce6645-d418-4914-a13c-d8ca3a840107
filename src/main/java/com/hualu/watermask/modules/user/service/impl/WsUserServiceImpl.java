package com.hualu.watermask.modules.user.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.common.exception.BusinessException;
import com.hualu.watermask.modules.user.entity.WsUser;
import com.hualu.watermask.modules.user.mapper.WsUserMapper;
import com.hualu.watermask.modules.user.service.WsUserService;
import java.util.List;
import org.springframework.stereotype.Service;

@Service
public class WsUserServiceImpl extends ServiceImpl<WsUserMapper, WsUser> implements WsUserService {

  @Override public WsUser getUser(String userCode, String password) {
    List<WsUser> list = lambdaQuery()
        .eq(WsUser::getUserCode, userCode)
        .eq(WsUser::getPassword, password)
        .list();
    WsUser wsUser = CollUtil.getFirst(list);
    if (wsUser == null) {
      throw new BusinessException("用户名或密码错误");
    }
    return wsUser;
  }

  @Override public WsUser register(String userCode, String userName, String password) {
    WsUser user = lambdaQuery().
        eq(WsUser::getUserCode, userCode)
        .one();
    if (user != null) {
      throw new BusinessException("已存在改用户");
    }

    WsUser wsUser = new WsUser();
    wsUser.setUserCode(userCode);
    wsUser.setUsername(userName);
    wsUser.setPassword(password);
    boolean b = save(wsUser);
    return b ? wsUser : null;
  }
}
