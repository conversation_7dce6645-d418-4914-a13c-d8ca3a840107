package com.hualu.watermask.modules.user.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.team.entity.WsTeam;
import com.hualu.watermask.modules.user.entity.WsUser;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import com.hualu.watermask.modules.user.service.WsUserTeamsService;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户团队关联控制器
 */
@RestController
@RequestMapping("/api/user/team")
@CrossOrigin
public class WsUserTeamsController {

    @Autowired
    private WsUserTeamsService wsUserTeamsService;

    /**
     * 申请加入团队
     *
     * @param teamCode 团队编码
     * @return 申请结果
     */
    @PostMapping("/apply")
    @OperationLog(businessType = "用户团队管理", operationContent = "申请加入团队", operationType = "INSERT")
    public RestResult<Object> applyJoinTeam(@RequestParam String teamCode) {
        String userId = StpUtil.getLoginIdAsString();
        return wsUserTeamsService.applyJoinTeam(teamCode, userId);
    }

    /**
     * 获取待审核的团队申请列表
     *
     * @param teamId 团队ID
     * @param current 当前页
     * @param size 页大小
     * @return 待审核申请列表（包含用户名称、角色名称、团队名称）
     */
    @GetMapping("/pending")
    @OperationLog(businessType = "用户团队管理", operationContent = "查看待审核申请", operationType = "SELECT")
    public RestResult<List<WsUserTeams>> getPendingApplications(
        @RequestParam String teamId,
        @RequestParam(value = "current", defaultValue = "1") Long current,
        @RequestParam(value = "size", defaultValue = "15") Long size
    ) {
        return wsUserTeamsService.getPendingApplications(teamId, current, size);
    }

    /**
     * 获取邀请用户的团队列表
     *
     * @param current 当前页
     * @param size 页大小
     * @return 邀请用户的团队列表
     */
    @GetMapping("/getInviteTeamList")
    @OperationLog(businessType = "用户团队管理", operationContent = "获取邀请用户的团队列表", operationType = "SELECT")
    public RestResult<List<WsUserTeams>> getInviteTeamList(
        @RequestParam(value = "current", defaultValue = "1") Long current,
        @RequestParam(value = "size", defaultValue = "15") Long size
    ) {
        String userId = StpUtil.getLoginIdAsString();
        return wsUserTeamsService.getInviteTeamList(userId, current, size);
    }

    /**
     * 审核团队申请
     *
     * @param id 用户团队关联ID
     * @param approved 是否批准
     * @return 审核结果
     */
    @PostMapping("/review")
    @OperationLog(businessType = "用户团队管理", operationContent = "审核团队申请", operationType = "UPDATE")
    public RestResult<Object> reviewApplication(
            @RequestParam String id,
            @RequestParam Integer approved) {
        String operatorId = StpUtil.getLoginIdAsString();
        return wsUserTeamsService.reviewApplication(id, approved, operatorId);
    }

    /**
     * 获取用户所属的所有团队
     *
     * @return 团队列表
     */
    @GetMapping("/user")
    @OperationLog(businessType = "用户团队管理", operationContent = "获取用户所属团队", operationType = "SELECT")
    public RestResult<List<WsTeam>> getUserTeams() {
        String userId = StpUtil.getLoginIdAsString();
        // 传入flag=1表示返回今日统计数据
        return wsUserTeamsService.getUserTeamList(userId, 1);
    }

    /**
     * 获取团队的所有成员
     *
     * @param teamId 团队ID
     * @param isAdmin 是否为管理员（1:是管理员, 0:非管理员, null:所有成员）
     * @param keyword 用户名关键字（模糊搜索）
     * @return 成员列表（树形结构）
     */
    @GetMapping("/getTeamMembers")
    @OperationLog(businessType = "用户团队管理", operationContent = "获取团队成员", operationType = "SELECT")
    public RestResult<List<Map<String, Object>>> getTeamMembers(
            @RequestParam String teamId,
            @RequestParam(required = false) Integer isAdmin,
            @RequestParam(required = false) String keyword) {
        return wsUserTeamsService.getTeamMembersTree(teamId, isAdmin, keyword);
    }

    /**
     * 给用户授权角色
     *
     * @param teamId 团队ID
     * @param targetUserId 目标用户ID
     * @param roleId 角色ID
     * @return 授权结果
     */
    @PostMapping("/assign-role")
    @OperationLog(businessType = "用户团队管理", operationContent = "给用户授权角色", operationType = "UPDATE")
    public RestResult<Object> assignUserRole(
            @RequestParam String teamId,
            @RequestParam String targetUserId,
            @RequestParam String roleId) {
        String operatorId = StpUtil.getLoginIdAsString();
        return wsUserTeamsService.assignUserRole(teamId, targetUserId, roleId, operatorId);
    }

    /**
     * 批量给用户授权角色
     *
     * @param teamId 团队ID
     * @param targetUserIds 多个目标用户ID（逗号分隔）
     * @param roleId 角色ID
     * @return 授权结果，包含成功和失败数量
     */
    @PostMapping("/assign-roles")
    @OperationLog(businessType = "用户团队管理", operationContent = "批量给用户授权角色", operationType = "UPDATE")
    public RestResult<Map<String, Integer>> assignUserRoleBatch(
            @RequestParam String teamId,
            @RequestParam String targetUserIds,
            @RequestParam String roleId) {
        String operatorId = StpUtil.getLoginIdAsString();
        return wsUserTeamsService.assignUserRoleBatch(teamId, targetUserIds, roleId, operatorId);
    }

    /**
     * 主管理员转让（与转让者互换角色）
     *
     * @param teamId 团队ID
     * @param targetUserId 目标用户ID
     * @return 转让结果
     */
    @PostMapping("/transfer-main-admin")
    @OperationLog(businessType = "用户团队管理", operationContent = "主管理员转让", operationType = "UPDATE")
    public RestResult<Object> transferMainAdmin(
            @RequestParam String teamId,
            @RequestParam String targetUserId) {
        String operatorId = StpUtil.getLoginIdAsString();
        return wsUserTeamsService.transferMainAdmin(teamId, targetUserId, operatorId);
    }

    /**
     * 更新用户团队的同步设置
     *
     * @param teamId 团队ID
     * @param isSyn 是否同步（1:是 0:否）
     * @return 更新结果
     */
    @PostMapping("/update-sync")
    @OperationLog(businessType = "用户团队管理", operationContent = "更新同步设置", operationType = "UPDATE")
    public RestResult<Object> updateSyncSetting(
            @RequestParam String teamId,
            @RequestParam Integer isSyn) {
        String userId = StpUtil.getLoginIdAsString();
        return wsUserTeamsService.updateSyncSetting(userId, teamId, isSyn);
    }

    /**
     * 获取常搜人列表
     *
     * @return 常搜人列表
     */
    @GetMapping("/getFrequentSearchUsers")
    @OperationLog(businessType = "用户团队管理", operationContent = "获取常搜人列表", operationType = "SELECT")
    public RestResult<List<String>> getFrequentSearchUsers(String teamId) {
        return wsUserTeamsService.getFrequentSearchUsers(teamId);
    }
} 