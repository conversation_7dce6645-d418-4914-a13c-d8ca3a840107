package com.hualu.watermask.modules.user.service;

import com.hualu.watermask.modules.user.entity.WsPhotoTeams;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.watermask.modules.common.vo.RestResult;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【WS_PHOTO_TEAMS(图片和团队关联表)】的数据库操作Service
* @createDate 2025-07-23 15:14:20
*/
public interface WsPhotoTeamsService extends IService<WsPhotoTeams> {

    /**
     * 添加照片与团队的关联
     *
     * @param photoId 照片ID
     * @param teamId 团队ID
     * @return 添加结果
     */
    RestResult<WsPhotoTeams> addPhotoTeam(String photoId, String teamId);
    
    /**
     * 批量添加照片与团队的关联
     *
     * @param photoIds 照片ID列表
     * @param teamId 团队ID
     * @return 添加结果
     */
    RestResult<Object> batchAddPhotoTeam(List<String> photoIds, String teamId);
    
    /**
     * 删除照片与团队的关联
     *
     * @param photoId 照片ID
     * @param teamId 团队ID
     * @return 删除结果
     */
    RestResult<Object> removePhotoTeam(String photoId, String teamId);
    
    /**
     * 批量删除照片与团队的关联
     *
     * @param photoIds 照片ID列表
     * @param teamId 团队ID
     * @return 删除结果
     */
    RestResult<Object> batchRemovePhotoTeam(List<String> photoIds, String teamId);
    
    /**
     * 为照片添加同步团队关联
     * 
     * @param photoId 照片ID
     * @param userId 用户ID
     * @return 处理结果
     */
    int syncPhotoToTeams(String photoId, String userId);
}
