package com.hualu.watermask.modules.user.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.log.entity.WsOperationLog;
import com.hualu.watermask.modules.log.service.WsOperationLogService;
import com.hualu.watermask.modules.user.entity.WsUser;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import com.hualu.watermask.modules.user.service.WsUserService;
import com.hualu.watermask.modules.user.service.WsUserTeamsService;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 用户控制器
 *
 * <AUTHOR>
 * @since 2025-07-16 10:44
 */
@RequestMapping("/user")
@RestController
@CrossOrigin
public class WsUserController {

  @Resource
  private WsUserService wsUserService;

  @Autowired
  private WsUserTeamsService wsUserTeamsService;

  @Autowired
  private WsOperationLogService wsOperationLogService;

  /**
   * 注册
   * @return
   */
  @PostMapping("/register")
  public RestResult<WsUser> register(String userCode, String username, String password) {
    WsUser w = wsUserService.register(userCode, username, password);
    return RestResult.success(w, "注册成功");
  }

  @PostMapping("/login")
  @OperationLog(businessType = "用户管理", operationContent = "用户登录", operationType = "SELECT")
  public RestResult<WsUser> login(String userCode, String password, HttpServletResponse response) {
    WsUser user = wsUserService.getUser(userCode, password);
    StpUtil.login(user.getUserId());
    response.addHeader(StpUtil.getTokenName(), StpUtil.getTokenValue());
    if (user != null) {
      user.setToken(StpUtil.getTokenValue());

      // 获取用户设置了同步状态的团队ID集合
      List<WsUserTeams> syncTeams = wsUserTeamsService.getSyncTeams(user.getUserId());
      if (syncTeams != null && !syncTeams.isEmpty()) {
        String teamIds = syncTeams.stream()
            .map(WsUserTeams::getTeamId)
            .collect(Collectors.joining(","));
        user.setTeamIds(teamIds);
      } else {
        user.setTeamIds("");
      }
    }
    return RestResult.success(user, "登录成功");
  }

  @GetMapping("/isLogin")
  public RestResult<Boolean> isLogin() {
    return RestResult.success(StpUtil.isLogin());
  }

  @GetMapping("/logout")
  public RestResult<String> logout() {
    // 在注销前获取用户信息，确保操作日志能正确记录
    String userId = null;
    String username = null;
    try {
      if (StpUtil.isLogin()) {
        userId = StpUtil.getLoginIdAsString();
        WsUser user = wsUserService.getById(userId);
        if (user != null) {
          username = user.getUsername();
        }
      }
    } catch (Exception e) {
      // 忽略获取用户信息的异常，继续执行注销
    }

    // 执行注销
    StpUtil.logout();

    // 手动记录操作日志（因为切面在注销后无法获取用户信息）
    if (userId != null && username != null) {
      try {
        recordLogoutOperation(userId, username);
      } catch (Exception e) {
        // 记录日志失败不影响注销操作
      }
    }

    return RestResult.success("注销成功");
  }
  
  /**
   * 获取团队成员列表
   *
   * @param teamId 团队ID
   * @param isAdmin 是否为管理员（1:管理员, 0:非管理员）
   * @return 成员列表（包含姓名、手机号、角色类型）
   */
  @GetMapping("/getTeamMembers")
  @OperationLog(businessType = "用户管理", operationContent = "获取团队成员列表", operationType = "SELECT")
  public RestResult<List<Map<String, Object>>> getTeamMembers(
          @RequestParam String teamId,
          @RequestParam Integer isAdmin) {
    return wsUserTeamsService.getTeamMembersByType(teamId, isAdmin);
  }

  /**
   * 手动记录注销操作日志
   *
   * @param userId 用户ID
   * @param username 用户名
   */
  private void recordLogoutOperation(String userId, String username) {
    try {
      WsOperationLog wsLog = new WsOperationLog();
      wsLog.setId(UUID.randomUUID().toString().replace("-", ""));
      wsLog.setOperationTime(new Date());
      wsLog.setBusinessType("用户管理");
      wsLog.setOperationContent("用户注销");
      wsLog.setOperationType("SELECT");
      wsLog.setStatus(new BigDecimal(1)); // 成功状态
      wsLog.setOperatorCode(userId);
      wsLog.setOperatorName(username);

      // 获取IP地址
      try {
        javax.servlet.http.HttpServletRequest request =
            ((org.springframework.web.context.request.ServletRequestAttributes)
             org.springframework.web.context.request.RequestContextHolder.getRequestAttributes())
            .getRequest();
        if (request != null) {
          wsLog.setIpAddress(request.getRemoteAddr());
        }
      } catch (Exception e) {
        // 忽略获取IP地址的异常
      }

      wsOperationLogService.save(wsLog);
    } catch (Exception e) {
      // 记录日志失败不影响主流程
    }
  }
}
