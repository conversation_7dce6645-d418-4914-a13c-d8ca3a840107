package com.hualu.watermask.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 用户与团队关联表，定义用户在团队中的角色
 */
@TableName(value = "WS_USER_TEAMS")
public class WsUserTeams {
  @TableId(value = "ID")
  private String id;

  /**
   * 用户ID，外键关联USERS表
   */
  @TableField(value = "USER_ID")
  private String userId;

  /**
   * 团队ID，外键关联TEAMS表
   */
  @TableField(value = "TEAM_ID")
  private String teamId;

  /**
   * 角色ID，外键关联ROLES表
   */
  @TableField(value = "ROLE_ID")
  private String roleId;

  /**
   * 用户分配到团队的时间
   */
  @TableField(value = "ASSIGNED_DATE")
  private Date assignedDate;

  /**
   * 分配操作执行者用户名
   */
  @TableField(value = "ASSIGNED_BY")
  private String assignedBy;
  
  /**
   * 加入状态（0:待加入 1:已加入）
   */
  @TableField(value = "STATUS")
  private BigDecimal status;

  /**
   * 同步状态（0:未同步 1:已同步）
   */
  @TableField(value = "IS_SYN")
  private BigDecimal isSyn;

  @TableField(exist = false)
  private String name;

  /**
   * @return ID
   */
  public String getId() {
    return id;
  }

  /**
   * @param id
   */
  public void setId(String id) {
    this.id = id;
  }

  /**
   * 获取用户ID，外键关联USERS表
   *
   * @return USER_ID - 用户ID，外键关联USERS表
   */
  public String getUserId() {
    return userId;
  }

  /**
   * 设置用户ID，外键关联USERS表
   *
   * @param userId 用户ID，外键关联USERS表
   */
  public void setUserId(String userId) {
    this.userId = userId;
  }

  /**
   * 获取团队ID，外键关联TEAMS表
   *
   * @return TEAM_ID - 团队ID，外键关联TEAMS表
   */
  public String getTeamId() {
    return teamId;
  }

  /**
   * 设置团队ID，外键关联TEAMS表
   *
   * @param teamId 团队ID，外键关联TEAMS表
   */
  public void setTeamId(String teamId) {
    this.teamId = teamId;
  }

  /**
   * 获取角色ID，外键关联ROLES表
   *
   * @return ROLE_ID - 角色ID，外键关联ROLES表
   */
  public String getRoleId() {
    return roleId;
  }

  /**
   * 设置角色ID，外键关联ROLES表
   *
   * @param roleId 角色ID，外键关联ROLES表
   */
  public void setRoleId(String roleId) {
    this.roleId = roleId;
  }

  /**
   * 获取用户分配到团队的时间
   *
   * @return ASSIGNED_DATE - 用户分配到团队的时间
   */
  public Date getAssignedDate() {
    return assignedDate;
  }

  /**
   * 设置用户分配到团队的时间
   *
   * @param assignedDate 用户分配到团队的时间
   */
  public void setAssignedDate(Date assignedDate) {
    this.assignedDate = assignedDate;
  }

  /**
   * 获取分配操作执行者用户名
   *
   * @return ASSIGNED_BY - 分配操作执行者用户名
   */
  public String getAssignedBy() {
    return assignedBy;
  }

  /**
   * 设置分配操作执行者用户名
   *
   * @param assignedBy 分配操作执行者用户名
   */
  public void setAssignedBy(String assignedBy) {
    this.assignedBy = assignedBy;
  }
  
  /**
   * 获取加入状态（0:待加入 1:已加入）
   *
   * @return STATUS - 加入状态（0:待加入 1:已加入）
   */
  public BigDecimal getStatus() {
    return status;
  }

  /**
   * 设置加入状态（0:待加入 1:已加入）
   *
   * @param status 加入状态（0:待加入 1:已加入）
   */
  public void setStatus(BigDecimal status) {
    this.status = status;
  }

  public BigDecimal getIsSyn() {
    return isSyn;
  }

  public void setIsSyn(BigDecimal isSyn) {
    this.isSyn = isSyn;
  }

  public String getName() {
    return name;
  }

  public void setName(String name) {
    this.name = name;
  }

  /**
   * 团队名称（非数据库字段，用于接口返回）
   */
  @TableField(exist = false)
  private String teamName;

  /**
   * 团队编码（非数据库字段，用于接口返回）
   */
  @TableField(exist = false)
  private String teamCode;

  /**
   * 团队描述（非数据库字段，用于接口返回）
   */
  @TableField(exist = false)
  private String teamDescription;

  /**
   * 父团队ID（非数据库字段，用于接口返回）
   */
  @TableField(exist = false)
  private String parentTeamId;

  /**
   * 顶级团队名称（非数据库字段，用于接口返回）
   */
  @TableField(exist = false)
  private String topTeamName;

  /**
   * 角色名称（非数据库字段，用于接口返回）
   */
  @TableField(exist = false)
  private String roleName;

  /**
   * 用户名称（非数据库字段，用于接口返回）
   */
  @TableField(exist = false)
  private String username;

  public String getTeamName() {
    return teamName;
  }

  public void setTeamName(String teamName) {
    this.teamName = teamName;
  }

  public String getTeamCode() {
    return teamCode;
  }

  public void setTeamCode(String teamCode) {
    this.teamCode = teamCode;
  }

  public String getTeamDescription() {
    return teamDescription;
  }

  public void setTeamDescription(String teamDescription) {
    this.teamDescription = teamDescription;
  }

  public String getParentTeamId() {
    return parentTeamId;
  }

  public void setParentTeamId(String parentTeamId) {
    this.parentTeamId = parentTeamId;
  }

  public String getTopTeamName() {
    return topTeamName;
  }

  public void setTopTeamName(String topTeamName) {
    this.topTeamName = topTeamName;
  }

  public String getRoleName() {
    return roleName;
  }

  public void setRoleName(String roleName) {
    this.roleName = roleName;
  }

  public String getUsername() {
    return username;
  }

  public void setUsername(String username) {
    this.username = username;
  }
}