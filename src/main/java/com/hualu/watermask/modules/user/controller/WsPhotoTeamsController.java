package com.hualu.watermask.modules.user.controller;

import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.user.entity.WsPhotoTeams;
import com.hualu.watermask.modules.user.service.WsPhotoTeamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.stp.StpUtil;

import java.util.List;

/**
 * 图片团队关联表控制器
 *
 * @since 2025-07-16 10:44
 */
@RequestMapping("/api/photo/team")
@RestController
@CrossOrigin
public class WsPhotoTeamsController {
    
    @Autowired
    private WsPhotoTeamsService wsPhotoTeamsService;
    
    /**
     * 添加照片与团队的关联
     *
     * @param photoId 照片ID
     * @param teamId 团队ID
     * @return 添加结果
     */
    @PostMapping("/add")
    @OperationLog(businessType = "照片团队关联管理", operationContent = "添加照片团队关联", operationType = "INSERT")
    public RestResult<WsPhotoTeams> addPhotoTeam(
            @RequestParam String photoId,
            @RequestParam String teamId) {
        return wsPhotoTeamsService.addPhotoTeam(photoId, teamId);
    }
    
    /**
     * 批量添加照片与团队的关联
     *
     * @param photoIds 照片ID列表
     * @param teamId 团队ID
     * @return 添加结果
     */
    @PostMapping("/batch-add")
    @OperationLog(businessType = "照片团队关联管理", operationContent = "批量添加照片团队关联", operationType = "INSERT")
    public RestResult<Object> batchAddPhotoTeam(
            @RequestParam List<String> photoIds,
            @RequestParam String teamId) {
        return wsPhotoTeamsService.batchAddPhotoTeam(photoIds, teamId);
    }
    
    /**
     * 删除照片与团队的关联
     *
     * @param photoId 照片ID
     * @param teamId 团队ID
     * @return 删除结果
     */
    @DeleteMapping("/remove")
    @OperationLog(businessType = "照片团队关联管理", operationContent = "删除照片团队关联", operationType = "DELETE")
    public RestResult<Object> removePhotoTeam(
            @RequestParam String photoId,
            @RequestParam String teamId) {
        return wsPhotoTeamsService.removePhotoTeam(photoId, teamId);
    }
    
    /**
     * 批量删除照片与团队的关联
     *
     * @param photoIds 照片ID列表
     * @param teamId 团队ID
     * @return 删除结果
     */
    @DeleteMapping("/batch-remove")
    @OperationLog(businessType = "照片团队关联管理", operationContent = "批量删除照片团队关联", operationType = "DELETE")
    public RestResult<Object> batchRemovePhotoTeam(
            @RequestParam List<String> photoIds,
            @RequestParam String teamId) {
        return wsPhotoTeamsService.batchRemovePhotoTeam(photoIds, teamId);
    }
}
