package com.hualu.watermask.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 系统用户账户信息表
 */
@TableName(value = "WS_USER")
@JsonIgnoreProperties({"password", "createdBy", "delFlag", "creationDate", "loginDate"})  // 忽略多个字段
public class WsUser {
  /**
   * 用户唯一标识符，主键
   */
  @TableId(value = "USER_ID")
  private String userId;

  /**
   * 头像
   */
  @TableField(value = "AVATAR")
  private String avatar;

  /**
   * 用户编码
   */
  @TableField(value = "USER_CODE")
  private String userCode;

  /**
   * 用户名
   */
  @TableField(value = "USERNAME")
  private String username;

  /**
   * 用户邮箱地址
   */
  @TableField(value = "EMAIL")
  private String email;

  /**
   * 密码
   */
  @TableField(value = "\"PASSWORD\"")
  private String password;

  /**
   * 用户联系电话号码
   */
  @TableField(value = "PHONE_NUMBER")
  private String phoneNumber;

  /**
   * 账户启用状态：1-启用，0-禁用
   */
  @TableField(value = "ENABLED")
  private BigDecimal enabled;

  /**
   * 用户账户创建者
   */
  @TableField(value = "CREATED_BY")
  private String createdBy;

  /**
   * 用户账户创建时间
   */
  @TableField(value = "CREATION_DATE")
  private Date creationDate;

  /**
   * 用户登录时间
   */
  @TableField(value = "LOGIN_DATE")
  private Date loginDate;

  /**
   * 是否删除1删除0未删除
   */
  @TableField(value = "DEL_FLAG")
  private BigDecimal delFlag;

  @TableField(exist = false)
  private String token;

  @TableField(exist = false)
  private String type;

  @TableField(exist = false)
  private String dept;

  /**
   * 用户设置了同步状态的团队ID集合，逗号分隔（非数据库字段）
   */
  @TableField(exist = false)
  private String teamIds;

  @TableField(exist = false)
  private Date lastActive;

  /**
   * 获取用户唯一标识符，主键
   *
   * @return USER_ID - 用户唯一标识符，主键
   */
  public String getUserId() {
    return userId;
  }

  /**
   * 设置用户唯一标识符，主键
   *
   * @param userId 用户唯一标识符，主键
   */
  public void setUserId(String userId) {
    this.userId = userId;
  }

  /**
   * 获取用户编码
   *
   * @return USER_CODE - 用户编码
   */
  public String getUserCode() {
    return userCode;
  }

  /**
   * 设置用户编码
   *
   * @param userCode 用户编码
   */
  public void setUserCode(String userCode) {
    this.userCode = userCode;
  }

  /**
   * 获取用户名
   *
   * @return USERNAME - 用户名
   */
  public String getUsername() {
    return username;
  }

  /**
   * 设置用户名
   *
   * @param username 用户名
   */
  public void setUsername(String username) {
    this.username = username;
  }

  /**
   * 获取用户邮箱地址
   *
   * @return EMAIL - 用户邮箱地址
   */
  public String getEmail() {
    return email;
  }

  /**
   * 设置用户邮箱地址
   *
   * @param email 用户邮箱地址
   */
  public void setEmail(String email) {
    this.email = email;
  }

  /**
   * 获取密码
   *
   * @return PASSWORD - 密码
   */
  public String getPassword() {
    return password;
  }

  /**
   * 设置密码
   *
   * @param password 密码
   */
  public void setPassword(String password) {
    this.password = password;
  }

  /**
   * 获取用户联系电话号码
   *
   * @return PHONE_NUMBER - 用户联系电话号码
   */
  public String getPhoneNumber() {
    return phoneNumber;
  }

  /**
   * 设置用户联系电话号码
   *
   * @param phoneNumber 用户联系电话号码
   */
  public void setPhoneNumber(String phoneNumber) {
    this.phoneNumber = phoneNumber;
  }

  /**
   * 获取账户启用状态：1-启用，0-禁用
   *
   * @return ENABLED - 账户启用状态：1-启用，0-禁用
   */
  public BigDecimal getEnabled() {
    return enabled;
  }

  /**
   * 设置账户启用状态：1-启用，0-禁用
   *
   * @param enabled 账户启用状态：1-启用，0-禁用
   */
  public void setEnabled(BigDecimal enabled) {
    this.enabled = enabled;
  }

  /**
   * 获取用户账户创建者
   *
   * @return CREATED_BY - 用户账户创建者
   */
  public String getCreatedBy() {
    return createdBy;
  }

  /**
   * 设置用户账户创建者
   *
   * @param createdBy 用户账户创建者
   */
  public void setCreatedBy(String createdBy) {
    this.createdBy = createdBy;
  }

  /**
   * 获取用户账户创建时间
   *
   * @return CREATION_DATE - 用户账户创建时间
   */
  public Date getCreationDate() {
    return creationDate;
  }

  /**
   * 设置用户账户创建时间
   *
   * @param creationDate 用户账户创建时间
   */
  public void setCreationDate(Date creationDate) {
    this.creationDate = creationDate;
  }

  /**
   * 获取用户登录时间
   *
   * @return LOGIN_DATE - 用户登录时间
   */
  public Date getLoginDate() {
    return loginDate;
  }

  /**
   * 设置用户登录时间
   *
   * @param loginDate 用户登录时间
   */
  public void setLoginDate(Date loginDate) {
    this.loginDate = loginDate;
  }

  /**
   * 获取是否删除1删除0未删除
   *
   * @return DEL_FLAG - 是否删除1删除0未删除
   */
  public BigDecimal getDelFlag() {
    return delFlag;
  }

  /**
   * 设置是否删除1删除0未删除
   *
   * @param delFlag 是否删除1删除0未删除
   */
  public void setDelFlag(BigDecimal delFlag) {
    this.delFlag = delFlag;
  }

  public String getToken() {
    return token;
  }

  public void setToken(String token) {
    this.token = token;
  }

  public String getAvatar() {
    return avatar;
  }

  public void setAvatar(String avatar) {
    this.avatar = avatar;
  }

  public String getType() {
    return type;
  }

  public void setType(String type) {
    this.type = type;
  }

  public String getDept() {
    return dept;
  }

  public void setDept(String dept) {
    this.dept = dept;
  }

  public Date getLastActive() {
    return loginDate;
  }

  public String getTeamIds() {
    return teamIds;
  }

  public void setTeamIds(String teamIds) {
    this.teamIds = teamIds;
  }


}