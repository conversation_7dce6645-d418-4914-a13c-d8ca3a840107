package com.hualu.watermask.modules.watermaskphoto.controller;

import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhotoCustom;
import com.hualu.watermask.modules.watermaskphoto.service.WatermarkPhotoCustomService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 水印照片自定义内容控制器
 */
@RestController
@RequestMapping("/api/photo/custom")
@CrossOrigin
public class WatermarkPhotoCustomController {

    @Autowired
    private WatermarkPhotoCustomService watermarkPhotoCustomService;

    /**
     * 更新照片自定义内容
     *
     * @param customId 自定义内容ID
     * @param content 内容
     * @param fontColor 字体颜色（可选）
     * @param backgroundColor 背景颜色（可选）
     * @return 更新结果
     */
    @PostMapping("/update")
    public RestResult<WatermarkPhotoCustom> updateCustomContent(
            @RequestParam String customId,
            @RequestParam String content,
            @RequestParam(required = false) String fontColor,
            @RequestParam(required = false) String backgroundColor) {
        return watermarkPhotoCustomService.updateCustomContent(customId, content, fontColor, backgroundColor);
    }

    /**
     * 批量更新照片自定义内容
     *
     * @param customContents 自定义内容列表
     * @return 更新结果
     */
    @PostMapping("/batch")
    public RestResult<Boolean> batchUpdateCustomContent(@RequestBody List<WatermarkPhotoCustom> customContents) {
        return watermarkPhotoCustomService.batchUpdateCustomContent(customContents);
    }

    /**
     * 获取照片自定义内容详情
     *
     * @param customId 自定义内容ID
     * @return 内容详情
     */
    @GetMapping("/get")
    public RestResult<WatermarkPhotoCustom> getCustomContent(@RequestParam String customId) {
        WatermarkPhotoCustom custom = watermarkPhotoCustomService.getById(customId);
        if (custom != null) {
            return RestResult.success(custom);
        } else {
            return RestResult.error("自定义内容不存在");
        }
    }
} 