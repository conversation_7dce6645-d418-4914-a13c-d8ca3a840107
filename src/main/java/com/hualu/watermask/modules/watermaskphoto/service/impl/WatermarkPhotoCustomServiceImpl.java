package com.hualu.watermask.modules.watermaskphoto.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.watermaskphoto.mapper.WatermarkPhotoCustomMapper;
import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhotoCustom;
import com.hualu.watermask.modules.watermaskphoto.service.WatermarkPhotoCustomService;
import com.hualu.watermask.modules.common.vo.RestResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

@Service
public class WatermarkPhotoCustomServiceImpl extends ServiceImpl<WatermarkPhotoCustomMapper, WatermarkPhotoCustom> implements WatermarkPhotoCustomService{

    /**
     * 更新照片自定义内容
     *
     * @param customId 自定义内容ID
     * @param content 内容
     * @param fontColor 字体颜色
     * @param backgroundColor 背景颜色
     * @return 更新结果
     */
    @Override
    @Transactional
    public RestResult<WatermarkPhotoCustom> updateCustomContent(String customId, String content, String fontColor, String backgroundColor) {
        if(StringUtils.isBlank(customId)) {
            return RestResult.error("自定义内容ID不能为空");
        }
        
        WatermarkPhotoCustom custom = this.getById(customId);
        if (custom == null) {
            return RestResult.error("自定义内容不存在");
        }
        
        // 更新内容
        custom.setContent(content);
        
        // 更新字体颜色和背景颜色（如果提供）
        if (StringUtils.isNotBlank(fontColor)) {
            custom.setFontColor(fontColor);
        }
        
        if (StringUtils.isNotBlank(backgroundColor)) {
            custom.setBackgroundColor(backgroundColor);
        }
        
        boolean result = this.updateById(custom);
        if (result) {
            return RestResult.success(custom, "更新自定义内容成功");
        } else {
            return RestResult.error("更新自定义内容失败");
        }
    }
    
    /**
     * 批量更新照片自定义内容
     *
     * @param customContents 自定义内容列表
     * @return 更新结果
     */
    @Override
    @Transactional
    public RestResult<Boolean> batchUpdateCustomContent(List<WatermarkPhotoCustom> customContents) {
        if (customContents == null || customContents.isEmpty()) {
            return RestResult.error("自定义内容列表不能为空");
        }
        
        boolean result = this.updateBatchById(customContents);
        if (result) {
            return RestResult.success(true, "批量更新自定义内容成功");
        } else {
            return RestResult.error("批量更新自定义内容失败");
        }
    }
}
