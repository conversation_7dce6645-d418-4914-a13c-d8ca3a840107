package com.hualu.watermask.modules.watermaskphoto.service;

import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhoto;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.watermaskphoto.vo.*;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface WatermarkPhotoService extends IService<WatermarkPhoto>{

    /**
     * 创建水印照片
     *
     * @param originalPhoto 原始照片文件
     * @param watermarkPhoto 水印照片文件
     * @param photo 水印照片信息
     * @return 创建结果
     */
    RestResult<WatermarkPhoto> createWatermarkPhoto(
            MultipartFile originalPhoto,
            MultipartFile watermarkPhoto,
            WatermarkPhoto photo);

    /**
     * 创建水印照片并同步到用户团队
     *
     * @param originalPhoto 原始照片文件
     * @param watermarkPhoto 水印照片文件
     * @param photo 水印照片信息
     * @param userId 用户ID
     * @param teamIds 同步团队ID集合，逗号分隔，非必填。如果不填则按默认逻辑同步，如果填了则按指定团队同步
     * @return 创建结果
     */
    RestResult<WatermarkPhoto> createWatermarkPhotoWithSync(
            MultipartFile originalPhoto,
            MultipartFile watermarkPhoto,
            WatermarkPhoto photo,
            String userId,
            String teamIds);

    /**
     * 获取水印照片详情
     *
     * @param photoId 照片ID
     * @return 照片详情（包含基本信息和自定义内容）
     */
    RestResult<WatermarkPhoto> getPhotoDetail(String photoId);

    /**
     * 分页查询水印照片列表
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param queryParams 查询参数
     * @return 照片列表（包含基本信息和自定义内容）
     */
    RestResult<List<WatermarkPhotoListVO>> getPhotoList(long current, long size, Map<String, Object> queryParams);

    /**
     * 按照团队ID、日期查询照片，并按照创建人员和拍照地点分组（带分页）
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param teamId 团队ID
     * @param captureDate 拍摄日期
     * @param userIds 成员搜索字段，逗号分隔（可选）
     * @param childrenTeamIds 子部门ID，逗号分隔（可选）
     * @return 分组后的照片列表
     */
    RestResult<List<PhotoGroupVO>> getPhotoGroupsByTeam(long current, long size, String teamId, Date captureDate, String userIds, String childrenTeamIds);

    /**
     * 查询某一日期的照片日报统计
     *
     * @param teamId 团队ID
     * @param date 统计日期
     * @param current 当前页码
     * @param size 每页大小
     * @param userIds 成员搜索字段，逗号分隔（可选）
     * @param childrenTeamIds 子团队ID，逗号分隔（可选）
     * @return 照片日报统计信息列表
     */
    RestResult<List<PhotoDailySummaryVO>> getPhotoDailySummary(String teamId, Date date, long current, long size, String userIds, String childrenTeamIds);

    /**
     * 获取照片字段选项和模板自定义内容选项
     *
     * @param templateId 模板ID
     * @return 字段选项列表
     */
    RestResult<PhotoFieldOptionsVO> getPhotoFieldOptions(String templateId);

    /**
     * 导出照片台账（直接导出，同步操作）
     *
     * @param response HTTP响应对象
     * @param params 导出参数
     */
    void exportPhotoLedger(HttpServletResponse response, PhotoExportParamVO params);
    
    /**
     * 异步导出照片台账（先记录导出任务，再异步生成文件）
     *
     * @param params 导出参数
     * @param userId 用户ID
     * @param havPic 是否包含照片（true-有照片，false-无照片，默认true）。在非自定义导出属性时，如果选择无照片，则不导出原图和水印图；自定义属性下载时此参数无效
     * @return 导出任务ID
     */
    RestResult<String> asyncExportPhotoLedger(PhotoExportParamVO params, String userId, Boolean havPic);

    /**
     * 异步导出照片文件夹（按分类存放照片并打包成ZIP）
     *
     * @param params 导出参数
     * @param userId 用户ID
     * @param fileType 文件类型
     * @return 导出任务ID
     */
    RestResult<String> asyncExportPhotoFolder(PhotoExportFolderParamVO params, String userId, String fileType);



    /**
     * 导出照片文件夹（按分类存放照片并打包成ZIP）
     *
     * @param response HTTP响应对象
     * @param params 导出参数
     */
    void exportPhotoFolder(HttpServletResponse response, PhotoExportFolderParamVO params);

    /**
     * 获取所有不重复的拍摄地点列表（用于下拉选项）
     *
     * @return 拍摄地点列表，格式为 { id: "", name: '469县道', desc: '广东省云浮市云城区安塘街道' }
     */
    RestResult<List<Map<String, String>>> getLocationOptions(String teamId);

    /**
     * 获取照片台账
     *
     * @param teamId 团队ID
     * @param keywords 水印关键词搜索（水印名称）
     * @param startDate 开始日期（拍照时间）
     * @param endDate 结束日期（拍照时间）
     * @param userId 筛选人员
     * @param templateId 筛选水印（模板ID）
     * @param current 当前页码
     * @param size 每页大小
     * @return 照片台账数据（包含表头和数据内容）
     */
    RestResult<Map<String, Object>> getPhotoLedger(
            String teamId,
            String keywords,
            Date startDate,
            Date endDate,
            String userId,
            String templateId,
            Long current,
            Long size);
}
