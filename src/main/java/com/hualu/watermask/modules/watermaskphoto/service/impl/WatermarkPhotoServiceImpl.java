package com.hualu.watermask.modules.watermaskphoto.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.common.exception.BusinessException;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.record.entity.WsExportRecord;
import com.hualu.watermask.modules.record.service.WsExportRecordService;
import com.hualu.watermask.modules.role.entity.WsRole;
import com.hualu.watermask.modules.role.mapper.WsRoleMapper;
import com.hualu.watermask.modules.team.entity.WsTeam;
import com.hualu.watermask.modules.team.service.WsTeamService;
import com.hualu.watermask.modules.user.entity.WsPhotoTeams;
import com.hualu.watermask.modules.user.entity.WsUser;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import com.hualu.watermask.modules.user.mapper.WsUserMapper;
import com.hualu.watermask.modules.user.service.WsPhotoTeamsService;
import com.hualu.watermask.modules.user.service.WsUserService;
import com.hualu.watermask.modules.user.service.WsUserTeamsService;
import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhoto;
import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhotoCustom;
import com.hualu.watermask.modules.watermaskphoto.mapper.WatermarkPhotoMapper;
import com.hualu.watermask.modules.watermaskphoto.service.WatermarkPhotoCustomService;
import com.hualu.watermask.modules.watermaskphoto.service.WatermarkPhotoService;
import com.hualu.watermask.modules.watermaskphoto.utils.ExcelExportUtils;
import com.hualu.watermask.modules.watermaskphoto.utils.FileUtils;
import com.hualu.watermask.modules.watermaskphoto.utils.FileZipUtils;
import com.hualu.watermask.modules.watermaskphoto.utils.GeoUtils;
import com.hualu.watermask.modules.watermaskphoto.vo.PhotoDailySummaryVO;
import com.hualu.watermask.modules.watermaskphoto.vo.PhotoExportFolderParamVO;
import com.hualu.watermask.modules.watermaskphoto.vo.PhotoExportParamVO;
import com.hualu.watermask.modules.watermaskphoto.vo.PhotoFieldOptionsVO;
import com.hualu.watermask.modules.watermaskphoto.vo.PhotoGroupVO;
import com.hualu.watermask.modules.watermaskphoto.vo.WatermarkPhotoListVO;
import com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplate;
import com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplateContent;
import com.hualu.watermask.modules.watermasktemplate.service.WatermarkTemplateContentService;
import com.hualu.watermask.modules.watermasktemplate.service.WatermarkTemplateService;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

@Service
public class WatermarkPhotoServiceImpl extends ServiceImpl<WatermarkPhotoMapper, WatermarkPhoto> implements WatermarkPhotoService {

    private static final Logger log = LoggerFactory.getLogger(WatermarkPhotoServiceImpl.class);
    
    @Value("${export.base-path:/tmp/exports}")
    private String exportBasePath;

    @Autowired
    private WatermarkPhotoCustomService watermarkPhotoCustomService;

    @Autowired
    private WatermarkTemplateService watermarkTemplateService;

    @Autowired
    private WatermarkTemplateContentService watermarkTemplateContentService;

    @Autowired
    private WsUserTeamsService wsUserTeamsService;

    @Autowired
    private WsRoleMapper wsRoleMapper;

    @Autowired
    private WsUserMapper wsUserMapper;

    @Autowired
    private WsPhotoTeamsService wsPhotoTeamsService;

    @Autowired
    private WsUserService wsUserService;

    @Autowired
    private WsTeamService wsTeamService;
    
    @Autowired
    private WsExportRecordService wsExportRecordService;

    /**
     * 创建水印照片
     *
     * @param originalPhoto 原始照片文件
     * @param watermarkPhoto 水印照片文件
     * @param photo 水印照片信息
     * @return 创建结果
     */
    @Override
    public RestResult<WatermarkPhoto> createWatermarkPhoto(
            MultipartFile originalPhoto,
            MultipartFile watermarkPhoto,
            WatermarkPhoto photo) {
        // 上传原始照片
        List<MultipartFile> originalFiles = new ArrayList<>();
        originalFiles.add(originalPhoto);
        List<String> originalPhotoIds = FileUtils.uploadImage(originalFiles);
        if (CollUtil.isEmpty(originalPhotoIds)) {
            throw new BusinessException("广乐数据中心文件服务器异常");
        }

        // 上传水印照片
        List<MultipartFile> watermarkFiles = new ArrayList<>();
        watermarkFiles.add(watermarkPhoto);
        List<String> watermarkPhotoIds = FileUtils.uploadImage(watermarkFiles);
        if (CollUtil.isEmpty(watermarkPhotoIds)) {
            throw new BusinessException("广乐数据中心文件服务器异常");
        }

        // 设置照片ID和其他信息
        String userId = StpUtil.getLoginIdAsString();
        String photoId = UUID.randomUUID().toString().replace("-", "");
        photo.setPhotoId(photoId);
        photo.setCreatedBy(userId);
        photo.setCreatedTime(new Date());
        WsUser user = wsUserService.getById(userId);
        photo.setPhotographer(user.getUsername());

        // 如果照片编码为空，则生成照片编码：当前日期（yyyyMMddHHmmssSSS格式）+ 10位随机数字
        if(StringUtils.isBlank(photo.getPhotoCode())){
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String dateStr = sdf.format(new Date());
            String randomNum = String.format("%010d", ThreadLocalRandom.current().nextInt(1000000000, Integer.MAX_VALUE));
            String photoCode = dateStr + randomNum;
            photo.setPhotoCode(photoCode);
        }

        // 设置原图ID和水印图ID
        if (originalPhotoIds != null && !originalPhotoIds.isEmpty()) {
            photo.setOriginalPhotoId(originalPhotoIds.get(0));
        }

        if (watermarkPhotoIds != null && !watermarkPhotoIds.isEmpty()) {
            photo.setWatermarkPhotoId(watermarkPhotoIds.get(0));
        }

        String templateId = photo.getTemplateId();
        if(StringUtils.isNotBlank(templateId)){
            WatermarkTemplate template = watermarkTemplateService.getById(templateId);
            if(template != null){
                photo.setIcon(template.getIcon());
            }
        }

        // 保存水印照片信息
        boolean saveResult = this.save(photo);

        // 处理自定义内容
        if (saveResult) {
            List<WatermarkPhotoCustom> customContents = photo.getWatermarkPhotoCustoms();

            // 如果前端传递了自定义内容，则直接使用
            if (customContents != null && !customContents.isEmpty()) {
                // 为每个自定义内容设置ID和照片ID
                for (WatermarkPhotoCustom custom : customContents) {
                    custom.setCustomId(UUID.randomUUID().toString().replace("-", ""));
                    custom.setPhotoId(photoId);
                }

                // 批量保存自定义内容
                watermarkPhotoCustomService.saveBatch(customContents);
            }
        }

        return RestResult.success(photo);
    }

    /**
     * 获取水印照片详情
     *
     * @param photoId 照片ID
     * @return 照片详情（包含基本信息和自定义内容）
     */
    @Override
    public RestResult<WatermarkPhoto> getPhotoDetail(String photoId) {
        try {
            if (StringUtils.isBlank(photoId)) {
                return RestResult.error("照片ID不能为空");
            }

            // 查询照片基本信息
            WatermarkPhoto photo = this.getById(photoId);
            if (photo == null) {
                return RestResult.error("照片不存在");
            }

            // 查询照片自定义内容
            LambdaQueryWrapper<WatermarkPhotoCustom> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WatermarkPhotoCustom::getPhotoId, photoId)
                    .orderByAsc(WatermarkPhotoCustom::getSortOrder);
            List<WatermarkPhotoCustom> customContents = watermarkPhotoCustomService.list(queryWrapper);

            // 设置自定义内容
            photo.setWatermarkPhotoCustoms(customContents);

            return RestResult.success(photo);
        } catch (Exception e) {
            return RestResult.error("获取照片详情失败：" + e.getMessage());
        }
    }

    /**
     * 分页查询水印照片列表
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param queryParams 查询参数
     * @return 照片列表（包含基本信息和自定义内容）
     */
    @Override
    public RestResult<List<WatermarkPhotoListVO>> getPhotoList(long current, long size, Map<String, Object> queryParams) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();

            // 添加查询条件
            if (queryParams != null) {
                // 多关键字搜索（逗号分隔）
                if (queryParams.containsKey("keywords") && StringUtils.isNotBlank((String) queryParams.get("keywords"))) {
                    String keywords = (String) queryParams.get("keywords");
                    String[] keywordArray = keywords.split(",");

                    // 使用OR连接多个关键字条件
                    queryWrapper.and(wrapper -> {
                        for (String keyword : keywordArray) {
                            String trimmedKeyword = keyword.trim();
                            if (StringUtils.isNotBlank(trimmedKeyword)) {
                                wrapper.or(w -> w.like(WatermarkPhoto::getPhotoCode, trimmedKeyword)
                                        .or().like(WatermarkPhoto::getPhotographer, trimmedKeyword)
                                        .or().like(WatermarkPhoto::getLocation, trimmedKeyword)
                                        .or().like(WatermarkPhoto::getReferenceText, trimmedKeyword));
                            }
                        }
                    });
                }

                // 拍摄人员列表（逗号分隔）
                if (queryParams.containsKey("photographers") && StringUtils.isNotBlank((String) queryParams.get("photographers"))) {
                    String photographers = (String) queryParams.get("photographers");
                    String[] photographerArray = photographers.split(",");
                    List<String> photographerList = Arrays.stream(photographerArray)
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());

                    if (!photographerList.isEmpty()) {
                        queryWrapper.in(WatermarkPhoto::getPhotographer, photographerList);
                    }
                }

                // 模板ID列表（逗号分隔）
                if (queryParams.containsKey("templateIds") && StringUtils.isNotBlank((String) queryParams.get("templateIds"))) {
                    String templateIds = (String) queryParams.get("templateIds");
                    String[] templateIdArray = templateIds.split(",");
                    List<String> templateIdList = Arrays.stream(templateIdArray)
                            .map(String::trim)
                            .filter(StringUtils::isNotBlank)
                            .collect(Collectors.toList());

                    if (!templateIdList.isEmpty()) {
                        queryWrapper.in(WatermarkPhoto::getTemplateId, templateIdList);
                    }
                }

                // 拍摄地点查询
                if (queryParams.containsKey("location") && StringUtils.isNotBlank((String) queryParams.get("location"))) {
                    queryWrapper.like(WatermarkPhoto::getLocation, queryParams.get("location"));
                }

                // 照片编号查询
                if (queryParams.containsKey("photoCode") && StringUtils.isNotBlank((String) queryParams.get("photoCode"))) {
                    queryWrapper.like(WatermarkPhoto::getPhotoCode, queryParams.get("photoCode"));
                }

                // 拍摄人查询（兼容旧参数）
                if (queryParams.containsKey("photographer") && StringUtils.isNotBlank((String) queryParams.get("photographer"))) {
                    queryWrapper.like(WatermarkPhoto::getPhotographer, queryParams.get("photographer"));
                }

                // 拍摄时间范围查询
                if (queryParams.containsKey("startTime") && queryParams.get("startTime") != null) {
                    queryWrapper.ge(WatermarkPhoto::getCaptureTime, queryParams.get("startTime"));
                }
                if (queryParams.containsKey("endTime") && queryParams.get("endTime") != null) {
                    queryWrapper.le(WatermarkPhoto::getCaptureTime, queryParams.get("endTime"));
                }
            }

            // 默认按创建时间倒序排序
            queryWrapper.orderByDesc(WatermarkPhoto::getCreatedTime);

            // 创建分页对象并执行分页查询
            Page<WatermarkPhoto> page = new Page<>(current, size);
            Page<WatermarkPhoto> result = this.page(page, queryWrapper);

            // 如果没有数据，返回空列表
            if (result.getRecords().isEmpty()) {
                return RestResult.success(new ArrayList<>(), 0, current, size);
            }

            List<WatermarkPhotoListVO> voList = new ArrayList<>();

            // 获取所有照片ID
            List<String> photoIds = new ArrayList<>();
            for (WatermarkPhoto photo : result.getRecords()) {
                photoIds.add(photo.getPhotoId());
            }

            // 批量查询所有照片的自定义内容
            LambdaQueryWrapper<WatermarkPhotoCustom> customQueryWrapper = new LambdaQueryWrapper<>();
            customQueryWrapper.in(WatermarkPhotoCustom::getPhotoId, photoIds)
                    .orderByAsc(WatermarkPhotoCustom::getSortOrder);
            List<WatermarkPhotoCustom> allCustomContents = watermarkPhotoCustomService.list(customQueryWrapper);

            // 按照照片ID分组自定义内容
            Map<String, List<WatermarkPhotoCustom>> photoCustomMap = new HashMap<>();
            for (WatermarkPhotoCustom custom : allCustomContents) {
                if (!photoCustomMap.containsKey(custom.getPhotoId())) {
                    photoCustomMap.put(custom.getPhotoId(), new ArrayList<>());
                }
                photoCustomMap.get(custom.getPhotoId()).add(custom);
            }

            // 组装VO对象
            for (WatermarkPhoto photo : result.getRecords()) {
                WatermarkPhotoListVO vo = new WatermarkPhotoListVO();
                BeanUtils.copyProperties(photo, vo);

                // 设置自定义字段
                List<WatermarkPhotoCustom> customContents = photoCustomMap.get(photo.getPhotoId());
                if (customContents != null && !customContents.isEmpty()) {
                    for (WatermarkPhotoCustom custom : customContents) {
                        vo.addCustomField(custom.getTitle(), custom.getContent());
                    }
                }

                voList.add(vo);
            }

            // 返回带有分页信息的结果
            return RestResult.success(voList, result.getTotal(), current, size);
        } catch (Exception e) {
            return RestResult.error("查询照片列表失败：" + e.getMessage());
        }
    }

    /**
     * 按照团队ID、日期查询照片，并按照创建人员和拍照地点分组（带分页）
     *
     * @param current 当前页码
     * @param size 每页大小
     * @param teamId 团队ID
     * @param captureDate 拍摄日期
     * @param userIdsParam 成员搜索字段，逗号分隔（可选）
     * @param childrenTeamIds 子部门ID，逗号分隔（可选）
     * @return 分组后的照片列表
     */
    @Override
    public RestResult<List<PhotoGroupVO>> getPhotoGroupsByTeam(long current, long size, String teamId, Date captureDate, String userIdsParam, String childrenTeamIds) {
        try {
            if (StringUtils.isBlank(teamId)) {
                return RestResult.error("团队ID不能为空");
            }

            if (captureDate == null) {
                captureDate = new Date(); // 默认为当天
            }

            // 获取需要查询的团队ID列表
            List<String> teamIdsToQuery = new ArrayList<>();
            teamIdsToQuery.add(teamId); // 添加主团队ID

            // 处理子部门ID参数
            if (StringUtils.isNotBlank(childrenTeamIds)) {
                String[] childTeamIdArray = childrenTeamIds.split(",");
                for (String childTeamId : childTeamIdArray) {
                    if (StringUtils.isNotBlank(childTeamId.trim())) {
                        teamIdsToQuery.add(childTeamId.trim());
                    }
                }
            }

            // 获取所有相关团队下的用户ID
            List<String> allUserIds = new ArrayList<>();
            for (String tId : teamIdsToQuery) {
                List<String> teamUserIds = wsUserTeamsService.getUserIdsByTeamId(tId);
                if (teamUserIds != null && !teamUserIds.isEmpty()) {
                    allUserIds.addAll(teamUserIds);
                }
            }

            // 去重
            allUserIds = allUserIds.stream().distinct().collect(Collectors.toList());

            // 处理成员搜索参数
            List<String> finalUserIds = allUserIds;
            if (StringUtils.isNotBlank(userIdsParam)) {
                String[] userIdArray = userIdsParam.split(",");
                List<String> specifiedUserIds = new ArrayList<>();
                for (String userId : userIdArray) {
                    if (StringUtils.isNotBlank(userId.trim())) {
                        specifiedUserIds.add(userId.trim());
                    }
                }

                // 取交集：既在团队中，又在指定的用户ID列表中
                finalUserIds = allUserIds.stream()
                        .filter(specifiedUserIds::contains)
                        .collect(Collectors.toList());
            }

            if (finalUserIds == null || finalUserIds.isEmpty()) {
                return RestResult.error("没有找到符合条件的用户");
            }

            // 设置日期范围（当天的开始和结束）
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(captureDate);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date startDate = calendar.getTime();

            calendar.add(Calendar.DAY_OF_MONTH, 1);
            calendar.add(Calendar.MILLISECOND, -1);
            Date endDate = calendar.getTime();

            // 查询照片 - 先按拍摄时间倒序排列，然后再进行分组计算
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(WatermarkPhoto::getCreatedBy, finalUserIds)
                    .between(WatermarkPhoto::getCaptureTime, startDate, endDate)
                    .orderByDesc(WatermarkPhoto::getCaptureTime); // 优先按拍摄时间倒序

            List<WatermarkPhoto> photos = this.list(queryWrapper);

            if (photos == null || photos.isEmpty()) {
                return RestResult.success(new ArrayList<>(), 0, current, size);
            }

            // 获取所有涉及的用户ID
            Set<String> photoUserIds = photos.stream()
                    .map(WatermarkPhoto::getCreatedBy)
                    .collect(Collectors.toSet());

            // 创建用户ID到角色名称的直接映射
            Map<String, String> userRoleNameMap = new HashMap<>();
            
            // 批量查询所有用户在团队中的角色关系
            if (!photoUserIds.isEmpty()) {
                LambdaQueryWrapper<WsUserTeams> teamQuery = new LambdaQueryWrapper<>();
                teamQuery.eq(WsUserTeams::getTeamId, teamId)
                        .in(WsUserTeams::getUserId, photoUserIds)
                        .eq(WsUserTeams::getStatus, new BigDecimal(1));
                List<WsUserTeams> allUserTeams = wsUserTeamsService.list(teamQuery);
                
                // 获取所有涉及的角色ID
                Set<String> roleIds = allUserTeams.stream()
                        .map(WsUserTeams::getRoleId)
                        .filter(StringUtils::isNotBlank)
                        .collect(Collectors.toSet());
                
                // 批量查询角色信息
                Map<String, String> roleNameMap = new HashMap<>();
                if (!roleIds.isEmpty()) {
                    LambdaQueryWrapper<WsRole> roleQuery = new LambdaQueryWrapper<>();
                    roleQuery.in(WsRole::getRoleId, roleIds);
                    List<WsRole> roles = wsRoleMapper.selectList(roleQuery);
                    
                    // 建立角色ID到角色名称的映射
                    roleNameMap = roles.stream()
                            .collect(Collectors.toMap(WsRole::getRoleId, tm -> ObjectUtil.defaultIfNull(tm.getRoleName(), ""), (k1, k2) -> k1));
                }
                
                // 直接创建用户ID到角色名称的映射
                for (WsUserTeams userTeam : allUserTeams) {
                    String userId = userTeam.getUserId();
                    String roleId = userTeam.getRoleId();
                    String roleName = "";
                    
                    if (StringUtils.isNotBlank(roleId)) {
                        if ("mainAdmin".equals(roleId)) {
                            roleName = "主管理员";
                        } else {
                            roleName = roleNameMap.getOrDefault(roleId, "");
                        }
                    }
                    
                    userRoleNameMap.put(userId, roleName);
                }
            }

            // 按照拍摄人员和拍摄地点分组
            Map<String, Map<String, List<WatermarkPhoto>>> groupedPhotos = photos.stream()
                    .collect(Collectors.groupingBy(WatermarkPhoto::getPhotographer,
                            Collectors.groupingBy(WatermarkPhoto::getLocation)));

            List<PhotoGroupVO> allGroups = new ArrayList<>();

            // 获取每个拍摄人员的历史最后一次拍照记录（包含当天之前的记录）
            Map<String, WatermarkPhoto> lastPhotoByPhotographer = getLastPhotoByPhotographer(finalUserIds, startDate);

            // 用于记录每个拍摄人员在当前处理过程中的最后一次拍照记录
            Map<String, WatermarkPhoto> currentLastPhotoByPhotographer = new HashMap<>();

            // 遍历分组结果
            for (String photographer : groupedPhotos.keySet()) {
                Map<String, List<WatermarkPhoto>> locationGroups = groupedPhotos.get(photographer);

                for (String location : locationGroups.keySet()) {
                    List<WatermarkPhoto> locationPhotos = locationGroups.get(location);

                    if (locationPhotos != null && !locationPhotos.isEmpty()) {
                        // 确保分组内的照片按拍摄时间倒序排列
                        locationPhotos.sort((p1, p2) -> {
                            if (p1.getCaptureTime() == null && p2.getCaptureTime() == null) return 0;
                            if (p1.getCaptureTime() == null) return 1;
                            if (p2.getCaptureTime() == null) return -1;
                            return p2.getCaptureTime().compareTo(p1.getCaptureTime()); // 倒序
                        });

                        PhotoGroupVO groupVO = new PhotoGroupVO();
                        groupVO.setPhotographer(photographer);
                        groupVO.setLocation(location);
                        groupVO.setPhotos(locationPhotos);
                        groupVO.setPhotoCount(locationPhotos.size());

                        // 获取该地点的第一张照片的经纬度
                        WatermarkPhoto firstPhoto = locationPhotos.get(0);
                        groupVO.setLongitude(firstPhoto.getLongitude());
                        groupVO.setLatitude(firstPhoto.getLatitude());

                        // 设置最早和最晚拍摄时间
                        Date earliestTime = locationPhotos.stream()
                                .map(WatermarkPhoto::getCaptureTime)
                                .min(Date::compareTo)
                                .orElse(null);
                        Date latestTime = locationPhotos.stream()
                                .map(WatermarkPhoto::getCaptureTime)
                                .max(Date::compareTo)
                                .orElse(null);

                        groupVO.setEarliestCaptureTime(earliestTime);
                        groupVO.setLatestCaptureTime(latestTime);

                        // 计算与上一次拍照地点的距离和时间差
                        // 优先使用当天数据中的上一次记录，如果没有则使用历史记录
                        WatermarkPhoto lastPhoto = currentLastPhotoByPhotographer.get(photographer);
                        if (lastPhoto == null) {
                            lastPhoto = lastPhotoByPhotographer.get(photographer);
                        }

                        if (lastPhoto != null && lastPhoto.getCaptureTime() != null && latestTime != null) {
                            // 计算距离（米）并格式化
                            double distanceInMeters = GeoUtils.calculateDistance(
                                    lastPhoto.getLongitude(), lastPhoto.getLatitude(),
                                    firstPhoto.getLongitude(), firstPhoto.getLatitude()
                            );
                            double formattedDistance = formatDistanceValue(distanceInMeters);
                            groupVO.setDistanceFromLastLocation(formattedDistance);

                            // 计算时间差（分钟）- 取绝对值确保为正数
                            long timeDiff = Math.abs(TimeUnit.MILLISECONDS.toMinutes(
                                    latestTime.getTime() - lastPhoto.getCaptureTime().getTime()
                            ));
                            groupVO.setTimeDiffFromLastCapture(timeDiff);
                        } else {
                            groupVO.setDistanceFromLastLocation(0.0);
                            groupVO.setTimeDiffFromLastCapture(0L);
                        }

                        // 设置角色信息 - 直接从映射中获取
                        String createdBy = firstPhoto.getCreatedBy();
                        String roleName = userRoleNameMap.getOrDefault(createdBy, "");
                        groupVO.setPhotographerRole(roleName);

                        // 更新该拍摄人员在当前处理过程中的最后一次拍照记录（时间倒序后，第一张是最新的）
                        currentLastPhotoByPhotographer.put(photographer, locationPhotos.get(0));

                        allGroups.add(groupVO);
                    }
                }
            }

            // 先按照拍摄时间正序排序，用于正确计算"上一次"的距离和时间差
            allGroups.sort(Comparator.comparing(PhotoGroupVO::getLatestCaptureTime,
                    Comparator.nullsLast(Comparator.naturalOrder())));

            // 重新计算距离和时间差（基于正确的时间顺序）
            recalculateDistanceAndTimeDiff(allGroups);

            // 最后按照拍摄时间倒序排序（最新的分组在前）
            allGroups.sort(Comparator.comparing(PhotoGroupVO::getLatestCaptureTime,
                    Comparator.nullsLast(Comparator.reverseOrder())));

            // 计算总记录数
            long total = allGroups.size();

            // 如果没有分组结果，返回空列表
            if (total == 0) {
                return RestResult.success(new ArrayList<>(), 0, current, size);
            }

            // 计算分页数据
            int fromIndex = (int) ((current - 1) * size);
            if (fromIndex >= total) {
                // 如果起始索引超出范围，返回空列表
                return RestResult.success(new ArrayList<>(), total, current, size);
            }

            int toIndex = Math.min((int) (fromIndex + size), allGroups.size());
            List<PhotoGroupVO> pagedList = allGroups.subList(fromIndex, toIndex);

            // 返回带有分页信息的结果
            return RestResult.success(pagedList, total, current, size);
        } catch (Exception e) {
            log.error("查询照片分组失败: {}", e.getMessage(), e);
            return RestResult.error("查询照片分组失败：" + e.getMessage());
        }
    }

    /**
     * 查询某一日期的照片日报统计
     *
     * @param teamId 团队ID
     * @param date 统计日期
     * @param current 当前页码
     * @param size 每页大小
     * @param userIdsParam 成员搜索字段，逗号分隔（可选）
     * @param childrenTeamIds 子团队ID，逗号分隔（可选）
     * @return 照片日报统计信息列表
     */
    @Override
    public RestResult<List<PhotoDailySummaryVO>> getPhotoDailySummary(String teamId, Date date, long current, long size, String userIdsParam, String childrenTeamIds) {
        try {
            if (StringUtils.isBlank(teamId)) {
                return RestResult.error("团队ID不能为空");
            }

            if (date == null) {
                date = new Date(); // 默认为当前时间
            }

            // 获取需要查询的团队ID列表
            List<String> teamIdsToQuery = new ArrayList<>();
            teamIdsToQuery.add(teamId); // 添加主团队ID

            // 处理子团队ID参数
            if (StringUtils.isNotBlank(childrenTeamIds)) {
                String[] childTeamIdArray = childrenTeamIds.split(",");
                for (String childTeamId : childTeamIdArray) {
                    if (StringUtils.isNotBlank(childTeamId.trim())) {
                        teamIdsToQuery.add(childTeamId.trim());
                    }
                }
            }

            // 获取所有相关团队下的用户ID
            List<String> allUserIds = new ArrayList<>();
            for (String tId : teamIdsToQuery) {
                List<String> teamUserIds = wsUserTeamsService.getUserIdsByTeamId(tId);
                if (teamUserIds != null && !teamUserIds.isEmpty()) {
                    allUserIds.addAll(teamUserIds);
                }
            }

            // 去重
            allUserIds = allUserIds.stream().distinct().collect(Collectors.toList());

            // 处理成员搜索参数
            List<String> finalUserIds = allUserIds;
            if (StringUtils.isNotBlank(userIdsParam)) {
                String[] userIdArray = userIdsParam.split(",");
                List<String> specifiedUserIds = new ArrayList<>();
                for (String userId : userIdArray) {
                    if (StringUtils.isNotBlank(userId.trim())) {
                        specifiedUserIds.add(userId.trim());
                    }
                }

                // 取交集：既在团队中，又在指定的用户ID列表中
                finalUserIds = allUserIds.stream()
                        .filter(specifiedUserIds::contains)
                        .collect(Collectors.toList());
            }

            if (finalUserIds == null || finalUserIds.isEmpty()) {
                return RestResult.error("没有找到符合条件的用户");
            }

            // 设置日期范围（当天的开始和结束）
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date startDate = calendar.getTime();

            calendar.add(Calendar.DAY_OF_MONTH, 1);
            calendar.add(Calendar.MILLISECOND, -1);
            Date endDate = calendar.getTime();

            // 查询团队下所有用户当天的照片
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(WatermarkPhoto::getCreatedBy, finalUserIds)
                    .between(WatermarkPhoto::getCaptureTime, startDate, endDate)
                    .orderByAsc(WatermarkPhoto::getCaptureTime);

            List<WatermarkPhoto> photos = this.list(queryWrapper);

            // 如果没有照片，返回空列表
            if (photos == null || photos.isEmpty()) {
                return RestResult.success(new ArrayList<>(), 0, current, size);
            }

            // 按照用户ID分组统计照片
            Map<String, List<WatermarkPhoto>> photosByUser = photos.stream()
                    .collect(Collectors.groupingBy(WatermarkPhoto::getCreatedBy));

            // 获取所有涉及的用户ID
            Set<String> userIdSet = photosByUser.keySet();

            // 批量查询所有用户信息
            List<WsUser> allUsers = new ArrayList<>();
            if (!userIdSet.isEmpty()) {
                LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
                userQuery.in(WsUser::getUserId, userIdSet);
                allUsers = wsUserMapper.selectList(userQuery);
            }
            Map<String, WsUser> userMap = allUsers.stream()
                    .collect(Collectors.toMap(WsUser::getUserId, user -> user, (k1, k2) -> k1));

            // 批量查询所有用户在团队中的角色关系
            List<WsUserTeams> allUserTeams = new ArrayList<>();
            if (!userIdSet.isEmpty()) {
                LambdaQueryWrapper<WsUserTeams> teamQuery = new LambdaQueryWrapper<>();
                teamQuery.eq(WsUserTeams::getTeamId, teamId)
                        .in(WsUserTeams::getUserId, userIdSet)
                        .eq(WsUserTeams::getStatus, new BigDecimal(1));
                allUserTeams = wsUserTeamsService.list(teamQuery);
            }
            Map<String, WsUserTeams> userTeamMap = allUserTeams.stream()
                    .collect(Collectors.toMap(WsUserTeams::getUserId, ut -> ut, (k1, k2) -> k1));

            // 获取所有涉及的角色ID
            Set<String> roleIds = allUserTeams.stream()
                    .map(WsUserTeams::getRoleId)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toSet());

            // 批量查询角色信息
            Map<String, WsRole> roleMap = new HashMap<>();
            if (!roleIds.isEmpty()) {
                LambdaQueryWrapper<WsRole> roleQuery = new LambdaQueryWrapper<>();
                roleQuery.in(WsRole::getRoleId, roleIds);
                List<WsRole> roles = wsRoleMapper.selectList(roleQuery);
                roleMap = roles.stream()
                        .collect(Collectors.toMap(WsRole::getRoleId, role -> role, (k1, k2) -> k1));
            }

            // 批量查询团队信息
            Map<String, WsTeam> teamMap = new HashMap<>();
            if (!teamIdsToQuery.isEmpty()) {
                LambdaQueryWrapper<WsTeam> teamQuery = new LambdaQueryWrapper<>();
                teamQuery.in(WsTeam::getTeamId, teamIdsToQuery);
                List<WsTeam> teams = wsTeamService.list(teamQuery);
                teamMap = teams.stream()
                        .collect(Collectors.toMap(WsTeam::getTeamId, team -> team, (k1, k2) -> k1));
            }

            // 创建结果列表
            List<PhotoDailySummaryVO> resultList = new ArrayList<>();

            // 多级分组：用户 -> 团队 -> 地点 -> 时间（5分钟内）
            Map<String, Map<String, Map<String, Map<String, List<WatermarkPhoto>>>>> groupedPhotos =
                    groupPhotosByMultipleCriteria(photos, teamIdsToQuery);

            // 为每个分组创建PhotoDailySummaryVO
            for (Map.Entry<String, Map<String, Map<String, Map<String, List<WatermarkPhoto>>>>> userEntry : groupedPhotos.entrySet()) {
                String userId = userEntry.getKey();
                Map<String, Map<String, Map<String, List<WatermarkPhoto>>>> userTimeGroups = userEntry.getValue();

                for (Map.Entry<String, Map<String, Map<String, List<WatermarkPhoto>>>> timeEntry : userTimeGroups.entrySet()) {
                    String timeGroupKey = timeEntry.getKey();
                    Map<String, Map<String, List<WatermarkPhoto>>> timeTeamGroups = timeEntry.getValue();

                    for (Map.Entry<String, Map<String, List<WatermarkPhoto>>> teamEntry : timeTeamGroups.entrySet()) {
                        String currentTeamId = teamEntry.getKey();
                        Map<String, List<WatermarkPhoto>> teamLocationGroups = teamEntry.getValue();

                        for (Map.Entry<String, List<WatermarkPhoto>> locationEntry : teamLocationGroups.entrySet()) {
                            String location = locationEntry.getKey();
                            List<WatermarkPhoto> groupPhotos = locationEntry.getValue();

                            if (groupPhotos != null && !groupPhotos.isEmpty()) {
                                PhotoDailySummaryVO summaryVO = new PhotoDailySummaryVO();
                                summaryVO.setStatisticsDate(date);
                                summaryVO.setTeamId(currentTeamId);
                                summaryVO.setLocation(location);

                                // 为时间距离组合生成更有意义的键
                                String meaningfulTimeKey = generateMeaningfulTimeKey(groupPhotos);
                                summaryVO.setTimeGroupKey(meaningfulTimeKey);

                                // 设置照片总数
                                summaryVO.setTotalPhotos(groupPhotos.size());

                                // 获取最早和最晚的照片时间
                                Date earliestTime = groupPhotos.stream()
                                        .map(WatermarkPhoto::getCaptureTime)
                                        .min(Date::compareTo)
                                        .orElse(null);

                                Date latestTime = groupPhotos.stream()
                                        .map(WatermarkPhoto::getCaptureTime)
                                        .max(Date::compareTo)
                                        .orElse(null);

                                summaryVO.setStartTime(earliestTime);
                                summaryVO.setEndTime(latestTime);

                                // 计算工时（小时，保留一位小数）
                                if (earliestTime != null && latestTime != null) {
                                    long timeDiffMillis = latestTime.getTime() - earliestTime.getTime();
                                    double hours = (double) timeDiffMillis / (1000 * 60 * 60); // 转换为小时
                                    BigDecimal workingHours = new BigDecimal(hours).setScale(1, RoundingMode.HALF_UP);
                                    summaryVO.setWorkingHours(workingHours);

                                    // 设置格式化的工时显示（带单位）
                                    String formattedWorkingTime = formatWorkingTimeWithUnit(timeDiffMillis);
                                    summaryVO.setFormattedWorkingTime(formattedWorkingTime);
                                } else {
                                    summaryVO.setWorkingHours(BigDecimal.ZERO);
                                    summaryVO.setFormattedWorkingTime("0分钟");
                                }

                                // 设置用户ID
                                summaryVO.setUserId(userId);

                                // 设置拍摄人员信息
                                WsUser user = userMap.get(userId);
                                String userName = "";
                                if (user != null) {
                                    userName = user.getUsername();
                                    summaryVO.setName(userName);
                                    summaryVO.setPhotographerName(userName); // 保持向后兼容
                                }

                                // 设置团队名称
                                WsTeam team = teamMap.get(currentTeamId);
                                if (team != null) {
                                    summaryVO.setTeamName(team.getTeamName());
                                }

                                // 设置角色信息
                                String roleName = ""; // 默认为空
                                WsUserTeams userTeam = userTeamMap.get(userId);
                                if (userTeam != null && StringUtils.isNotBlank(userTeam.getRoleId())) {
                                    String roleId = userTeam.getRoleId();
                                    if ("mainAdmin".equals(roleId)) {
                                        roleName = "主管理员";
                                    } else {
                                        WsRole role = roleMap.get(roleId);
                                        if (role != null) {
                                            roleName = role.getRoleName();
                                        }
                                    }
                                }
                                summaryVO.setRole(roleName);
                                summaryVO.setPhotographerRole(roleName); // 保持向后兼容

                                // 设置照片数量
                                summaryVO.setPhotoCount(groupPhotos.size());

                                // 对分组的照片按时间倒序排列
                                groupPhotos.sort((p1, p2) -> {
                                    if (p1.getCaptureTime() == null && p2.getCaptureTime() == null) return 0;
                                    if (p1.getCaptureTime() == null) return 1;
                                    if (p2.getCaptureTime() == null) return -1;
                                    return p2.getCaptureTime().compareTo(p1.getCaptureTime()); // 倒序
                                });

                                summaryVO.setPhotos(groupPhotos);

                                // 添加到结果列表
                                resultList.add(summaryVO);
                            }
                        }
                    }
                }
            }

            // 对结果列表进行多级排序：日期优先（最新在前） -> 人员 -> 地点 -> 团队
            resultList.sort((s1, s2) -> {
                // 第一级：按日期倒序（最新的在前）
                Date date1 = s1.getEndTime() != null ? s1.getEndTime() : s1.getStartTime();
                Date date2 = s2.getEndTime() != null ? s2.getEndTime() : s2.getStartTime();
                if (date1 != null && date2 != null) {
                    int dateCompare = date2.compareTo(date1); // 倒序
                    if (dateCompare != 0) return dateCompare;
                }

                // 第二级：按人员名倒序
                String name1 = s1.getName() != null ? s1.getName() : "";
                String name2 = s2.getName() != null ? s2.getName() : "";
                int nameCompare = name2.compareTo(name1);
                if (nameCompare != 0) return nameCompare;

                // 第三级：按地点倒序
                String location1 = s1.getLocation() != null ? s1.getLocation() : "";
                String location2 = s2.getLocation() != null ? s2.getLocation() : "";
                int locationCompare = location2.compareTo(location1);
                if (locationCompare != 0) return locationCompare;

                // 第四级：按团队名倒序
                String team1 = s1.getTeamName() != null ? s1.getTeamName() : "";
                String team2 = s2.getTeamName() != null ? s2.getTeamName() : "";
                return team2.compareTo(team1);
            });

            // 排序后重新计算距离和时间差（包含历史数据）
            calculateDistanceAndTimeDiffWithHistory(resultList, finalUserIds, startDate);

            // 对结果列表进行分页处理
            long total = resultList.size();

            // 计算起始和结束索引
            int fromIndex = (int) ((current - 1) * size);
            if (fromIndex >= total) {
                // 如果起始索引超出范围，返回空列表
                return RestResult.success(new ArrayList<>(), total, current, size);
            }

            int toIndex = Math.min((int) (fromIndex + size), resultList.size());

            // 获取分页数据
            List<PhotoDailySummaryVO> pagedList = resultList.subList(fromIndex, toIndex);

            // 返回带有分页信息的结果
            return RestResult.success(pagedList, total, current, size);
        } catch (Exception e) {
            log.error("查询照片日报统计失败: {}", e.getMessage(), e);
            return RestResult.error("查询照片日报统计失败：" + e.getMessage());
        }
    }

    /**
     * 获取照片字段选项和模板自定义内容选项
     *
     * @param templateId 模板ID
     * @return 字段选项列表
     */
    @Override
    public RestResult<PhotoFieldOptionsVO> getPhotoFieldOptions(String templateId) {
        try {
            PhotoFieldOptionsVO optionsVO = new PhotoFieldOptionsVO();

            // 设置固定字段选项
            List<PhotoFieldOptionsVO.FieldOption> allOptions = new ArrayList<>();
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("PHOTO_CODE", "照片编号", "照片编号/编码"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("CAPTURE_TIME", "拍摄时间", "照片拍摄时间（精确到秒）"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("WEATHER", "天气状况", "天气状况描述"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("LOCATION", "拍摄地点", "拍摄地点描述"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("ELEVATION", "海拔高度", "海拔高度（单位：米，保留2位小数）"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("AZIMUTH", "方位角", "方位角（0-360度，保留3位小数）"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("SPEED", "移动速度", "移动速度（单位：km/h，保留3位小数）"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("LONGITUDE", "经度", "经度值(精度:小数点后8位)"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("LATITUDE", "纬度", "纬度值(精度:小数点后8位)"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("PHOTOGRAPHER", "拍摄人", "拍摄人姓名"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("REFERENCE_TEXT", "参考文字", "参考文字描述"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("ORIGINAL_PHOTO_ID", "原图", "原始图片"));
            allOptions.add(new PhotoFieldOptionsVO.FieldOption("WATERMARK_PHOTO_ID", "水印图", "带水印图片"));

            // 如果提供了模板ID，则获取模板自定义内容选项并添加到同一列表
            if (StringUtils.isNotBlank(templateId)) {
                RestResult<List<WatermarkTemplateContent>> templateContents =
                        watermarkTemplateContentService.getContentByTemplateId(templateId);

                if (templateContents != null && templateContents.getData() != null) {
                    // 将模板自定义字段添加到同一个列表中
                    for (WatermarkTemplateContent content : templateContents.getData()) {
                        allOptions.add(new PhotoFieldOptionsVO.FieldOption(
                                "CUSTOM_" + content.getContentId(),
                                content.getTitle(),
                                "模板自定义字段：" + content.getTitle(),
                                content.getFontColor(),
                                content.getBackgroundColor()));
                    }
                }
            }

            // 设置所有字段到固定选项中，不再区分固定和自定义
            optionsVO.setFixedOptions(allOptions);
            // 保持兼容性，避免其他地方引用templateOptions出错
            optionsVO.setTemplateOptions(new ArrayList<>());

            return RestResult.success(optionsVO);
        } catch (Exception e) {
            return RestResult.error("获取照片字段选项失败：" + e.getMessage());
        }
    }

    /**
     * 获取照片台账
     *
     * @param teamId 团队ID
     * @param keywords 水印关键词搜索（水印名称）
     * @param startDate 开始日期（拍照时间）
     * @param endDate 结束日期（拍照时间）
     * @param userId 筛选人员
     * @param templateId 筛选水印（模板ID）
     * @param current 当前页码
     * @param size 每页大小
     * @return 照片台账数据（包含表头和数据内容）
     */
    @Override
    public RestResult<Map<String, Object>> getPhotoLedger(
            String teamId,
            String keywords,
            Date startDate,
            Date endDate,
            String userId,
            String templateId,
            Long current,
            Long size) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();

            // 只查询未删除的照片
            queryWrapper.eq(WatermarkPhoto::getDelFlag, new BigDecimal(0));

            // 根据团队ID筛选照片
            if (StringUtils.isNotBlank(teamId)) {
                // 通过WsPhotoTeams关联表查询该团队的所有照片ID
                LambdaQueryWrapper<WsPhotoTeams> photoTeamsQuery = new LambdaQueryWrapper<>();
                photoTeamsQuery.eq(WsPhotoTeams::getTeamId, teamId);

                List<WsPhotoTeams> photoTeams = wsPhotoTeamsService.list(photoTeamsQuery);

                if (photoTeams.isEmpty()) {
                    // 如果没有找到照片，返回空结果
                    Map<String, Object> emptyResult = new HashMap<>();
                    emptyResult.put("columns", new ArrayList<>());
                    emptyResult.put("data", new ArrayList<>());
                    emptyResult.put("total", 0);
                    emptyResult.put("current", current);
                    emptyResult.put("size", size);
                    return RestResult.success(emptyResult);
                }

                List<String> photoIds = photoTeams.stream()
                        .map(WsPhotoTeams::getPhotoId)
                        .collect(Collectors.toList());

                queryWrapper.in(WatermarkPhoto::getPhotoId, photoIds);
            }

            // 根据关键词筛选（水印名称）
            if (StringUtils.isNotBlank(keywords)) {
                queryWrapper.and(wrapper ->
                        wrapper.like(WatermarkPhoto::getPhotoCode, keywords)
                                .or()
                                .like(WatermarkPhoto::getLocation, keywords)
                                .or()
                                .like(WatermarkPhoto::getPhotographer, keywords)
                                .or()
                                .like(WatermarkPhoto::getReferenceText, keywords)
                );
            }

            // 根据拍照时间范围筛选
            if (startDate != null) {
                queryWrapper.ge(WatermarkPhoto::getCaptureTime, startDate);
            }

            if (endDate != null) {
                // 将结束日期设置为当天的23:59:59
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(endDate);
                calendar.set(Calendar.HOUR_OF_DAY, 23);
                calendar.set(Calendar.MINUTE, 59);
                calendar.set(Calendar.SECOND, 59);
                calendar.set(Calendar.MILLISECOND, 999);

                queryWrapper.le(WatermarkPhoto::getCaptureTime, calendar.getTime());
            }

            // 根据拍摄人员筛选
            if (StringUtils.isNotBlank(userId)) {
                queryWrapper.eq(WatermarkPhoto::getCreatedBy, userId);
            }

            // 根据模板ID筛选
            if (StringUtils.isNotBlank(templateId)) {
                queryWrapper.eq(WatermarkPhoto::getTemplateId, templateId);
            }

            // 按拍摄时间降序排序
            queryWrapper.orderByDesc(WatermarkPhoto::getCaptureTime);

            // 查询总记录数
            long total = this.count(queryWrapper);

            if (total == 0) {
                // 如果没有找到照片，返回空结果
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("columns", new ArrayList<>());
                emptyResult.put("data", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("current", current);
                emptyResult.put("size", size);
                return RestResult.success(emptyResult);
            }

            // 分页查询
            Page<WatermarkPhoto> page = new Page<>(current, size);
            Page<WatermarkPhoto> photoPage = this.page(page, queryWrapper);
            List<WatermarkPhoto> photoList = photoPage.getRecords();

            // 构建表头（列定义）
            List<Map<String, Object>> columns = new ArrayList<>();

            // 添加固定字段列定义
            addColumnDefinition(columns, "photoCode", "照片编号", 150);
            addColumnDefinition(columns, "photographer", "拍摄人", 90);
            addColumnDefinition(columns, "captureTime", "拍摄时间", 150);
            addColumnDefinition(columns, "location", "拍摄地点", 150);
            addColumnDefinition(columns, "weather", "天气状况", 100);
            addColumnDefinition(columns, "longitude", "经度", 100);
            addColumnDefinition(columns, "latitude", "纬度", 100);
            addColumnDefinition(columns, "elevation", "海拔高度", 100);
            addColumnDefinition(columns, "azimuth", "方位角", 100);
            addColumnDefinition(columns, "speed", "移动速度", 100);
            addColumnDefinition(columns, "referenceText", "参考文字", 150);
            addColumnDefinition(columns, "watermarkPhotoId", "水印图片ID", 150);
            addColumnDefinition(columns, "createdTime", "创建时间", 150);

            // 如果选择了模板ID，查询照片的自定义内容
            if (StringUtils.isNotBlank(templateId)) {
                // 提取照片ID列表，用于查询自定义内容
                List<String> photoIds = photoList.stream()
                        .map(WatermarkPhoto::getPhotoId)
                        .collect(Collectors.toList());

                // 查询所有照片的自定义内容
                LambdaQueryWrapper<WatermarkPhotoCustom> customQueryWrapper = new LambdaQueryWrapper<>();
                customQueryWrapper.in(WatermarkPhotoCustom::getPhotoId, photoIds)
                        .orderByAsc(WatermarkPhotoCustom::getSortOrder);

                List<WatermarkPhotoCustom> allCustomContents = watermarkPhotoCustomService.list(customQueryWrapper);

                // 按照照片ID分组自定义内容
                Map<String, List<WatermarkPhotoCustom>> photoCustomMap = new HashMap<>();
                for (WatermarkPhotoCustom custom : allCustomContents) {
                    if (!photoCustomMap.containsKey(custom.getPhotoId())) {
                        photoCustomMap.put(custom.getPhotoId(), new ArrayList<>());
                    }
                    photoCustomMap.get(custom.getPhotoId()).add(custom);
                }

                // 收集所有自定义字段的标题，用于确定列
                Set<String> customTitles = new HashSet<>();
                for (List<WatermarkPhotoCustom> customs : photoCustomMap.values()) {
                    for (WatermarkPhotoCustom custom : customs) {
                        customTitles.add(custom.getTitle());
                    }
                }

                // 将自定义字段添加到列定义中
                for (String title : customTitles) {
                    // 使用中文标题作为列名
                    addColumnDefinition(columns, title, title, 120);
                }

                // 将自定义内容添加到照片对象中
                for (WatermarkPhoto photo : photoList) {
                    List<WatermarkPhotoCustom> customContents = photoCustomMap.get(photo.getPhotoId());
                    if (customContents != null) {
                        photo.setWatermarkPhotoCustoms(customContents);
                    }
                }
            }

            // 构建最终结果
            Map<String, Object> result = new HashMap<>();
            result.put("columns", columns);

            // 构建数据行
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (WatermarkPhoto photo : photoList) {
                Map<String, Object> row = new HashMap<>();

                // 添加固定字段
                row.put("photoCode", photo.getPhotoCode());
                row.put("photographer", photo.getPhotographer());
                row.put("captureTime", formatDate(photo.getCaptureTime()));
                row.put("location", photo.getLocation());
                row.put("weather", photo.getWeather());
                row.put("longitude", photo.getLongitude());
                row.put("latitude", photo.getLatitude());
                row.put("elevation", photo.getElevation());
                row.put("azimuth", photo.getAzimuth());
                row.put("speed", photo.getSpeed());
                row.put("referenceText", photo.getReferenceText());
                row.put("watermarkPhotoId", photo.getWatermarkPhotoId());
                row.put("createdTime", formatDate(photo.getCreatedTime()));

                // 添加自定义字段
                if (StringUtils.isNotBlank(templateId) && photo.getWatermarkPhotoCustoms() != null) {
                    for (WatermarkPhotoCustom custom : photo.getWatermarkPhotoCustoms()) {
                        row.put(custom.getTitle(), custom.getContent());
                    }
                }

                dataList.add(row);
            }

            result.put("data", dataList);
            result.put("total", total);
            result.put("current", current);
            result.put("size", size);

            return RestResult.success(result);
        } catch (Exception e) {
            log.error("获取照片台账失败", e);
            return RestResult.error("获取照片台账失败: " + e.getMessage());
        }
    }

    /**
     * 添加列定义
     *
     * @param columns 列定义列表
     * @param colKey 列键
     * @param title 列标题
     * @param width 列宽度
     */
    private void addColumnDefinition(List<Map<String, Object>> columns, String colKey, String title, int width) {
        Map<String, Object> column = new HashMap<>();
        column.put("colKey", colKey);
        column.put("title", title);
        column.put("width", width);
        column.put("align", "center");
        columns.add(column);
    }

    /**
     * 格式化日期
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    private String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    @Override
    public void exportPhotoLedger(HttpServletResponse response, PhotoExportParamVO params) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();

            // 时间范围条件
            if (params.getStartTime() != null) {
                queryWrapper.ge(WatermarkPhoto::getCaptureTime, params.getStartTime());
            }
            if (params.getEndTime() != null) {
                queryWrapper.le(WatermarkPhoto::getCaptureTime, params.getEndTime());
            }

            // 团队条件
            if (params.getTeamId() != null && !params.getTeamId().isEmpty()) {
                List<String> userIds = wsUserTeamsService.getUserIdsByTeamId(params.getTeamId());
                if (!userIds.isEmpty()) {
                    queryWrapper.in(WatermarkPhoto::getCreatedBy, userIds);
                } else {
                    // 如果团队没有用户，返回空结果
                    Map<String, String> emptyHeaderMap = new LinkedHashMap<>();
                    emptyHeaderMap.put("originalPhotoUrl", "原图");
                    emptyHeaderMap.put("watermarkPhotoUrl", "水印图");
                    emptyHeaderMap.put("photoCode", "照片编号");
                    emptyHeaderMap.put("photographer", "拍摄人员");
                    emptyHeaderMap.put("location", "拍摄地点");
                    emptyHeaderMap.put("captureTime", "拍摄时间");
                    emptyHeaderMap.put("longitude", "经度");
                    emptyHeaderMap.put("latitude", "纬度");
                    emptyHeaderMap.put("weather", "天气状况");
                    emptyHeaderMap.put("elevation", "海拔高度");
                    emptyHeaderMap.put("azimuth", "方位角");
                    emptyHeaderMap.put("speed", "移动速度");
                    emptyHeaderMap.put("referenceText", "参考文字");
                    ExcelExportUtils.exportPhotoLedger(response, new ArrayList<>(), emptyHeaderMap, "originalPhotoUrl", "照片台账");
                    return;
                }
            }

            // 拍摄人员条件
            if (params.getPhotographer() != null && !params.getPhotographer().isEmpty()) {
                queryWrapper.like(WatermarkPhoto::getPhotographer, params.getPhotographer());
            }

            if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
                queryWrapper.like(WatermarkPhoto::getTemplateId, params.getTemplateId());
            }

            // 按拍摄时间升序排序
            queryWrapper.orderByAsc(WatermarkPhoto::getCaptureTime);

            // 查询照片列表
            List<WatermarkPhoto> photoList = this.list(queryWrapper);

            // 如果没有照片，返回空结果
            if (photoList.isEmpty()) {
                Map<String, String> emptyHeaderMap = new LinkedHashMap<>();
                emptyHeaderMap.put("originalPhotoUrl", "原图");
                emptyHeaderMap.put("watermarkPhotoUrl", "水印图");
                emptyHeaderMap.put("photoCode", "照片编号");
                emptyHeaderMap.put("photographer", "拍摄人员");
                emptyHeaderMap.put("location", "拍摄地点");
                emptyHeaderMap.put("captureTime", "拍摄时间");
                emptyHeaderMap.put("longitude", "经度");
                emptyHeaderMap.put("latitude", "纬度");
                emptyHeaderMap.put("weather", "天气状况");
                emptyHeaderMap.put("elevation", "海拔高度");
                emptyHeaderMap.put("azimuth", "方位角");
                emptyHeaderMap.put("speed", "移动速度");
                emptyHeaderMap.put("referenceText", "参考文字");
                ExcelExportUtils.exportPhotoLedger(response, new ArrayList<>(), emptyHeaderMap, "originalPhotoUrl", "照片台账");
                return;
            }

            // 获取所有照片ID
            List<String> photoIds = photoList.stream()
                    .map(WatermarkPhoto::getPhotoId)
                    .collect(Collectors.toList());

            // 构建导出数据
            List<Map<String, Object>> exportDataList = new ArrayList<>();
            Map<String, String> headerMap = new LinkedHashMap<>();

            // 添加固定字段到表头，原图和水印图放到第一列和第二列
            headerMap.put("originalPhotoUrl", "原图");
            headerMap.put("watermarkPhotoUrl", "水印图");
            headerMap.put("photoCode", "照片编号");
            headerMap.put("photographer", "拍摄人员");
            headerMap.put("location", "拍摄地点");
            headerMap.put("captureTime", "拍摄时间");
            headerMap.put("longitude", "经度");
            headerMap.put("latitude", "纬度");
            headerMap.put("weather", "天气状况");
            headerMap.put("elevation", "海拔高度");
            headerMap.put("azimuth", "方位角");
            headerMap.put("speed", "移动速度");
            headerMap.put("referenceText", "参考文字");

            // 只有当传入了模板ID时，才查询并添加自定义字段
            List<WatermarkPhotoCustom> customList = new ArrayList<>();
            Set<String> customFields = new HashSet<>();

            if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {

                // 查询照片自定义内容
                LambdaQueryWrapper<WatermarkPhotoCustom> customQueryWrapper = new LambdaQueryWrapper<>();
                customQueryWrapper.in(WatermarkPhotoCustom::getPhotoId, photoIds);
                customList = watermarkPhotoCustomService.list(customQueryWrapper);

                // 添加自定义字段到表头
                if (!customList.isEmpty()) {
                    customFields = customList.stream()
                            .map(WatermarkPhotoCustom::getTitle)
                            .collect(Collectors.toSet());

                    // 添加自定义字段到表头，考虑字段筛选
                    if (params.getFields() != null && !params.getFields().isEmpty()) {
                    for (String field : customFields) {
                            // 如果自定义字段在指定的字段列表中，则添加
                            if (params.getFields().contains(field)) {
                        headerMap.put(field, field);
                            }
                        }
                    } else {
                        // 没有指定字段列表，添加所有自定义字段
                        for (String field : customFields) {
                            headerMap.put(field, field);
                        }
                    }
                }
            } else {
            }

            // 按照照片ID分组自定义内容
            Map<String, List<WatermarkPhotoCustom>> customMap = new HashMap<>();
            if (!customList.isEmpty()) {
                customMap = customList.stream()
                        .collect(Collectors.groupingBy(WatermarkPhotoCustom::getPhotoId));
            }

            // 构建每行数据
            for (WatermarkPhoto photo : photoList) {
                Map<String, Object> rowData = new LinkedHashMap<>();

                // 添加固定字段
                // 构建原图和水印图的完整URL
                String baseUrl = "https://172.29.0.20:7001/mongo/FileServlet?isPic=0&action=downFile&isThumail=0&id=";
                String originalPhotoId = photo.getOriginalPhotoId();
                String watermarkPhotoId = photo.getWatermarkPhotoId();

                // 确保ID不为空才构建URL
                String originalPhotoUrl = (originalPhotoId != null) ? baseUrl + originalPhotoId : "";
                String watermarkPhotoUrl = (watermarkPhotoId != null) ? baseUrl + watermarkPhotoId : "";

                // 特别标记这些字段，方便在图片处理时识别
                rowData.put("originalPhotoUrl", originalPhotoUrl);
                rowData.put("watermarkPhotoUrl", watermarkPhotoUrl);
                rowData.put("photoCode", photo.getPhotoCode());
                rowData.put("photographer", photo.getPhotographer());
                rowData.put("location", photo.getLocation());
                rowData.put("captureTime", photo.getCaptureTime());
                rowData.put("longitude", photo.getLongitude());
                rowData.put("latitude", photo.getLatitude());
                rowData.put("weather", photo.getWeather());
                rowData.put("elevation", photo.getElevation());
                rowData.put("azimuth", photo.getAzimuth());
                rowData.put("speed", photo.getSpeed());
                rowData.put("referenceText", photo.getReferenceText());

                // 添加自定义字段，仅当传入了模板ID时
                if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
                    List<WatermarkPhotoCustom> photoCustomList = customMap.getOrDefault(photo.getPhotoId(), Collections.emptyList());
                    for (WatermarkPhotoCustom custom : photoCustomList) {
                        // 直接使用自定义字段标题作为key
                        rowData.put(custom.getTitle(), custom.getContent());

                        // 添加字体颜色和背景颜色字段
                        if (custom.getFontColor() != null) {
                            rowData.put(custom.getTitle() + "_FONT_COLOR", custom.getFontColor());
                        }
                        if (custom.getBackgroundColor() != null) {
                            rowData.put(custom.getTitle() + "_BACKGROUND_COLOR", custom.getBackgroundColor());
                        }
                    }

                    // 对于没有值的自定义字段，设置为空字符串（JDK8兼容方式）
                    for (String field : customFields) {
                        if (!rowData.containsKey(field)) {
                            rowData.put(field, "");
                        }
                    }
                }

                exportDataList.add(rowData);
            }

            // 导出Excel
            String title = "照片台账_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
            // 使用originalPhotoUrl作为图片字段参数，ExcelExportUtils会处理这两列的图片展示
            ExcelExportUtils.exportPhotoLedger(response, exportDataList, headerMap, "originalPhotoUrl", title);

        } catch (Exception e) {
            log.error("导出照片台账失败", e);
            try {
                // 设置错误响应
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"导出照片台账失败: " + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("设置错误响应失败", ex);
            }
        }
    }

    @Override
    public void exportPhotoFolder(HttpServletResponse response, PhotoExportFolderParamVO params) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();

            // 根据photoIds查询，如果指定了photoIds则优先使用
            if (params.getPhotoIds() != null && !params.getPhotoIds().isEmpty()) {
                queryWrapper.in(WatermarkPhoto::getPhotoId, params.getPhotoIds());
            } else {
                // 时间范围条件
                if (params.getStartTime() != null) {
                    queryWrapper.ge(WatermarkPhoto::getCaptureTime, params.getStartTime());
                }
                if (params.getEndTime() != null) {
                    queryWrapper.le(WatermarkPhoto::getCaptureTime, params.getEndTime());
                }

                // 团队条件
                if (params.getTeamId() != null && !params.getTeamId().isEmpty()) {
                    List<String> userIds = wsUserTeamsService.getUserIdsByTeamId(params.getTeamId());
                    if (!userIds.isEmpty()) {
                        queryWrapper.in(WatermarkPhoto::getCreatedBy, userIds);
                    } else {
                        // 如果团队没有用户，返回空结果
                        return;
                    }
                }

                // 拍摄人员条件
                if (params.getPhotographer() != null && !params.getPhotographer().isEmpty()) {
                    queryWrapper.like(WatermarkPhoto::getPhotographer, params.getPhotographer());
                }

                // 拍摄地点条件
                if (params.getLocation() != null && !params.getLocation().isEmpty()) {
                    queryWrapper.like(WatermarkPhoto::getLocation, params.getLocation());
                }
            }

            // 查询照片列表
            List<WatermarkPhoto> photoList = this.list(queryWrapper);

            // 如果没有照片，返回错误信息
            if (photoList.isEmpty()) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"未找到符合条件的照片\"}");
                return;
            }

            // 获取模板名称（如果指定了模板ID）
            String templateName = "";
            if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
                WatermarkTemplate template = watermarkTemplateService.getById(params.getTemplateId());
                if (template != null) {
                    templateName = template.getTemplateName();
                }
            }

            // 创建临时文件夹
            String tempFolderPath = FileZipUtils.createTempFolder("photo_export_");
            if (tempFolderPath == null) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"创建临时文件夹失败\"}");
                return;
            }

            // 根据归类方式分类照片并复制到对应文件夹
            int groupType = params.getGroupType() != null ? params.getGroupType() : 0;
            SimpleDateFormat dateFolderFormat = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat fileDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

            // 记录处理成功的文件数
            int successCount = 0;

            // 处理每张照片
            for (WatermarkPhoto photo : photoList) {
                try {
                    // 确定照片ID（原图或水印图）
                    String photoId = params.getExportWatermark() ? photo.getWatermarkPhotoId() : photo.getOriginalPhotoId();
                    if (photoId == null || photoId.isEmpty()) {
                        continue;
                    }

                    // 构建源图片URL
                    String sourceUrl = "https://172.29.0.20:7001/mongo/FileServlet?isPic=0&action=downFile&isThumail=0&id=" + photoId;

                    // 根据归类方式确定目标文件夹路径
                    String targetFolderPath = tempFolderPath;

                    switch (groupType) {
                        case 1: // 按拍摄人员归类
                            String photographer = photo.getPhotographer();
                            if (photographer != null && !photographer.isEmpty()) {
                                targetFolderPath = tempFolderPath + File.separator + photographer;
                            }
                            break;

                        case 2: // 按拍摄日期归类
                            Date captureTime = photo.getCaptureTime();
                            if (captureTime != null) {
                                targetFolderPath = tempFolderPath + File.separator + dateFolderFormat.format(captureTime);
                            }
                            break;

                        case 3: // 按自定义属性归类
                            String customField = params.getCustomGroupField();
                            if (customField != null && !customField.isEmpty()) {
                                Object fieldValue = getPhotoFieldValue(photo, customField);
                                if (fieldValue != null) {
                                    targetFolderPath = tempFolderPath + File.separator + fieldValue.toString();
                                }
                            }
                            break;

                        default: // 不归类
                            targetFolderPath = tempFolderPath;
                            break;
                    }

                    // 确保目标文件夹存在
                    FileZipUtils.ensureFolderExists(targetFolderPath);

                    // 构建文件名
                    String fileName;

                    if (params.getFileNamePattern() != null && !params.getFileNamePattern().isEmpty()) {
                        // 使用自定义命名模式
                        String[] fields = params.getFileNamePattern().split(",");
                        StringBuilder fileNameBuilder = new StringBuilder();

                        for (String field : fields) {
                            Object fieldValue = getPhotoFieldValue(photo, field.trim());
                            if (fieldValue != null) {
                                if (fileNameBuilder.length() > 0) {
                                    fileNameBuilder.append("_");
                                }

                                // 如果是日期类型，使用格式化后的字符串
                                if (fieldValue instanceof Date) {
                                    fileNameBuilder.append(fileDateFormat.format((Date) fieldValue));
                                } else {
                                    fileNameBuilder.append(fieldValue.toString());
                                }
                            }
                        }

                        fileName = fileNameBuilder.toString();
                    } else {
                        // 默认命名格式：拍摄人员+拍摄时间+模板名称
                        StringBuilder fileNameBuilder = new StringBuilder();

                        if (photo.getPhotographer() != null && !photo.getPhotographer().isEmpty()) {
                            fileNameBuilder.append(photo.getPhotographer());
                        }

                        if (photo.getCaptureTime() != null) {
                            if (fileNameBuilder.length() > 0) {
                                fileNameBuilder.append("_");
                            }
                            fileNameBuilder.append(fileDateFormat.format(photo.getCaptureTime()));
                        }

                        if (templateName != null && !templateName.isEmpty()) {
                            if (fileNameBuilder.length() > 0) {
                                fileNameBuilder.append("_");
                            }
                            fileNameBuilder.append(templateName);
                        }

                        fileName = fileNameBuilder.toString();
                    }

                    // 确保文件名有效（去除不允许的字符）
                    fileName = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");

                    // 确保文件名不为空
                    if (fileName.isEmpty()) {
                        fileName = "photo_" + photo.getPhotoId();
                    }

                    // 添加扩展名
                    fileName += ".jpg";

                    // 完整的目标文件路径
                    String targetFilePath = targetFolderPath + File.separator + fileName;

                    // 下载并保存图片
                    if (downloadAndSaveImage(sourceUrl, targetFilePath)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("处理照片失败, photoId: {}, error: {}", photo.getPhotoId(), e.getMessage());
                }
            }

            if (successCount == 0) {
                // 没有成功处理的文件
                FileZipUtils.deleteFolder(tempFolderPath);
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"未能成功导出任何照片\"}");
                return;
            }

            // 创建ZIP文件路径
            String zipFilePath = tempFolderPath + ".zip";

            // 压缩文件夹
            boolean zipResult = FileZipUtils.zipFolder(tempFolderPath, zipFilePath);

            if (!zipResult) {
                // 压缩失败
                FileZipUtils.deleteFolder(tempFolderPath);
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"压缩文件夹失败\"}");
                return;
            }

            // 准备下载
            File zipFile = new File(zipFilePath);

            // 设置响应头
            response.setContentType("application/zip");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("照片导出_" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".zip", "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName);
            response.setContentLength((int) zipFile.length());

            // 写入响应
            try (InputStream is = new FileInputStream(zipFile);
                 OutputStream os = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    os.write(buffer, 0, bytesRead);
                }
                os.flush();
            }

            // 删除临时文件和文件夹
            try {
                Files.delete(Paths.get(zipFilePath));
                FileZipUtils.deleteFolder(tempFolderPath);
            } catch (Exception e) {
                log.error("删除临时文件失败: {}", e.getMessage());
            }

        } catch (Exception e) {
            log.error("导出照片文件夹失败: {}", e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"导出照片文件夹失败: " + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("设置错误响应失败", ex);
            }
        }
    }

    /**
     * 获取照片的字段值
     *
     * @param photo 照片对象
     * @param fieldName 字段名
     * @return 字段值
     */
    private Object getPhotoFieldValue(WatermarkPhoto photo, String fieldName) {
        if (photo == null || fieldName == null || fieldName.isEmpty()) {
            return null;
        }

        switch (fieldName.toLowerCase()) {
            case "photoid":
                return photo.getPhotoId();
            case "photocode":
                return photo.getPhotoCode();
            case "photographer":
                return photo.getPhotographer();
            case "location":
                return photo.getLocation();
            case "capturetime":
                return photo.getCaptureTime();
            case "longitude":
                return photo.getLongitude();
            case "latitude":
                return photo.getLatitude();
            case "weather":
                return photo.getWeather();
            case "elevation":
                return photo.getElevation();
            case "azimuth":
                return photo.getAzimuth();
            case "speed":
                return photo.getSpeed();
            case "referencetext":
                return photo.getReferenceText();
            default:
                return null;
        }
    }

    /**
     * 下载并保存图片
     *
     * @param imageUrl 图片URL
     * @param targetFilePath 目标文件路径
     * @return 是否成功
     */
    private boolean downloadAndSaveImage(String imageUrl, String targetFilePath) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            log.error("图片URL为空");
            return false;
        }

        HttpURLConnection connection = null;
        InputStream inputStream = null;
        FileOutputStream outputStream = null;

        try {
            log.info("开始下载图片: {} -> {}", imageUrl, targetFilePath);

            URL url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("GET");
            connection.setConnectTimeout(30000);  // 30秒连接超时
            connection.setReadTimeout(60000);     // 60秒读取超时

            // 添加请求头，模拟浏览器请求
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setRequestProperty("Accept", "image/webp,image/apng,image/*,*/*;q=0.8");
            connection.setRequestProperty("Accept-Encoding", "gzip, deflate");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);

            int responseCode = connection.getResponseCode();
            String responseMessage = connection.getResponseMessage();
            log.info("图片下载响应: {} {}", responseCode, responseMessage);

            if (responseCode != HttpURLConnection.HTTP_OK) {
                log.error("下载图片失败，HTTP状态码: {}, 消息: {}, URL: {}", responseCode, responseMessage, imageUrl);

                // 如果失败，尝试不同的URL参数
                if (imageUrl.contains("isPic=0") && !imageUrl.contains("RETRY")) {
                    String alternativeUrl = imageUrl.replace("isPic=0", "isPic=1") + "&RETRY=1";
                    log.info("尝试替代URL: {}", alternativeUrl);
                    return downloadAndSaveImage(alternativeUrl, targetFilePath);
                }
                return false;
            }

            // 获取内容信息
            int contentLength = connection.getContentLength();
            String contentType = connection.getContentType();
            log.info("图片内容长度: {}字节, 内容类型: {}", contentLength, contentType);

            inputStream = connection.getInputStream();
            outputStream = new FileOutputStream(targetFilePath);

            byte[] buffer = new byte[16384]; // 16KB缓冲区
            int bytesRead;
            long totalRead = 0;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                totalRead += bytesRead;

                // 每读取1MB输出一次进度
                if (totalRead % (1024 * 1024) == 0) {
                    log.info("已下载: {}MB", totalRead / (1024 * 1024));
                }
            }

            log.info("成功下载图片: {} -> {}, 大小: {}KB", imageUrl, targetFilePath, totalRead / 1024);

            // 验证下载的文件是否有效
            File downloadedFile = new File(targetFilePath);
            if (downloadedFile.exists() && downloadedFile.length() > 0) {
                log.info("文件验证成功，文件大小: {}字节", downloadedFile.length());
                return true;
            } else {
                log.error("下载的文件无效或为空: {}", targetFilePath);
                return false;
            }

        } catch (Exception e) {
            log.error("下载图片异常: {}, URL: {}", e.getMessage(), imageUrl, e);

            // 如果发生异常，尝试不同的URL参数
            if (imageUrl.contains("isPic=0") && !imageUrl.contains("RETRY")) {
                String alternativeUrl = imageUrl.replace("isPic=0", "isPic=1") + "&RETRY=1";
                log.info("异常后尝试替代URL: {}", alternativeUrl);
                return downloadAndSaveImage(alternativeUrl, targetFilePath);
            }
            return false;
        } finally {
            try {
                if (outputStream != null) outputStream.close();
                if (inputStream != null) inputStream.close();
                if (connection != null) connection.disconnect();
            } catch (IOException e) {
                log.error("关闭资源失败", e);
            }
        }
    }

    /**
     * 获取所有不重复的拍摄地点列表（用于下拉选项）
     *
     * @return 拍摄地点列表，格式为 { id: "", name: '469县道', desc: '广东省云浮市云城区安塘街道' }
     */
    @Override
    public RestResult<List<Map<String, String>>> getLocationOptions(String teamId) {
        try {
            // 查询所有非空且不重复的地点
            QueryWrapper<WatermarkPhoto> queryWrapper = new QueryWrapper<>();
            queryWrapper.select("DISTINCT location");
            queryWrapper.isNotNull("location");
            
            // 如果teamId不为空，则根据团队ID筛选照片
            if (StringUtils.isNotBlank(teamId)) {
                // 获取团队下的所有用户ID
                List<String> userIds = wsUserTeamsService.getUserIdsByTeamId(teamId);
                if (userIds != null && !userIds.isEmpty()) {
                    queryWrapper.in("created_by", userIds);
                }
                
                // 通过WsPhotoTeams关联表查询该团队的所有照片ID
                LambdaQueryWrapper<WsPhotoTeams> photoTeamsQuery = new LambdaQueryWrapper<>();
                photoTeamsQuery.eq(WsPhotoTeams::getTeamId, teamId);
                List<WsPhotoTeams> photoTeams = wsPhotoTeamsService.list(photoTeamsQuery);
                
                if (!photoTeams.isEmpty()) {
                    List<String> photoIds = photoTeams.stream()
                            .map(WsPhotoTeams::getPhotoId)
                            .collect(Collectors.toList());
                    queryWrapper.in("photo_id", photoIds);
                }
            }

            List<Object> locations = baseMapper.selectObjs(queryWrapper);
            List<Map<String, String>> result = new ArrayList<>();

            // 转换为所需格式的列表
            for (Object locationObj : locations) {
                if (locationObj != null) {
                    String location = locationObj.toString().trim();
                    if (!location.isEmpty()) {
                        Map<String, String> locationMap = new HashMap<>();
                        locationMap.put("id", location);
                        locationMap.put("desc", location);
                        
                        // 提取"市"前面的部分作为name
                        String name = extractNameFromLocation(location);
                        locationMap.put("name", name);
                        
                        result.add(locationMap);
                    }
                }
            }

            return RestResult.success(result);
        } catch (Exception e) {
            log.error("获取拍摄地点列表失败", e);
            return RestResult.error("获取拍摄地点列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 从完整地址中提取省市部分作为name（例如"广东省广州市"）
     *
     * @param location 完整地址
     * @return 提取后的name
     */
    private String extractNameFromLocation(String location) {
        if (StringUtils.isBlank(location)) {
            return "";
        }
        
        int cityIndex = location.indexOf("市");
        if (cityIndex > 0) {
            // 查找省/自治区/直辖市标识
            int provinceIndex = location.indexOf("省");
            int autoRegionIndex = location.indexOf("自治区");
            int directCityIndex = location.indexOf("直辖市");
            
            // 从最早出现的行政区标识开始
            int startIndex = Integer.MAX_VALUE;
            if (provinceIndex >= 0 && provinceIndex < startIndex) {
                startIndex = provinceIndex;
            }
            if (autoRegionIndex >= 0 && autoRegionIndex < startIndex) {
                startIndex = autoRegionIndex;
            }
            if (directCityIndex >= 0 && directCityIndex < startIndex) {
                startIndex = directCityIndex;
            }
            
            // 确定实际的起始索引位置
            int actualStartIndex;
            if (startIndex != Integer.MAX_VALUE) {
                // 找到前面可能的国家标识，如"中国"
                int countryIndex = location.indexOf("国");
                if (countryIndex >= 0 && countryIndex < startIndex) {
                    actualStartIndex = countryIndex + 1;
                } else {
                    // 如果没有找到国家标识，从地址开头开始查找第一个省级行政区开始
                    int firstMeaningfulCharIndex = 0;
                    while (firstMeaningfulCharIndex < location.length() && 
                           (location.charAt(firstMeaningfulCharIndex) == ' ' || 
                            location.charAt(firstMeaningfulCharIndex) == ',' || 
                            location.charAt(firstMeaningfulCharIndex) == '，' ||
                            location.charAt(firstMeaningfulCharIndex) == '、')) {
                        firstMeaningfulCharIndex++;
                    }
                    actualStartIndex = firstMeaningfulCharIndex;
                }
            } else {
                // 如果没有找到省级行政区，从头开始截取
                actualStartIndex = 0;
            }
            
            // 截取到市结束
            return location.substring(actualStartIndex, cityIndex + 1);
        } else {
            // 如果没有"市"，则返回完整地址
            return location;
        }
    }

    /**
     * 获取用户角色
     *
     * @param userId 用户ID
     * @return 用户角色名称
     */
    private String getUserRole(String userId) {
        // 从用户信息中推断角色
        try {
            WsUser user = wsUserMapper.selectById(userId);
            if (user != null && user.getUsername() != null) {
                String username = user.getUsername();
                // 根据用户名推断角色
                if (username.contains("管理") || username.contains("admin")) {
                    return "管理员";
                } else if (username.contains("审核")) {
                    return "审核员";
                } else if (username.contains("摄影")) {
                    return "摄影师";
                }
            }
        } catch (Exception e) {
            log.error("获取用户角色失败: {}", e.getMessage());
        }

        // 默认角色
        return "摄影人员";
    }

    /**
     * 创建水印照片并同步到用户团队
     *
     * @param originalPhoto 原始照片文件
     * @param watermarkPhoto 水印照片文件
     * @param photo 水印照片信息
     * @param userId 用户ID
     * @param teamIds 同步团队ID集合，逗号分隔，非必填。如果不填则按默认逻辑同步，如果填了则按指定团队同步
     * @return 创建结果
     */
    @Override
    @Transactional
    public RestResult<WatermarkPhoto> createWatermarkPhotoWithSync(
            MultipartFile originalPhoto,
            MultipartFile watermarkPhoto,
            WatermarkPhoto photo,
            String userId,
            String teamIds) {

        // 调用原有方法创建照片
        RestResult<WatermarkPhoto> result = createWatermarkPhoto(originalPhoto, watermarkPhoto, photo);

        // 如果创建成功，进行团队同步处理
        int count = 0;
        if (result.getCode() == RestResult.SUCCESS && result.getData() != null) {
            // 获取创建成功的照片
            WatermarkPhoto createdPhoto = result.getData();

            // 根据teamIds参数决定同步逻辑
            if (teamIds != null && !teamIds.trim().isEmpty()) {
                // 如果指定了teamIds，则按指定的团队ID进行同步
                count = syncPhotoToSpecificTeams(createdPhoto.getPhotoId(), teamIds);
            } else {
                // 如果没有指定teamIds，则按默认逻辑同步到用户的团队
                count = wsPhotoTeamsService.syncPhotoToTeams(createdPhoto.getPhotoId(), userId);
            }
        }

        return RestResult.success(result.getData(),"已同步到"+count+"个团队");
    }
    
    /**
     * 异步导出照片台账（先记录导出任务，再异步生成文件）
     *
     * @param params 导出参数
     * @param userId 用户ID
     * @param havPic 是否包含照片（true-有照片，false-无照片，默认true）。在非自定义导出属性时，如果选择无照片，则不导出原图和水印图；自定义属性下载时此参数无效
     * @return 导出任务ID
     */
    @Override
    @Transactional
    public RestResult<String> asyncExportPhotoLedger(PhotoExportParamVO params, String userId, Boolean havPic) {
        try {
            // 获取用户信息
            WsUser user = wsUserService.getById(userId);
            if (user == null) {
                return RestResult.error("用户不存在");
            }
            
            // 1. 创建导出记录
            WsExportRecord record = new WsExportRecord();
            record.setId(UUID.randomUUID().toString().replace("-", ""));
            record.setApplyTime(new Date());
            // downloadCount将在导出完成后设置为实际记录数量
            record.setStatus(0L); // 0-处理中，1-成功，-1-失败
            record.setCreatedBy(userId);
            record.setCreatedDate(new Date());
            record.setIsDeleted(0L);
            
            // 设置文件名和类型
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = "照片台账_" + sdf.format(new Date()) + ".xlsx";
            record.setFileName(fileName);
            record.setFileType("照片台账-表格");
            
            // 设置时间范围
            if (params.getStartTime() != null || params.getEndTime() != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                StringBuilder timeRange = new StringBuilder();
                
                if (params.getStartTime() != null) {
                    timeRange.append(dateFormat.format(params.getStartTime()));
                } else {
                    timeRange.append("不限");
                }
                
                timeRange.append(" 至 ");
                
                if (params.getEndTime() != null) {
                    timeRange.append(dateFormat.format(params.getEndTime()));
                } else {
                    timeRange.append("不限");
                }
                
                record.setTimeRange(timeRange.toString());
            }
            
            // 2. 保存记录
            boolean saved = wsExportRecordService.save(record);
            if (!saved) {
                return RestResult.error("创建导出任务失败");
            }
            
            // 3. 异步执行导出任务
            CompletableFuture.runAsync(() -> {
                processExportTask(record.getId(), params, userId, havPic);
            });
            
            // 4. 返回任务ID
            return RestResult.success(record.getId(), "导出任务已创建，请稍后查看下载记录");
            
        } catch (Exception e) {
            log.error("创建导出任务失败", e);
            return RestResult.error("创建导出任务失败：" + e.getMessage());
        }
    }
    
    /**
     * 异步处理导出任务
     *
     * @param recordId 记录ID
     * @param params 导出参数
     * @param userId 用户ID
     * @param havPic 是否包含照片
     */
    @Async
    public void processExportTask(String recordId, PhotoExportParamVO params, String userId, Boolean havPic) {
        try {
            // 获取记录信息
            WsExportRecord record = wsExportRecordService.getById(recordId);
            if (record == null) {
                log.error("导出任务记录不存在：{}", recordId);
                return;
            }
            
            // 创建导出目录
            SimpleDateFormat dateDirFormat = new SimpleDateFormat("yyyyMMdd");
            String dateDir = dateDirFormat.format(new Date());
            String userDir = userId;
            
            // 创建目录：基础路径/日期/用户ID/
            Path exportDir = Paths.get(exportBasePath, dateDir, userDir);
            Files.createDirectories(exportDir);
            
            // 导出文件的完整路径
            Path exportFilePath = exportDir.resolve(record.getFileName());
            
            // 执行导出逻辑，将结果保存到文件
            boolean success = exportToFile(params, exportFilePath.toString(), havPic);
            
            // 更新记录状态
            if (success) {
                record.setStatus(1L); // 成功
                record.setLocalPath(exportFilePath.toString());
                
                // 设置导出的数据记录数量
                long recordCount = getExportedRecordCount(params);
                record.setDownloadCount(recordCount);
                
                // 设置导出涉及的人员数量
                long memberCount = getExportedMemberCount(params);
                record.setMemberCount(memberCount);
                
            } else {
                record.setStatus(-1L); // 失败
            }
            
            // 更新记录
            wsExportRecordService.updateById(record);
            
        } catch (Exception e) {
            log.error("处理导出任务失败：{}", recordId, e);
            try {
                // 更新记录状态为失败
                WsExportRecord record = wsExportRecordService.getById(recordId);
                if (record != null) {
                    record.setStatus(-1L); // 失败
                    wsExportRecordService.updateById(record);
                }
            } catch (Exception ex) {
                log.error("更新导出记录状态失败", ex);
            }
        }
    }
    
    /**
     * 将导出结果保存到文件
     *
     * @param params 导出参数
     * @param filePath 文件路径
     * @param havPic 是否包含照片
     * @return 是否成功
     */
    private boolean exportToFile(PhotoExportParamVO params, String filePath, Boolean havPic) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();

            // 时间范围条件
            if (params.getStartTime() != null) {
                queryWrapper.ge(WatermarkPhoto::getCaptureTime, params.getStartTime());
            }
            if (params.getEndTime() != null) {
                queryWrapper.le(WatermarkPhoto::getCaptureTime, params.getEndTime());
            }

            // 团队条件
            if (params.getTeamId() != null && !params.getTeamId().isEmpty()) {
                List<String> userIds = wsUserTeamsService.getUserIdsByTeamId(params.getTeamId());
                if (!userIds.isEmpty()) {
                    queryWrapper.in(WatermarkPhoto::getCreatedBy, userIds);
                } else {
                    return false; // 团队没有用户，无法导出
                }
            }

            // 拍摄人员条件（逗号分隔）
            if (params.getPhotographer() != null && !params.getPhotographer().isEmpty()) {
                String[] photographers = params.getPhotographer().split(",");
                if (photographers.length > 0) {
                    queryWrapper.and(wrapper -> {
                        for (int i = 0; i < photographers.length; i++) {
                            String photographer = photographers[i].trim();
                            if (StringUtils.isNotBlank(photographer)) {
                                if (i == 0) {
                                    wrapper.like(WatermarkPhoto::getPhotographer, photographer);
                                } else {
                                    wrapper.or().like(WatermarkPhoto::getPhotographer, photographer);
                                }
                            }
                        }
                    });
                }
            }

            // 模板ID条件（逗号分隔）
            if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
                String[] templateIds = params.getTemplateId().split(",");
                if (templateIds.length > 0) {
                    queryWrapper.and(wrapper -> {
                        for (int i = 0; i < templateIds.length; i++) {
                            String templateId = templateIds[i].trim();
                            if (StringUtils.isNotBlank(templateId)) {
                                if (i == 0) {
                                    wrapper.eq(WatermarkPhoto::getTemplateId, templateId);
                                } else {
                                    wrapper.or().eq(WatermarkPhoto::getTemplateId, templateId);
                                }
                            }
                        }
                    });
                }
            }

            // 按拍摄时间升序排序
            queryWrapper.orderByAsc(WatermarkPhoto::getCaptureTime);

            // 查询照片列表
            List<WatermarkPhoto> photoList = this.list(queryWrapper);

            // 如果没有照片，返回失败
            if (photoList.isEmpty()) {
                return false;
            }

            // 获取所有照片ID
            List<String> photoIds = photoList.stream()
                    .map(WatermarkPhoto::getPhotoId)
                    .collect(Collectors.toList());

            // 构建导出数据
            List<Map<String, Object>> exportDataList = new ArrayList<>();
            Map<String, String> headerMap = new LinkedHashMap<>();

            // 定义所有可用的固定字段
            Map<String, String> allFieldsMap = new LinkedHashMap<>();
            allFieldsMap.put("originalPhotoUrl", "原图");
            allFieldsMap.put("watermarkPhotoUrl", "水印图");
            allFieldsMap.put("photoCode", "照片编号");
            allFieldsMap.put("photographer", "拍摄人员");
            allFieldsMap.put("location", "拍摄地点");
            allFieldsMap.put("captureTime", "拍摄时间");
            allFieldsMap.put("longitude", "经度");
            allFieldsMap.put("latitude", "纬度");
            allFieldsMap.put("weather", "天气状况");
            allFieldsMap.put("elevation", "海拔高度");
            allFieldsMap.put("azimuth", "方位角");
            allFieldsMap.put("speed", "移动速度");
            allFieldsMap.put("referenceText", "参考文字");
            
            // 如果指定了字段，则严格按照用户指定的字段导出
            if (params.getFields() != null && !params.getFields().isEmpty()) {
                for (String field : params.getFields()) {
                    if (allFieldsMap.containsKey(field)) {
                        headerMap.put(field, allFieldsMap.get(field));
                    }
                }
                log.info("使用用户指定的字段导出: {}", headerMap.keySet());
            } else {
                // 没有指定字段，默认导出水印图和所有基本字段
                headerMap.put("watermarkPhotoUrl", "水印图");
                headerMap.put("photoCode", "照片编号");
                headerMap.put("photographer", "拍摄人员");
                headerMap.put("location", "拍摄地点");
                headerMap.put("captureTime", "拍摄时间");
                headerMap.put("longitude", "经度");
                headerMap.put("latitude", "纬度");
                headerMap.put("weather", "天气状况");
                headerMap.put("elevation", "海拔高度");
                headerMap.put("azimuth", "方位角");
                headerMap.put("speed", "移动速度");
                headerMap.put("referenceText", "参考文字");
                log.info("默认字段导出(水印图+完整基本信息): {}", headerMap.keySet());
            }
            
            // 检查表头是否包含水印图或原图中的至少一个
            boolean hasImageField = headerMap.containsKey("originalPhotoUrl") || headerMap.containsKey("watermarkPhotoUrl");
            if (!hasImageField) {
                log.warn("导出设置中未包含图片字段，图片将不会显示在Excel中");
            }

            // 只有当传入了模板ID时，才查询并添加自定义字段
            List<WatermarkPhotoCustom> customList = new ArrayList<>();
            Set<String> customFields = new HashSet<>();

            if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
                // 查询照片自定义内容
                LambdaQueryWrapper<WatermarkPhotoCustom> customQueryWrapper = new LambdaQueryWrapper<>();
                customQueryWrapper.in(WatermarkPhotoCustom::getPhotoId, photoIds);
                customList = watermarkPhotoCustomService.list(customQueryWrapper);

                // 添加自定义字段到表头
                if (!customList.isEmpty()) {
                    customFields = customList.stream()
                            .map(WatermarkPhotoCustom::getTitle)
                            .collect(Collectors.toSet());

                    for (String field : customFields) {
                        headerMap.put(field, field);
                    }
                }
            }

            // 按照照片ID分组自定义内容
            Map<String, List<WatermarkPhotoCustom>> customMap = new HashMap<>();
            if (!customList.isEmpty()) {
                customMap = customList.stream()
                        .collect(Collectors.groupingBy(WatermarkPhotoCustom::getPhotoId));
            }

            // 构建每行数据
            for (WatermarkPhoto photo : photoList) {
                Map<String, Object> rowData = new LinkedHashMap<>();

                // 添加固定字段
                // 构建原图和水印图的完整URL
                String originalPhotoId = photo.getOriginalPhotoId();
                String watermarkPhotoId = photo.getWatermarkPhotoId();

                // 确保ID不为空才构建URL
                String originalPhotoUrl = "";
                String watermarkPhotoUrl = "";

                if (originalPhotoId != null && !originalPhotoId.isEmpty()) {
                    // 构建原图URL - 使用标准格式
                    originalPhotoUrl = "https://172.29.0.20:7001/mongo/FileServlet?isPic=0&action=downFile&isThumail=0&id=" + originalPhotoId;
                    log.info("构建原图URL: {}", originalPhotoUrl);
                }

                if (watermarkPhotoId != null && !watermarkPhotoId.isEmpty()) {
                    // 构建水印图URL - 使用标准格式
                    watermarkPhotoUrl = "https://172.29.0.20:7001/mongo/FileServlet?isPic=0&action=downFile&isThumail=0&id=" + watermarkPhotoId;
                    log.info("构建水印图URL: {}", watermarkPhotoUrl);
                }
                
                // 始终添加原图和水印图URL
                rowData.put("originalPhotoUrl", originalPhotoUrl);
                rowData.put("watermarkPhotoUrl", watermarkPhotoUrl);
                rowData.put("photoCode", photo.getPhotoCode());
                rowData.put("photographer", photo.getPhotographer());
                rowData.put("location", photo.getLocation());
                rowData.put("captureTime", photo.getCaptureTime());
                rowData.put("longitude", photo.getLongitude());
                rowData.put("latitude", photo.getLatitude());
                rowData.put("weather", photo.getWeather());
                rowData.put("elevation", photo.getElevation());
                rowData.put("azimuth", photo.getAzimuth());
                rowData.put("speed", photo.getSpeed());
                rowData.put("referenceText", photo.getReferenceText());

                // 添加自定义字段，仅当传入了模板ID时
                if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
                    List<WatermarkPhotoCustom> photoCustomList = customMap.getOrDefault(photo.getPhotoId(), Collections.emptyList());
                    for (WatermarkPhotoCustom custom : photoCustomList) {
                        rowData.put(custom.getTitle(), custom.getContent());

                        // 添加字体颜色和背景颜色字段
                        if (custom.getFontColor() != null) {
                            rowData.put(custom.getTitle() + "_FONT_COLOR", custom.getFontColor());
                        }
                        if (custom.getBackgroundColor() != null) {
                            rowData.put(custom.getTitle() + "_BACKGROUND_COLOR", custom.getBackgroundColor());
                        }
                    }

                    // 对于没有值的自定义字段，设置为空字符串
                    for (String field : customFields) {
                        if (!rowData.containsKey(field)) {
                            rowData.put(field, "");
                        }
                    }
                }

                exportDataList.add(rowData);
            }

            // 打印导出信息
            log.info("照片台账导出信息 - 数据行数: {}, 表头: {}", exportDataList.size(), headerMap.keySet());

            // 检查图片URL是否有效
            if (!exportDataList.isEmpty()) {
                // 打印前5行数据的图片URL信息，帮助调试
                int checkRows = Math.min(5, exportDataList.size());
                log.info("开始检查前{}行图片URL的有效性", checkRows);

                for (int i = 0; i < checkRows; i++) {
                    Map<String, Object> row = exportDataList.get(i);
                    String origUrl = (String) row.get("originalPhotoUrl");
                    String waterUrl = (String) row.get("watermarkPhotoUrl");

                    log.info("第{}行图片URL - 原图: {}, 水印图: {}",
                        i + 1,
                        origUrl != null && !origUrl.isEmpty() ? origUrl : "无",
                        waterUrl != null && !waterUrl.isEmpty() ? waterUrl : "无");

                    // 测试URL可访问性
                    if (origUrl != null && !origUrl.isEmpty()) {
                        boolean accessible = testImageUrlAccessibility(origUrl);
                        log.info("第{}行原图URL可访问性: {}", i + 1, accessible ? "可访问" : "不可访问");
                    }

                    if (waterUrl != null && !waterUrl.isEmpty()) {
                        boolean accessible = testImageUrlAccessibility(waterUrl);
                        log.info("第{}行水印图URL可访问性: {}", i + 1, accessible ? "可访问" : "不可访问");
                    }
                }
            }
            
            // 确定图片字段名称，根据havPic参数和是否为自定义属性来决定
            String imageFieldName = null;

            // 检查是否为自定义属性导出（通过模板ID判断）
            boolean isCustomExport = params.getTemplateId() != null && !params.getTemplateId().isEmpty();

            if (isCustomExport) {
                // 自定义属性导出时，havPic参数无效，按原来的逻辑处理
                if (headerMap.containsKey("watermarkPhotoUrl")) {
                    imageFieldName = "watermarkPhotoUrl"; // 优先使用水印图
                    log.info("自定义属性导出：使用水印图作为Excel中的图片来源");
                } else if (headerMap.containsKey("originalPhotoUrl")) {
                    imageFieldName = "originalPhotoUrl"; // 次选原图
                    log.info("自定义属性导出：使用原图作为Excel中的图片来源");
                } else {
                    log.info("自定义属性导出：未设置图片字段，Excel中不会显示图片");
                }
            } else {
                // 非自定义属性导出时，根据havPic参数决定
                if (havPic != null && havPic) {
                    // havPic为true时，导出照片
                    if (headerMap.containsKey("watermarkPhotoUrl")) {
                        imageFieldName = "watermarkPhotoUrl"; // 优先使用水印图
                        log.info("非自定义导出（有照片）：使用水印图作为Excel中的图片来源");
                    } else if (headerMap.containsKey("originalPhotoUrl")) {
                        imageFieldName = "originalPhotoUrl"; // 次选原图
                        log.info("非自定义导出（有照片）：使用原图作为Excel中的图片来源");
                    } else {
                        log.info("非自定义导出（有照片）：未设置图片字段，Excel中不会显示图片");
                    }
                } else {
                    // havPic为false时，不导出照片
                    imageFieldName = null;
                    log.info("非自定义导出（无照片）：不导出原图和水印图");
                }
            }

            // 导出Excel到文件，使用try-with-resources确保流正确关闭
            File file = new File(filePath);
            try (FileOutputStream fos = new FileOutputStream(file)) {
                // 调用ExcelExportUtils中的方法导出到文件
                ExcelExportUtils.exportPhotoLedgerToStream(fos, exportDataList, headerMap, imageFieldName, "照片台账");
            }

            return true;
        } catch (Exception e) {
            log.error("导出照片台账到文件失败", e);
            return false;
        }
    }

    /**
     * 异步导出照片文件夹（按分类存放照片并打包成ZIP）
     *
     * @param params 导出参数
     * @param userId 用户ID
     * @param fileType 文件类型
     * @return 导出任务ID
     */
    @Override
    @Transactional
    public RestResult<String> asyncExportPhotoFolder(PhotoExportFolderParamVO params, String userId, String fileType) {
        try {
            // 获取用户信息
            WsUser user = wsUserService.getById(userId);
            if (user == null) {
                return RestResult.error("用户不存在");
            }

            // 1. 创建导出记录
            WsExportRecord record = new WsExportRecord();
            record.setId(UUID.randomUUID().toString().replace("-", ""));
            record.setApplyTime(new Date());
            record.setStatus(0L); // 0-处理中，1-成功，-1-失败
            record.setCreatedBy(userId);
            record.setCreatedDate(new Date());
            record.setIsDeleted(0L);

            // 设置文件名和类型
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = fileType + "_" + sdf.format(new Date()) + ".zip";
            record.setFileName(fileName);
            record.setFileType(fileType);

            // 设置时间范围
            if (params.getStartTime() != null || params.getEndTime() != null) {
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
                StringBuilder timeRange = new StringBuilder();

                if (params.getStartTime() != null) {
                    timeRange.append(dateFormat.format(params.getStartTime()));
                } else {
                    timeRange.append("不限");
                }

                timeRange.append(" 至 ");

                if (params.getEndTime() != null) {
                    timeRange.append(dateFormat.format(params.getEndTime()));
                } else {
                    timeRange.append("不限");
                }

                record.setTimeRange(timeRange.toString());
            }

            // 保存记录
            wsExportRecordService.save(record);

            // 2. 同步执行导出（参考团队成员导出模式）
            try {
                // 创建导出目录
                SimpleDateFormat dateDirFormat = new SimpleDateFormat("yyyyMMdd");
                String dateDir = dateDirFormat.format(new Date());
                String userDir = userId;

                // 创建目录：基础路径/日期/用户ID/
                Path exportDir = Paths.get(exportBasePath, dateDir, userDir);
                Files.createDirectories(exportDir);

                // 导出文件的完整路径
                Path exportFilePath = exportDir.resolve(record.getFileName());

                // 执行导出逻辑，将结果保存到文件
                boolean success = exportPhotoFolderToFile(params, exportFilePath.toString());

                // 更新记录状态
                if (success) {
                    record.setStatus(1L); // 成功
                    record.setLocalPath(exportFilePath.toString());

                    // 设置导出的数据记录数量
                    long recordCount = getPhotoFolderExportedRecordCount(params);
                    record.setDownloadCount(recordCount);

                    // 设置导出涉及的人员数量
                    long memberCount = getPhotoFolderExportedMemberCount(params);
                    record.setMemberCount(memberCount);

                    log.info("照片文件夹导出成功: {}", exportFilePath);
                } else {
                    record.setStatus(-1L); // 失败
                    log.error("照片文件夹导出失败");
                }

                wsExportRecordService.updateById(record);

            } catch (Exception e) {
                log.error("导出照片文件夹异常", e);
                record.setStatus(-1L); // 失败
                wsExportRecordService.updateById(record);
            }

            return RestResult.success(record.getId(), "导出任务已创建，请稍后在下载记录中查看");

        } catch (Exception e) {
            log.error("创建照片文件夹导出任务失败", e);
            return RestResult.error("创建导出任务失败：" + e.getMessage());
        }
    }



    /**
     * 导出照片文件夹到文件
     *
     * @param params 导出参数
     * @param filePath 文件路径
     * @return 是否成功
     */
    private boolean exportPhotoFolderToFile(PhotoExportFolderParamVO params, String filePath) {
        try {
            // 直接复用原有的核心逻辑，但输出到文件而不是HttpServletResponse
            return exportPhotoFolderToZipFile(params, filePath);
        } catch (Exception e) {
            log.error("导出照片文件夹到文件失败", e);
            return false;
        }
    }

    /**
     * 导出照片文件夹到ZIP文件（复用原有逻辑）
     *
     * @param params 导出参数
     * @param zipFilePath ZIP文件路径
     * @return 是否成功
     */
    private boolean exportPhotoFolderToZipFile(PhotoExportFolderParamVO params, String zipFilePath) throws Exception {
        // 构建查询条件（复用原有逻辑）
        LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();

        // 根据photoIds查询，如果指定了photoIds则优先使用
        if (params.getPhotoIds() != null && !params.getPhotoIds().isEmpty()) {
            queryWrapper.in(WatermarkPhoto::getPhotoId, params.getPhotoIds());
        } else {
            // 时间范围条件
            if (params.getStartTime() != null) {
                queryWrapper.ge(WatermarkPhoto::getCaptureTime, params.getStartTime());
            }
            if (params.getEndTime() != null) {
                queryWrapper.le(WatermarkPhoto::getCaptureTime, params.getEndTime());
            }

            // 团队条件
            if (params.getTeamId() != null && !params.getTeamId().isEmpty()) {
                List<String> userIds = wsUserTeamsService.getUserIdsByTeamId(params.getTeamId());
                if (!userIds.isEmpty()) {
                    queryWrapper.in(WatermarkPhoto::getCreatedBy, userIds);
                } else {
                    // 如果团队没有用户，返回失败
                    return false;
                }
            }

            // 拍摄人员条件
            if (params.getPhotographer() != null && !params.getPhotographer().isEmpty()) {
                queryWrapper.like(WatermarkPhoto::getPhotographer, params.getPhotographer());
            }

            // 拍摄地点条件
            if (params.getLocation() != null && !params.getLocation().isEmpty()) {
                queryWrapper.like(WatermarkPhoto::getLocation, params.getLocation());
            }
        }

        // 查询照片列表
        List<WatermarkPhoto> photoList = this.list(queryWrapper);

        // 如果没有照片，返回失败
        if (photoList.isEmpty()) {
            return false;
        }

        // 获取模板名称（如果指定了模板ID）
        String templateName = "";
        if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
            WatermarkTemplate template = watermarkTemplateService.getById(params.getTemplateId());
            if (template != null) {
                templateName = template.getTemplateName();
            }
        }

        // 创建临时文件夹
        String tempFolderPath = FileZipUtils.createTempFolder("photo_export_");
        if (tempFolderPath == null) {
            return false;
        }

        try {
            // 根据归类方式分类照片并复制到对应文件夹
            int groupType = params.getGroupType() != null ? params.getGroupType() : 0;
            SimpleDateFormat dateFolderFormat = new SimpleDateFormat("yyyyMMdd");
            SimpleDateFormat fileDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");

            // 记录处理成功的文件数
            int successCount = 0;

            // 处理每张照片（复用原有逻辑）
            for (WatermarkPhoto photo : photoList) {
                try {
                    // 确定照片ID（原图或水印图）
                    String photoId = params.getExportWatermark() ? photo.getWatermarkPhotoId() : photo.getOriginalPhotoId();
                    if (photoId == null || photoId.isEmpty()) {
                        continue;
                    }

                    // 构建源图片URL
                    String sourceUrl = "https://172.29.0.20:7001/mongo/FileServlet?isPic=0&action=downFile&isThumail=0&id=" + photoId;

                    // 根据归类方式确定目标文件夹路径
                    String targetFolderPath = tempFolderPath;

                    switch (groupType) {
                        case 1: // 按拍摄人员归类
                            String photographer = photo.getPhotographer();
                            if (photographer != null && !photographer.isEmpty()) {
                                targetFolderPath = tempFolderPath + File.separator + photographer;
                            }
                            break;

                        case 2: // 按拍摄日期归类
                            Date captureTime = photo.getCaptureTime();
                            if (captureTime != null) {
                                targetFolderPath = tempFolderPath + File.separator + dateFolderFormat.format(captureTime);
                            }
                            break;

                        case 3: // 按自定义属性归类
                            String customField = params.getCustomGroupField();
                            if (customField != null && !customField.isEmpty()) {
                                Object fieldValue = getPhotoFieldValue(photo, customField);
                                if (fieldValue != null) {
                                    targetFolderPath = tempFolderPath + File.separator + fieldValue.toString();
                                }
                            }
                            break;

                        default: // 不归类
                            targetFolderPath = tempFolderPath;
                            break;
                    }

                    // 确保目标文件夹存在
                    FileZipUtils.ensureFolderExists(targetFolderPath);

                    // 构建文件名（复用原有逻辑）
                    String fileName = buildPhotoFileName(photo, params, templateName, fileDateFormat);

                    // 完整的目标文件路径
                    String targetFilePath = targetFolderPath + File.separator + fileName;

                    // 下载并保存图片
                    if (downloadAndSaveImage(sourceUrl, targetFilePath)) {
                        successCount++;
                    }
                } catch (Exception e) {
                    log.error("处理照片失败, photoId: {}, error: {}", photo.getPhotoId(), e.getMessage());
                }
            }

            if (successCount == 0) {
                // 没有成功处理的文件
                return false;
            }

            // 压缩文件夹到指定路径
            boolean zipResult = FileZipUtils.zipFolder(tempFolderPath, zipFilePath);

            return zipResult;

        } finally {
            // 删除临时文件夹
            try {
                FileZipUtils.deleteFolder(tempFolderPath);
            } catch (Exception e) {
                log.error("删除临时文件夹失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 构建照片文件名（复用原有逻辑）
     *
     * @param photo 照片对象
     * @param params 导出参数
     * @param templateName 模板名称
     * @param fileDateFormat 日期格式化器
     * @return 文件名
     */
    private String buildPhotoFileName(WatermarkPhoto photo, PhotoExportFolderParamVO params,
                                    String templateName, SimpleDateFormat fileDateFormat) {
        String fileName;

        if (params.getFileNamePattern() != null && !params.getFileNamePattern().isEmpty()) {
            // 使用自定义命名模式
            String[] fields = params.getFileNamePattern().split(",");
            StringBuilder fileNameBuilder = new StringBuilder();

            for (String field : fields) {
                Object fieldValue = getPhotoFieldValue(photo, field.trim());
                if (fieldValue != null) {
                    if (fileNameBuilder.length() > 0) {
                        fileNameBuilder.append("_");
                    }

                    // 如果是日期类型，使用格式化后的字符串
                    if (fieldValue instanceof Date) {
                        fileNameBuilder.append(fileDateFormat.format((Date) fieldValue));
                    } else {
                        fileNameBuilder.append(fieldValue.toString());
                    }
                }
            }

            fileName = fileNameBuilder.toString();
        } else {
            // 默认命名格式：拍摄人员+拍摄时间+模板名称
            StringBuilder fileNameBuilder = new StringBuilder();

            if (photo.getPhotographer() != null && !photo.getPhotographer().isEmpty()) {
                fileNameBuilder.append(photo.getPhotographer());
            }

            if (photo.getCaptureTime() != null) {
                if (fileNameBuilder.length() > 0) {
                    fileNameBuilder.append("_");
                }
                fileNameBuilder.append(fileDateFormat.format(photo.getCaptureTime()));
            }

            if (templateName != null && !templateName.isEmpty()) {
                if (fileNameBuilder.length() > 0) {
                    fileNameBuilder.append("_");
                }
                fileNameBuilder.append(templateName);
            }

            fileName = fileNameBuilder.toString();
        }

        // 确保文件名有效（去除不允许的字符）
        fileName = fileName.replaceAll("[\\\\/:*?\"<>|]", "_");

        // 确保文件名不为空
        if (fileName.isEmpty()) {
            fileName = "photo_" + photo.getPhotoId();
        }

        // 添加扩展名
        fileName += ".jpg";

        return fileName;
    }

    /**
     * 获取照片文件夹导出的记录数量
     *
     * @param params 导出参数
     * @return 记录数量
     */
    private long getPhotoFolderExportedRecordCount(PhotoExportFolderParamVO params) {
        try {
            // 构建查询条件（参考原有的exportPhotoFolder方法中的查询逻辑）
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();

            // 照片ID列表条件（如果指定了具体照片）
            if (params.getPhotoIds() != null && !params.getPhotoIds().isEmpty()) {
                queryWrapper.in(WatermarkPhoto::getPhotoId, params.getPhotoIds());
                return this.count(queryWrapper);
            }

            // 团队ID条件
            if (params.getTeamId() != null && !params.getTeamId().isEmpty()) {
                List<String> userIds = wsUserTeamsService.getUserIdsByTeamId(params.getTeamId());
                if (!userIds.isEmpty()) {
                    queryWrapper.in(WatermarkPhoto::getCreatedBy, userIds);
                } else {
                    return 0; // 团队没有用户
                }
            }

            // 时间范围条件
            if (params.getStartTime() != null) {
                queryWrapper.ge(WatermarkPhoto::getCaptureTime, params.getStartTime());
            }
            if (params.getEndTime() != null) {
                queryWrapper.le(WatermarkPhoto::getCaptureTime, params.getEndTime());
            }

            // 拍摄人员条件
            if (params.getPhotographer() != null && !params.getPhotographer().isEmpty()) {
                queryWrapper.like(WatermarkPhoto::getPhotographer, params.getPhotographer());
            }

            // 拍摄地点条件
            if (params.getLocation() != null && !params.getLocation().isEmpty()) {
                queryWrapper.like(WatermarkPhoto::getLocation, params.getLocation());
            }

            // 模板ID条件
            if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
                queryWrapper.eq(WatermarkPhoto::getTemplateId, params.getTemplateId());
            }

            // 删除标记条件
            queryWrapper.eq(WatermarkPhoto::getDelFlag, 0);

            return this.count(queryWrapper);
        } catch (Exception e) {
            log.error("获取照片文件夹导出记录数量失败", e);
            return 0;
        }
    }

    /**
     * 获取照片文件夹导出涉及的人员数量
     *
     * @param params 导出参数
     * @return 人员数量
     */
    private long getPhotoFolderExportedMemberCount(PhotoExportFolderParamVO params) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();

            // 照片ID列表条件（如果指定了具体照片）
            if (params.getPhotoIds() != null && !params.getPhotoIds().isEmpty()) {
                queryWrapper.in(WatermarkPhoto::getPhotoId, params.getPhotoIds());
            } else {
                // 团队ID条件
                if (params.getTeamId() != null && !params.getTeamId().isEmpty()) {
                    List<String> userIds = wsUserTeamsService.getUserIdsByTeamId(params.getTeamId());
                    if (!userIds.isEmpty()) {
                        queryWrapper.in(WatermarkPhoto::getCreatedBy, userIds);
                    } else {
                        return 0; // 团队没有用户
                    }
                }

                // 时间范围条件
                if (params.getStartTime() != null) {
                    queryWrapper.ge(WatermarkPhoto::getCaptureTime, params.getStartTime());
                }
                if (params.getEndTime() != null) {
                    queryWrapper.le(WatermarkPhoto::getCaptureTime, params.getEndTime());
                }

                // 拍摄人员条件
                if (params.getPhotographer() != null && !params.getPhotographer().isEmpty()) {
                    queryWrapper.like(WatermarkPhoto::getPhotographer, params.getPhotographer());
                }

                // 拍摄地点条件
                if (params.getLocation() != null && !params.getLocation().isEmpty()) {
                    queryWrapper.like(WatermarkPhoto::getLocation, params.getLocation());
                }

                // 模板ID条件
                if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
                    queryWrapper.eq(WatermarkPhoto::getTemplateId, params.getTemplateId());
                }
            }

            // 删除标记条件
            queryWrapper.eq(WatermarkPhoto::getDelFlag, 0);

            // 查询不重复的拍摄人员
            List<WatermarkPhoto> photos = this.list(queryWrapper);
            return photos.stream()
                    .map(WatermarkPhoto::getPhotographer)
                    .filter(photographer -> photographer != null && !photographer.isEmpty())
                    .distinct()
                    .count();

        } catch (Exception e) {
            log.error("获取照片文件夹导出人员数量失败", e);
            return 0;
        }
    }

    /**
     * 测试图片URL的可访问性
     * @param imageUrl 图片URL
     * @return 是否可访问
     */
    private boolean testImageUrlAccessibility(String imageUrl) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            return false;
        }

        HttpURLConnection connection = null;
        try {
            URL url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(5000);
            connection.setReadTimeout(5000);

            // 添加请求头
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setRequestProperty("Accept", "image/*,*/*;q=0.8");
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);

            int responseCode = connection.getResponseCode();
            return (responseCode == HttpURLConnection.HTTP_OK ||
                    responseCode == HttpURLConnection.HTTP_MOVED_TEMP ||
                    responseCode == HttpURLConnection.HTTP_MOVED_PERM ||
                    responseCode == HttpURLConnection.HTTP_SEE_OTHER);
        } catch (Exception e) {
            log.warn("测试图片URL可访问性失败: {}, URL: {}", e.getMessage(), imageUrl);
            return false;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }



    /**
     * 获取导出的数据记录数量
     *
     * @param params 导出参数
     * @return 记录数量
     */
    private long getExportedRecordCount(PhotoExportParamVO params) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();

            // 时间范围条件
            if (params.getStartTime() != null) {
                queryWrapper.ge(WatermarkPhoto::getCaptureTime, params.getStartTime());
            }
            if (params.getEndTime() != null) {
                queryWrapper.le(WatermarkPhoto::getCaptureTime, params.getEndTime());
            }

            // 团队条件
            if (params.getTeamId() != null && !params.getTeamId().isEmpty()) {
                List<String> userIds = wsUserTeamsService.getUserIdsByTeamId(params.getTeamId());
                if (!userIds.isEmpty()) {
                    queryWrapper.in(WatermarkPhoto::getCreatedBy, userIds);
                }
            }

            // 拍摄人员条件（逗号分隔）
            if (params.getPhotographer() != null && !params.getPhotographer().isEmpty()) {
                String[] photographers = params.getPhotographer().split(",");
                if (photographers.length > 0) {
                    queryWrapper.and(wrapper -> {
                        for (int i = 0; i < photographers.length; i++) {
                            String photographer = photographers[i].trim();
                            if (StringUtils.isNotBlank(photographer)) {
                                if (i == 0) {
                                    wrapper.like(WatermarkPhoto::getPhotographer, photographer);
                                } else {
                                    wrapper.or().like(WatermarkPhoto::getPhotographer, photographer);
                                }
                            }
                        }
                    });
                }
            }

            // 模板ID条件（逗号分隔）
            if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
                String[] templateIds = params.getTemplateId().split(",");
                if (templateIds.length > 0) {
                    queryWrapper.and(wrapper -> {
                        for (int i = 0; i < templateIds.length; i++) {
                            String templateId = templateIds[i].trim();
                            if (StringUtils.isNotBlank(templateId)) {
                                if (i == 0) {
                                    wrapper.eq(WatermarkPhoto::getTemplateId, templateId);
                                } else {
                                    wrapper.or().eq(WatermarkPhoto::getTemplateId, templateId);
                                }
                            }
                        }
                    });
                }
            }

            // 计算记录数
            return this.count(queryWrapper);
        } catch (Exception e) {
            log.error("计算导出记录数量失败", e);
            return 0;
        }
    }

    /**
     * 获取导出数据中涉及的不同人员数量
     *
     * @param params 导出参数
     * @return 人员数量
     */
    private long getExportedMemberCount(PhotoExportParamVO params) {
        try {
            // 构建查询条件
            LambdaQueryWrapper<WatermarkPhoto> queryWrapper = new LambdaQueryWrapper<>();

            // 时间范围条件
            if (params.getStartTime() != null) {
                queryWrapper.ge(WatermarkPhoto::getCaptureTime, params.getStartTime());
            }
            if (params.getEndTime() != null) {
                queryWrapper.le(WatermarkPhoto::getCaptureTime, params.getEndTime());
            }

            // 团队条件
            if (params.getTeamId() != null && !params.getTeamId().isEmpty()) {
                List<String> userIds = wsUserTeamsService.getUserIdsByTeamId(params.getTeamId());
                if (!userIds.isEmpty()) {
                    queryWrapper.in(WatermarkPhoto::getCreatedBy, userIds);
                }
            }

            // 拍摄人员条件（逗号分隔）
            if (params.getPhotographer() != null && !params.getPhotographer().isEmpty()) {
                String[] photographers = params.getPhotographer().split(",");
                if (photographers.length > 0) {
                    queryWrapper.and(wrapper -> {
                        for (int i = 0; i < photographers.length; i++) {
                            String photographer = photographers[i].trim();
                            if (StringUtils.isNotBlank(photographer)) {
                                if (i == 0) {
                                    wrapper.like(WatermarkPhoto::getPhotographer, photographer);
                                } else {
                                    wrapper.or().like(WatermarkPhoto::getPhotographer, photographer);
                                }
                            }
                        }
                    });
                }
            }

            // 模板ID条件（逗号分隔）
            if (params.getTemplateId() != null && !params.getTemplateId().isEmpty()) {
                String[] templateIds = params.getTemplateId().split(",");
                if (templateIds.length > 0) {
                    queryWrapper.and(wrapper -> {
                        for (int i = 0; i < templateIds.length; i++) {
                            String templateId = templateIds[i].trim();
                            if (StringUtils.isNotBlank(templateId)) {
                                if (i == 0) {
                                    wrapper.eq(WatermarkPhoto::getTemplateId, templateId);
                                } else {
                                    wrapper.or().eq(WatermarkPhoto::getTemplateId, templateId);
                                }
                            }
                        }
                    });
                }
            }
            
            // 只查询 photographer 字段
            queryWrapper.select(WatermarkPhoto::getPhotographer);
            
            // 获取不同的摄影师列表
            List<WatermarkPhoto> photos = this.list(queryWrapper);
            
            // 通过Set去重统计不同的摄影师人数
            Set<String> photographers = photos.stream()
                .map(WatermarkPhoto::getPhotographer)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
            
            return photographers.size();
        } catch (Exception e) {
            log.error("计算导出人员数量失败", e);
            return 0;
        }
    }

    /**
     * 格式化工作时间显示
     *
     * @param timeDiffMillis 时间差（毫秒）
     * @return 格式化的时间字符串
     */
    private String formatWorkingTime(long timeDiffMillis) {
        if (timeDiffMillis <= 0) {
            return "0分钟";
        }

        long seconds = timeDiffMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        long months = days / 30; // 简化计算，30天为一个月
        long years = days / 365; // 简化计算，365天为一年

        if (years > 0) {
            return years + "年前";
        } else if (months > 0) {
            return months + "个月前";
        } else if (days > 0) {
            return days + "天前";
        } else if (hours > 0) {
            long remainingMinutes = minutes % 60;
            if (remainingMinutes > 0) {
                return hours + "小时" + remainingMinutes + "分钟";
            } else {
                return hours + "小时";
            }
        } else if (minutes > 0) {
            return minutes + "分钟";
        } else {
            return "1分钟";
        }
    }

    /**
     * 格式化距离显示
     *
     * @param distanceInMeters 距离（米）
     * @return 格式化的距离字符串
     */
    private String formatDistance(double distanceInMeters) {
        if (distanceInMeters < 0) {
            return "0米";
        }

        if (distanceInMeters < 1000) {
            // 小于1000米，显示米，保留两位小数
            BigDecimal meters = new BigDecimal(distanceInMeters).setScale(2, RoundingMode.HALF_UP);
            // 去掉末尾的0
            String meterStr = meters.stripTrailingZeros().toPlainString();
            return meterStr + "米";
        } else {
            // 大于等于1000米，显示千米，保留两位小数
            double kilometers = distanceInMeters / 1000.0;
            BigDecimal km = new BigDecimal(kilometers).setScale(2, RoundingMode.HALF_UP);
            // 去掉末尾的0
            String kmStr = km.stripTrailingZeros().toPlainString();
            return kmStr + "千米";
        }
    }

    /**
     * 将照片同步到指定的团队
     *
     * @param photoId 照片ID
     * @param teamIds 团队ID集合，逗号分隔
     * @return 同步的团队数量
     */
    private int syncPhotoToSpecificTeams(String photoId, String teamIds) {
        try {
            if (teamIds == null || teamIds.trim().isEmpty()) {
                return 0;
            }

            // 解析团队ID列表
            String[] teamIdArray = teamIds.split(",");
            List<String> teamIdList = new ArrayList<>();
            for (String teamId : teamIdArray) {
                String trimmedTeamId = teamId.trim();
                if (!trimmedTeamId.isEmpty()) {
                    teamIdList.add(trimmedTeamId);
                }
            }

            if (teamIdList.isEmpty()) {
                return 0;
            }

            // 验证团队是否存在
            LambdaQueryWrapper<WsTeam> teamQuery = new LambdaQueryWrapper<>();
            teamQuery.in(WsTeam::getTeamId, teamIdList);
            teamQuery.eq(WsTeam::getDelFlag, new BigDecimal(0)); // 未删除的团队
            List<WsTeam> existingTeams = wsTeamService.list(teamQuery);

            if (existingTeams.isEmpty()) {
                log.warn("指定的团队ID都不存在或已被删除: {}", teamIds);
                return 0;
            }

            // 获取存在的团队ID列表
            List<String> validTeamIds = existingTeams.stream()
                    .map(WsTeam::getTeamId)
                    .collect(Collectors.toList());

            // 创建照片团队关联记录
            List<WsPhotoTeams> photoTeamsList = new ArrayList<>();
            for (String teamId : validTeamIds) {
                WsPhotoTeams photoTeam = new WsPhotoTeams();
                photoTeam.setId(UUID.randomUUID().toString().replace("-", ""));
                photoTeam.setPhotoId(photoId);
                photoTeam.setTeamId(teamId);
                photoTeamsList.add(photoTeam);
            }

            // 批量保存关联记录
            if (!photoTeamsList.isEmpty()) {
                wsPhotoTeamsService.saveBatch(photoTeamsList);
                log.info("照片 {} 已同步到指定的 {} 个团队", photoId, photoTeamsList.size());
                return photoTeamsList.size();
            }

            return 0;

        } catch (Exception e) {
            log.error("同步照片到指定团队失败: photoId={}, teamIds={}", photoId, teamIds, e);
            return 0;
        }
    }

    /**
     * 获取每个拍摄人员的历史最后一次拍照记录（不限于当天）
     *
     * @param userIds 用户ID列表
     * @param beforeDate 查询此日期之前的记录
     * @return 每个拍摄人员的最后一次拍照记录
     */
    private Map<String, WatermarkPhoto> getLastPhotoByPhotographer(List<String> userIds, Date beforeDate) {
        Map<String, WatermarkPhoto> lastPhotoMap = new HashMap<>();

        try {
            for (String userId : userIds) {
                // 使用分页查询获取第一条记录，避免 Oracle ROWNUM 语法问题
                LambdaQueryWrapper<WatermarkPhoto> query = new LambdaQueryWrapper<>();
                query.eq(WatermarkPhoto::getCreatedBy, userId)
                     .lt(WatermarkPhoto::getCaptureTime, beforeDate)
                     .orderByDesc(WatermarkPhoto::getCaptureTime);

                // 使用分页查询，获取第一页第一条记录
                Page<WatermarkPhoto> page = new Page<>(1, 1);
                IPage<WatermarkPhoto> result = this.page(page, query);

                if (result != null && !result.getRecords().isEmpty()) {
                    WatermarkPhoto lastPhoto = result.getRecords().get(0);
                    if (lastPhoto != null) {
                        // 使用 userId 作为 key，而不是 photographer
                        lastPhotoMap.put(userId, lastPhoto);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取历史拍照记录失败", e);
        }

        return lastPhotoMap;
    }

    /**
     * 格式化距离值
     *
     * @param distanceInMeters 距离（米）
     * @return 格式化后的距离值
     */
    private double formatDistanceValue(double distanceInMeters) {
        if (distanceInMeters < 0) {
            return 0.0;
        }

        if (distanceInMeters < 1000) {
            // 小于1000米，按米算，四舍五入，不保留小数
            return Math.round(distanceInMeters);
        } else {
            // 大于等于1000米，按千米算，四舍五入，不保留小数
            double kilometers = distanceInMeters / 1000.0;
            return Math.round(kilometers);
        }
    }

    /**
     * 重新计算距离和时间差（基于正确的时间顺序）
     *
     * @param allGroups 按时间正序排列的分组列表
     */
    private void recalculateDistanceAndTimeDiff(List<PhotoGroupVO> allGroups) {
        // 按拍摄人员分组
        Map<String, List<PhotoGroupVO>> photographerGroups = allGroups.stream()
                .collect(Collectors.groupingBy(PhotoGroupVO::getPhotographer));

        for (String photographer : photographerGroups.keySet()) {
            List<PhotoGroupVO> groups = photographerGroups.get(photographer);

            // 按时间正序排列
            groups.sort(Comparator.comparing(PhotoGroupVO::getLatestCaptureTime,
                    Comparator.nullsLast(Comparator.naturalOrder())));

            // 重新计算每个分组的距离和时间差
            for (int i = 0; i < groups.size(); i++) {
                PhotoGroupVO currentGroup = groups.get(i);

                if (i == 0) {
                    // 第一个分组（最早的），没有上一次
                    currentGroup.setDistanceFromLastLocation(0.0);
                    currentGroup.setTimeDiffFromLastCapture(0L);
                } else {
                    // 后续分组，计算与上一个分组的距离和时间差
                    PhotoGroupVO previousGroup = groups.get(i - 1);

                    // 计算距离
                    double distanceInMeters = GeoUtils.calculateDistance(
                            previousGroup.getLongitude(), previousGroup.getLatitude(),
                            currentGroup.getLongitude(), currentGroup.getLatitude()
                    );
                    double formattedDistance = formatDistanceValue(distanceInMeters);
                    currentGroup.setDistanceFromLastLocation(formattedDistance);

                    // 计算时间差
                    if (previousGroup.getLatestCaptureTime() != null && currentGroup.getLatestCaptureTime() != null) {
                        long timeDiff = TimeUnit.MILLISECONDS.toMinutes(
                                currentGroup.getLatestCaptureTime().getTime() - previousGroup.getLatestCaptureTime().getTime()
                        );
                        currentGroup.setTimeDiffFromLastCapture(Math.max(0, timeDiff));
                    } else {
                        currentGroup.setTimeDiffFromLastCapture(0L);
                    }
                }
            }
        }
    }

    /**
     * 多级分组：用户 -> 时间距离组合（5分钟内且100米内） -> 团队 -> 地点
     *
     * @param photos 照片列表
     * @param teamIds 团队ID列表
     * @return 多级分组结果
     */
    private Map<String, Map<String, Map<String, Map<String, List<WatermarkPhoto>>>>> groupPhotosByMultipleCriteria(
            List<WatermarkPhoto> photos, List<String> teamIds) {

        // 按用户分组进行时间和距离的组合分组
        Map<String, List<List<WatermarkPhoto>>> userTimeDistanceGroups = groupByTimeAndDistance(photos);

        Map<String, Map<String, Map<String, Map<String, List<WatermarkPhoto>>>>> result = new HashMap<>();

        for (Map.Entry<String, List<List<WatermarkPhoto>>> userEntry : userTimeDistanceGroups.entrySet()) {
            String userId = userEntry.getKey();
            List<List<WatermarkPhoto>> userGroups = userEntry.getValue();

            int groupIndex = 0;
            for (List<WatermarkPhoto> groupPhotos : userGroups) {
                String timeDistanceGroupKey = "group_" + groupIndex++;

                // 在每个时间距离组内，再按团队和地点分组
                for (WatermarkPhoto photo : groupPhotos) {
                    String teamId = getPhotoTeamId(photo, teamIds);
                    String location = photo.getLocation() != null ? photo.getLocation() : "未知地点";

                    result.computeIfAbsent(userId, k -> new HashMap<>())
                          .computeIfAbsent(timeDistanceGroupKey, k -> new HashMap<>())
                          .computeIfAbsent(teamId, k -> new HashMap<>())
                          .computeIfAbsent(location, k -> new ArrayList<>())
                          .add(photo);
                }
            }
        }

        return result;
    }

    /**
     * 获取照片所属的团队ID
     *
     * @param photo 照片
     * @param teamIds 可能的团队ID列表
     * @return 团队ID
     */
    private String getPhotoTeamId(WatermarkPhoto photo, List<String> teamIds) {
        // 这里可以根据实际业务逻辑来确定照片属于哪个团队
        // 简化处理：如果只有一个团队，就返回该团队ID
        if (teamIds.size() == 1) {
            return teamIds.get(0);
        }

        // 如果有多个团队，可以根据照片的创建者来判断
        // 这里需要查询用户所属的团队，简化处理返回第一个
        return teamIds.get(0);
    }

    /**
     * 生成时间分组键（从当天第一个时间开始，5分钟一组）
     *
     * @param captureTime 拍摄时间
     * @param firstTimeOfDay 当天第一个时间
     * @return 时间分组键
     */
    private String generateTimeGroupKey(Date captureTime, Date firstTimeOfDay) {
        if (captureTime == null || firstTimeOfDay == null) {
            return "unknown";
        }

        // 计算与当天第一个时间的差值（分钟）
        long timeDiffMillis = captureTime.getTime() - firstTimeOfDay.getTime();
        long timeDiffMinutes = timeDiffMillis / (60 * 1000);

        // 计算属于第几个5分钟组
        long groupIndex = timeDiffMinutes / 5;

        // 计算该组的开始时间
        long groupStartMillis = firstTimeOfDay.getTime() + (groupIndex * 5 * 60 * 1000);

        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        return sdf.format(new Date(groupStartMillis));
    }

    /**
     * 格式化工作时间显示（带单位）
     *
     * @param timeDiffMillis 时间差（毫秒）
     * @return 格式化的时间字符串（带单位）
     */
    private String formatWorkingTimeWithUnit(long timeDiffMillis) {
        if (timeDiffMillis <= 0) {
            return "0分钟";
        }

        long seconds = timeDiffMillis / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;
        long days = hours / 24;
        long months = days / 30;
        long years = days / 365;

        if (years > 0) {
            return years + "年前";
        } else if (months > 0) {
            return months + "个月前";
        } else if (days > 0) {
            return days + "天前";
        } else if (hours > 0) {
            long remainingMinutes = minutes % 60;
            if (remainingMinutes > 0) {
                return hours + "小时" + remainingMinutes + "分钟";
            } else {
                return hours + "小时";
            }
        } else if (minutes > 0) {
            return minutes + "分钟";
        } else {
            return "1分钟";
        }
    }

    /**
     * 格式化距离显示（带单位）
     *
     * @param distanceInMeters 距离（米）
     * @return 格式化的距离字符串（带单位）
     */
    private String formatDistanceWithUnit(double distanceInMeters) {
        if (distanceInMeters < 0) {
            return "0米";
        }

        if (distanceInMeters < 1000) {
            // 小于1000米，显示米，四舍五入，不保留小数
            long meters = Math.round(distanceInMeters);
            return meters + "米";
        } else {
            // 大于等于1000米，显示千米，四舍五入，不保留小数
            double kilometers = distanceInMeters / 1000.0;
            long km = Math.round(kilometers);
            return km + "千米";
        }
    }

    /**
     * 计算距离和时间差（包含历史数据）
     *
     * @param resultList 排序后的结果列表
     * @param userIds 用户ID列表
     * @param currentDate 当前查询日期
     */
    private void calculateDistanceAndTimeDiffWithHistory(List<PhotoDailySummaryVO> resultList, List<String> userIds, Date currentDate) {
        // 获取每个用户的历史最后一次拍照记录
        Map<String, WatermarkPhoto> lastPhotoByUser = getLastPhotoByPhotographer(userIds, currentDate);

        // 按用户分组
        Map<String, List<PhotoDailySummaryVO>> userGroups = resultList.stream()
                .collect(Collectors.groupingBy(PhotoDailySummaryVO::getUserId, LinkedHashMap::new, Collectors.toList()));

        for (String userId : userGroups.keySet()) {
            List<PhotoDailySummaryVO> userSummaries = userGroups.get(userId);

            // 按时间正序排列（用于正确计算上一次）
            userSummaries.sort((s1, s2) -> {
                Date date1 = s1.getEndTime() != null ? s1.getEndTime() : s1.getStartTime();
                Date date2 = s2.getEndTime() != null ? s2.getEndTime() : s2.getStartTime();
                if (date1 != null && date2 != null) {
                    return date1.compareTo(date2); // 正序
                }
                return 0;
            });

            // 获取该用户的历史最后一次照片
            WatermarkPhoto userLastHistoryPhoto = lastPhotoByUser.get(userId);

            // 计算每个分组的距离和时间差
            for (int i = 0; i < userSummaries.size(); i++) {
                PhotoDailySummaryVO current = userSummaries.get(i);
                System.out.println("处理分组 " + i + ", 开始时间: " + current.getStartTime());

                if (i == 0) {
                    // 第一个分组，检查是否有历史数据
                    if (userLastHistoryPhoto != null) {
                        // 有历史数据，计算与历史数据的距离和时间差
                        calculateDistanceFromHistory(current, userLastHistoryPhoto);
                        calculateTimeDiffFromHistory(current, userLastHistoryPhoto);

                        // 添加日志输出，用于调试
                        System.out.println("用户 " + userId + " 找到历史数据: " + userLastHistoryPhoto.getCaptureTime());
                    } else {
                        // 没有历史数据
                        current.setFormattedDistance("0米");
                        current.setFormattedTimeDiff("0分钟");

                        // 添加日志输出，用于调试
                        System.out.println("用户 " + userId + " 没有找到历史数据");
                    }
                } else {
                    // 后续分组，计算与上一个分组的距离和时间差
                    PhotoDailySummaryVO previous = userSummaries.get(i - 1);

                    // 计算距离
                    calculateDistanceBetweenGroups(current, previous);

                    // 计算时间差
                    calculateTimeDiffBetweenGroups(current, previous);
                }

                // 修复单张照片的工作时长显示
                fixSinglePhotoWorkingTime(current);
            }
        }
    }

    /**
     * 计算两个分组之间的距离
     */
    private void calculateDistanceBetweenGroups(PhotoDailySummaryVO current, PhotoDailySummaryVO previous) {
        if (current.getPhotos() != null && !current.getPhotos().isEmpty() &&
            previous.getPhotos() != null && !previous.getPhotos().isEmpty()) {

            WatermarkPhoto currentPhoto = current.getPhotos().get(0);
            WatermarkPhoto previousPhoto = previous.getPhotos().get(0);

            if (currentPhoto.getLongitude() != null && currentPhoto.getLatitude() != null &&
                previousPhoto.getLongitude() != null && previousPhoto.getLatitude() != null) {

                double distanceInMeters = GeoUtils.calculateDistance(
                        previousPhoto.getLongitude(), previousPhoto.getLatitude(),
                        currentPhoto.getLongitude(), currentPhoto.getLatitude()
                );

                String formattedDistance = formatDistanceWithUnit(distanceInMeters);
                current.setFormattedDistance(formattedDistance);
            } else {
                current.setFormattedDistance("0米");
            }
        } else {
            current.setFormattedDistance("0米");
        }
    }

    /**
     * 计算两个分组之间的时间差
     */
    private void calculateTimeDiffBetweenGroups(PhotoDailySummaryVO current, PhotoDailySummaryVO previous) {
        Date currentTime = current.getStartTime();
        Date previousTime = previous.getEndTime() != null ? previous.getEndTime() : previous.getStartTime();

        if (currentTime != null && previousTime != null) {
            long timeDiffMillis = currentTime.getTime() - previousTime.getTime();
            if (timeDiffMillis > 0) {
                String formattedTimeDiff = formatWorkingTimeWithUnit(timeDiffMillis);
                current.setFormattedTimeDiff(formattedTimeDiff);
            } else {
                current.setFormattedTimeDiff("0分钟");
            }
        } else {
            current.setFormattedTimeDiff("0分钟");
        }
    }

    /**
     * 计算与历史照片的距离
     */
    private void calculateDistanceFromHistory(PhotoDailySummaryVO current, WatermarkPhoto historyPhoto) {
        if (current.getPhotos() != null && !current.getPhotos().isEmpty()) {
            WatermarkPhoto currentPhoto = current.getPhotos().get(0);

            if (currentPhoto.getLongitude() != null && currentPhoto.getLatitude() != null &&
                historyPhoto.getLongitude() != null && historyPhoto.getLatitude() != null) {

                double distanceInMeters = GeoUtils.calculateDistance(
                        historyPhoto.getLongitude(), historyPhoto.getLatitude(),
                        currentPhoto.getLongitude(), currentPhoto.getLatitude()
                );

                String formattedDistance = formatDistanceWithUnit(distanceInMeters);
                current.setFormattedDistance(formattedDistance);
            } else {
                current.setFormattedDistance("0米");
            }
        } else {
            current.setFormattedDistance("0米");
        }
    }

    /**
     * 计算与历史照片的时间差
     */
    private void calculateTimeDiffFromHistory(PhotoDailySummaryVO current, WatermarkPhoto historyPhoto) {
        Date currentTime = current.getStartTime();
        Date historyTime = historyPhoto.getCaptureTime();

        // 添加调试信息
        System.out.println("当前时间: " + currentTime);
        System.out.println("历史时间: " + historyTime);

        if (currentTime != null && historyTime != null) {
            long timeDiffMillis = currentTime.getTime() - historyTime.getTime();
            System.out.println("时间差毫秒: " + timeDiffMillis);
            System.out.println("时间差小时: " + (timeDiffMillis / (1000 * 60 * 60.0)));

            if (timeDiffMillis > 0) {
                String formattedTimeDiff = formatWorkingTimeWithUnit(timeDiffMillis);
                System.out.println("格式化时间差: " + formattedTimeDiff);
                current.setFormattedTimeDiff(formattedTimeDiff);
            } else {
                current.setFormattedTimeDiff("0分钟");
            }
        } else {
            current.setFormattedTimeDiff("0分钟");
        }
    }

    /**
     * 修复单张照片的工作时长显示
     */
    private void fixSinglePhotoWorkingTime(PhotoDailySummaryVO summary) {
        // 不做任何修改，保持原有的工作时长计算逻辑
        // 如果是单张照片，工作时长就是0分钟，这是正确的
    }

    /**
     * 按时间和距离进行组合分组（5分钟内且100米内算一组）
     *
     * @param photos 照片列表
     * @return 按用户分组的时间距离组合结果
     */
    private Map<String, List<List<WatermarkPhoto>>> groupByTimeAndDistance(List<WatermarkPhoto> photos) {
        // 先按用户分组
        Map<String, List<WatermarkPhoto>> userPhotosMap = photos.stream()
                .collect(Collectors.groupingBy(WatermarkPhoto::getCreatedBy));

        Map<String, List<List<WatermarkPhoto>>> result = new HashMap<>();

        for (Map.Entry<String, List<WatermarkPhoto>> userEntry : userPhotosMap.entrySet()) {
            String userId = userEntry.getKey();
            List<WatermarkPhoto> userPhotos = userEntry.getValue();

            // 按时间排序
            userPhotos.sort(Comparator.comparing(WatermarkPhoto::getCaptureTime,
                    Comparator.nullsLast(Comparator.naturalOrder())));

            // 找到当天第一个时间
            Date firstTime = userPhotos.stream()
                    .map(WatermarkPhoto::getCaptureTime)
                    .filter(Objects::nonNull)
                    .min(Date::compareTo)
                    .orElse(null);

            // 进行时间和距离的组合分组
            List<List<WatermarkPhoto>> userGroups = performTimeDistanceGrouping(userPhotos, firstTime);
            result.put(userId, userGroups);
        }

        return result;
    }

    /**
     * 执行时间和距离的组合分组
     *
     * @param photos 用户的照片列表（已按时间排序）
     * @param firstTime 当天第一个时间
     * @return 分组结果
     */
    private List<List<WatermarkPhoto>> performTimeDistanceGrouping(List<WatermarkPhoto> photos, Date firstTime) {
        List<List<WatermarkPhoto>> groups = new ArrayList<>();

        for (WatermarkPhoto photo : photos) {
            boolean addedToExistingGroup = false;

            // 尝试将照片加入现有分组
            for (List<WatermarkPhoto> group : groups) {
                if (canAddToGroup(photo, group, firstTime)) {
                    group.add(photo);
                    addedToExistingGroup = true;
                    break;
                }
            }

            // 如果无法加入现有分组，创建新分组
            if (!addedToExistingGroup) {
                List<WatermarkPhoto> newGroup = new ArrayList<>();
                newGroup.add(photo);
                groups.add(newGroup);
            }
        }

        return groups;
    }

    /**
     * 判断照片是否可以加入指定分组
     *
     * @param photo 待判断的照片
     * @param group 现有分组
     * @param firstTime 当天第一个时间
     * @return 是否可以加入
     */
    private boolean canAddToGroup(WatermarkPhoto photo, List<WatermarkPhoto> group, Date firstTime) {
        if (group.isEmpty()) {
            return true;
        }

        // 检查时间条件：5分钟内
        for (WatermarkPhoto groupPhoto : group) {
            if (!isWithinTimeRange(photo, groupPhoto, firstTime)) {
                return false;
            }
        }

        // 检查距离条件：100米内
        for (WatermarkPhoto groupPhoto : group) {
            if (!isWithinDistanceRange(photo, groupPhoto)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 检查两张照片是否在5分钟时间范围内
     *
     * @param photo1 照片1
     * @param photo2 照片2
     * @param firstTime 当天第一个时间
     * @return 是否在时间范围内
     */
    private boolean isWithinTimeRange(WatermarkPhoto photo1, WatermarkPhoto photo2, Date firstTime) {
        if (photo1.getCaptureTime() == null || photo2.getCaptureTime() == null || firstTime == null) {
            return false;
        }

        // 计算两张照片相对于第一个时间的分组索引
        long group1Index = getTimeGroupIndex(photo1.getCaptureTime(), firstTime);
        long group2Index = getTimeGroupIndex(photo2.getCaptureTime(), firstTime);

        // 同一个5分钟时间组
        return group1Index == group2Index;
    }

    /**
     * 检查两张照片是否在100米距离范围内
     *
     * @param photo1 照片1
     * @param photo2 照片2
     * @return 是否在距离范围内
     */
    private boolean isWithinDistanceRange(WatermarkPhoto photo1, WatermarkPhoto photo2) {
        if (photo1.getLongitude() == null || photo1.getLatitude() == null ||
            photo2.getLongitude() == null || photo2.getLatitude() == null) {
            return false;
        }

        double distance = GeoUtils.calculateDistance(
                photo1.getLongitude(), photo1.getLatitude(),
                photo2.getLongitude(), photo2.getLatitude()
        );

        return distance <= 100.0; // 100米以内
    }

    /**
     * 获取照片的时间分组索引
     *
     * @param captureTime 拍摄时间
     * @param firstTime 当天第一个时间
     * @return 时间分组索引
     */
    private long getTimeGroupIndex(Date captureTime, Date firstTime) {
        if (captureTime == null || firstTime == null) {
            return 0;
        }

        long timeDiffMillis = captureTime.getTime() - firstTime.getTime();
        long timeDiffMinutes = timeDiffMillis / (60 * 1000);

        return timeDiffMinutes / 5; // 每5分钟一组
    }

    /**
     * 为分组生成有意义的时间键
     *
     * @param photos 分组中的照片列表
     * @return 有意义的时间键
     */
    private String generateMeaningfulTimeKey(List<WatermarkPhoto> photos) {
        if (photos == null || photos.isEmpty()) {
            return "unknown";
        }

        // 找到最早的时间
        Date earliestTime = photos.stream()
                .map(WatermarkPhoto::getCaptureTime)
                .filter(Objects::nonNull)
                .min(Date::compareTo)
                .orElse(null);

        if (earliestTime == null) {
            return "unknown";
        }

        SimpleDateFormat sdf = new SimpleDateFormat("HH:mm");
        return sdf.format(earliestTime);
    }
}
