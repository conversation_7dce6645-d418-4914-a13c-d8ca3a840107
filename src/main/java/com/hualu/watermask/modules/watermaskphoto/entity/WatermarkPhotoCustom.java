package com.hualu.watermask.modules.watermaskphoto.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 照片自定义内容表
 */
@TableName(value = "WATERMARK_PHOTO_CUSTOM")
public class WatermarkPhotoCustom {
    /**
     * 自定义项唯一标识符
     */
    @TableId(value = "CUSTOM_ID")
    private String customId;

    /**
     * 关联照片ID
     */
    @TableField(value = "PHOTO_ID")
    private String photoId;

    /**
     * 字段标题
     */
    @TableField(value = "TITLE")
    private String title;

    /**
     * 自定义内容
     */
    @TableField(value = "CONTENT")
    private String content;

    /**
     * 字段显示顺序
     */
    @TableField(value = "SORT_ORDER")
    private Integer sortOrder;
    
    /**
     * 字体颜色（例如：#FFFFFF）
     */
    @TableField(value = "FONT_COLOR")
    private String fontColor;
    
    /**
     * 背景颜色（例如：#000000）
     */
    @TableField(value = "BACKGROUND_COLOR")
    private String backgroundColor;

    /**
     * 输入类型,1表示输入,其它表示查看
     */
    @TableField(value = "INPUT_TYPE")
    private String inputType;

    /**
     * 是否标题，1为标题，0为内容
     */
    @TableField(value = "WHETHER_TITLE")
    private String whetherTitle;

    @TableField(value = "ICON")
    private String icon;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    /**
     * 获取自定义项唯一标识符
     *
     * @return CUSTOM_ID - 自定义项唯一标识符
     */
    public String getCustomId() {
        return customId;
    }

    /**
     * 设置自定义项唯一标识符
     *
     * @param customId 自定义项唯一标识符
     */
    public void setCustomId(String customId) {
        this.customId = customId;
    }

    /**
     * 获取关联照片ID
     *
     * @return PHOTO_ID - 关联照片ID
     */
    public String getPhotoId() {
        return photoId;
    }

    /**
     * 设置关联照片ID
     *
     * @param photoId 关联照片ID
     */
    public void setPhotoId(String photoId) {
        this.photoId = photoId;
    }

    /**
     * 获取字段标题
     *
     * @return TITLE - 字段标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 设置字段标题
     *
     * @param title 字段标题
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取自定义内容
     *
     * @return CONTENT - 自定义内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置自定义内容
     *
     * @param content 自定义内容
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 获取字段显示顺序
     *
     * @return SORT_ORDER - 字段显示顺序
     */
    public Integer getSortOrder() {
        return sortOrder;
    }

    /**
     * 设置字段显示顺序
     *
     * @param sortOrder 字段显示顺序
     */
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    /**
     * 获取字体颜色
     *
     * @return FONT_COLOR - 字体颜色
     */
    public String getFontColor() {
        return fontColor;
    }

    /**
     * 设置字体颜色
     *
     * @param fontColor 字体颜色
     */
    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    /**
     * 获取背景颜色
     *
     * @return BACKGROUND_COLOR - 背景颜色
     */
    public String getBackgroundColor() {
        return backgroundColor;
    }

    /**
     * 设置背景颜色
     *
     * @param backgroundColor 背景颜色
     */
    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public String getInputType() {
        return inputType;
    }

    public void setInputType(String inputType) {
        this.inputType = inputType;
    }

    public String getWhetherTitle() {
        return whetherTitle;
    }

    public void setWhetherTitle(String whetherTitle) {
        this.whetherTitle = whetherTitle;
    }
}