package com.hualu.watermask.modules.watermaskphoto.utils;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.alibaba.excel.write.handler.RowWriteHandler;
import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFDrawing;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.IIOImage;
import javax.imageio.ImageIO;
import javax.imageio.ImageWriteParam;
import javax.imageio.ImageWriter;
import javax.imageio.stream.ImageOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.HttpURLConnection;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.concurrent.CompletableFuture;

/**
 * Excel导出工具类，使用EasyExcel处理大量图片和数据
 * 针对大数据量和大量图片进行了性能优化
 */
public class ExcelExportUtils {

    private static final Logger log = LoggerFactory.getLogger(ExcelExportUtils.class);

    /**
     * 格式化单元格值，根据字段类型进行适当处理
     *
     * @param fieldName 字段名称
     * @param value 字段值
     * @return 格式化后的值
     */
    private static Object formatCellValue(String fieldName, Object value) {
        if (value == null) {
            return "";
        }

        // 根据字段名特殊处理
        if ("captureTime".equals(fieldName) && value instanceof Date) {
            // 日期时间格式化
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format((Date) value);
        } else if (value instanceof BigDecimal) {
            // 数值类型处理
            BigDecimal decimal = (BigDecimal) value;
            if (fieldName.contains("longitude") || fieldName.contains("latitude")) {
                // 经纬度保留6位小数
                return decimal.setScale(6, RoundingMode.HALF_UP).toString();
            } else if (fieldName.contains("elevation")) {
                // 高度保留2位小数
                return decimal.setScale(2, RoundingMode.HALF_UP).toString();
            } else if (fieldName.contains("azimuth") || fieldName.contains("speed")) {
                // 方位角和速度保留3位小数
                return decimal.setScale(3, RoundingMode.HALF_UP).toString();
            }
            return decimal.toString();
        }

        // 默认转换为字符串
        return value.toString();
    }

    /**
     * 导出照片台账到Excel
     *
     * @param response HTTP响应
     * @param data 数据列表
     * @param headerMap 表头映射（字段名 -> 表头名）
     * @param imageField 图片路径字段名
     * @param title 标题
     */
    public static void exportPhotoLedger(HttpServletResponse response,
                                         List<Map<String, Object>> data,
                                         Map<String, String> headerMap,
                                         String imageField,
                                         String title) throws IOException {
        exportPhotoLedger(response, data, headerMap, imageField, title, false);
    }

    /**
     * 导出照片台账到Excel
     *
     * @param response HTTP响应
     * @param data 数据列表
     * @param headerMap 表头映射（字段名 -> 表头名）
     * @param imageField 图片路径字段名
     * @param title 标题
     * @param enableFilter 是否启用筛选功能
     */
    public static void exportPhotoLedger(HttpServletResponse response,
                                         List<Map<String, Object>> data,
                                         Map<String, String> headerMap,
                                         String imageField,
                                         String title,
                                         boolean enableFilter) throws IOException {
        // 水印图字段名
        String watermarkImageField = "watermarkPhotoUrl";

        try {
            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode(title, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");

            // 创建临时文件 - 先写入临时文件再输出到response，避免图片处理时间长导致响应超时
            File tempFile = File.createTempFile("excel_export_", ".xlsx");

            log.info("创建临时文件: {}", tempFile.getAbsolutePath());

            try (FileOutputStream fileOut = new FileOutputStream(tempFile);
                 XSSFWorkbook workbook = new XSSFWorkbook()) {

                // 创建工作表
                XSSFSheet sheet = workbook.createSheet(title);

                // 创建标题行样式
                CellStyle titleStyle = workbook.createCellStyle();
                // 移除背景色填充
                Font titleFont = workbook.createFont();
                titleFont.setBold(true);
                titleFont.setFontHeightInPoints((short) 16);
                // 移除字体颜色设置
                titleStyle.setFont(titleFont);
                titleStyle.setAlignment(HorizontalAlignment.CENTER);
                titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                // 设置标题行 - 使用合并单元格
                Row titleRow = sheet.createRow(0);
                titleRow.setHeight((short)600); // 设置行高

                // 计算列数用于合并单元格
                int columnCount = headerMap.size();

                // 创建标题单元格
                Cell titleCell = titleRow.createCell(0);
                titleCell.setCellValue(title);
                titleCell.setCellStyle(titleStyle);

                // 应用样式到所有标题行单元格
                for (int i = 0; i < columnCount; i++) {
                    Cell cell = titleRow.getCell(i);
                    if (cell == null) {
                        cell = titleRow.createCell(i);
                    }
                    cell.setCellStyle(titleStyle);
                }

                // 合并标题行单元格
                sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, columnCount - 1));

                // 创建表头行
                Row headerRow = sheet.createRow(1);
                headerRow.setHeight((short)400); // 设置表头行高

                // 表头样式
                CellStyle headerStyle = workbook.createCellStyle();
                // 移除背景色填充
                Font headerFont = workbook.createFont();
                headerFont.setBold(true);
                headerFont.setFontHeightInPoints((short) 11);
                headerStyle.setFont(headerFont);
                headerStyle.setAlignment(HorizontalAlignment.CENTER);
                headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                // 创建所有表头单元格
                List<String> fieldKeys = new ArrayList<>(headerMap.keySet());
                for (int i = 0; i < fieldKeys.size(); i++) {
                    Cell cell = headerRow.createCell(i);
                    cell.setCellValue(headerMap.get(fieldKeys.get(i)));
                    cell.setCellStyle(headerStyle);
                }

                // 设置列宽 - 增加图片列宽度
                sheet.setColumnWidth(0, 256 * 35); // 原图列 - 35个字符宽度
                sheet.setColumnWidth(1, 256 * 35); // 水印图列 - 35个字符宽度
                for (int i = 2; i < fieldKeys.size(); i++) {
                    if (i <= 6) {
                        sheet.setColumnWidth(i, 256 * 18);
                    } else {
                        sheet.setColumnWidth(i, 256 * 12);
                    }
                }

                // 数据行样式 - 不设置底色
                CellStyle dataStyle = workbook.createCellStyle();
                Font dataFont = workbook.createFont();
                dataFont.setFontHeightInPoints((short) 10);
                dataStyle.setFont(dataFont);
                dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);

                // 日期样式
                CellStyle dateStyle = workbook.createCellStyle();
                dateStyle.cloneStyleFrom(dataStyle);
                CreationHelper createHelper = workbook.getCreationHelper();
                dateStyle.setDataFormat(createHelper.createDataFormat().getFormat("yyyy-MM-dd HH:mm:ss"));

                // 处理数据行 - 提前下载所有图片以提高效率
                log.info("开始处理{}条数据", data.size());

                // 创建绘图区域用于插入图片
                XSSFDrawing drawing = sheet.createDrawingPatriarch();

                // 创建线程池进行并行下载
                int cpuCount = Runtime.getRuntime().availableProcessors();
                int threadCount = Math.min(cpuCount * 2, 20); // CPU核心数*2，最多20个线程
                ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

                // 预加载所有图片 - 使用CompletableFuture进行异步处理
                Map<Integer, byte[]> originalImageCache = new ConcurrentHashMap<>();
                Map<Integer, byte[]> watermarkImageCache = new ConcurrentHashMap<>();

                List<CompletableFuture<Void>> downloadFutures = new ArrayList<>();

                for (int rowIndex = 0; rowIndex < data.size(); rowIndex++) {
                    final int currentRowIndex = rowIndex;
                    Map<String, Object> rowData = data.get(rowIndex);

                    // 异步下载原图
                    CompletableFuture<Void> originalFuture = CompletableFuture.runAsync(() -> {
                        String originalUrl = (String) rowData.get(imageField);
                        if (originalUrl != null && !originalUrl.isEmpty()) {
                            try {
                                // 下载图片
                                byte[] imageBytes = null;
                                HttpURLConnection connection = null;
                                InputStream inputStream = null;
                                ByteArrayOutputStream outputStream = null;

                                try {
                                    URL url = new URL(originalUrl);
                                    connection = (HttpURLConnection) url.openConnection();
                                    connection.setRequestMethod("GET");
                                    connection.setConnectTimeout(10000);
                                    connection.setReadTimeout(10000);

                                    if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                                        int contentLength = connection.getContentLength();
                                        inputStream = connection.getInputStream();

                                        outputStream = new ByteArrayOutputStream(
                                                contentLength > 0 ? contentLength : 1024 * 1024);

                                        byte[] buffer = new byte[16384];
                                        int bytesRead;
                                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                                            outputStream.write(buffer, 0, bytesRead);
                                        }

                                        imageBytes = outputStream.toByteArray();

                                        if (imageBytes != null && imageBytes.length > 0) {
                                            originalImageCache.put(currentRowIndex, imageBytes);
                                            log.info("预加载原图 - 行: {}, 大小: {}KB", currentRowIndex, imageBytes.length / 1024);
                                        }
                                    }
                                } finally {
                                    if (outputStream != null) outputStream.close();
                                    if (inputStream != null) inputStream.close();
                                    if (connection != null) connection.disconnect();
                                }
                            } catch (Exception e) {
                                log.error("预加载原图失败 - 行: {}, URL: {}, 错误: {}", currentRowIndex, originalUrl, e.getMessage());
                            }
                        }
                    }, executorService);

                    downloadFutures.add(originalFuture);

                    // 异步下载水印图
                    CompletableFuture<Void> watermarkFuture = CompletableFuture.runAsync(() -> {
                        String watermarkUrl = (String) rowData.get(watermarkImageField);
                        if (watermarkUrl != null && !watermarkUrl.isEmpty()) {
                            try {
                                // 下载图片
                                byte[] imageBytes = null;
                                HttpURLConnection connection = null;
                                InputStream inputStream = null;
                                ByteArrayOutputStream outputStream = null;

                                try {
                                    URL url = new URL(watermarkUrl);
                                    connection = (HttpURLConnection) url.openConnection();
                                    connection.setRequestMethod("GET");
                                    connection.setConnectTimeout(10000);
                                    connection.setReadTimeout(10000);

                                    if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                                        int contentLength = connection.getContentLength();
                                        inputStream = connection.getInputStream();

                                        outputStream = new ByteArrayOutputStream(
                                                contentLength > 0 ? contentLength : 1024 * 1024);

                                        byte[] buffer = new byte[16384];
                                        int bytesRead;
                                        while ((bytesRead = inputStream.read(buffer)) != -1) {
                                            outputStream.write(buffer, 0, bytesRead);
                                        }

                                        imageBytes = outputStream.toByteArray();

                                        if (imageBytes != null && imageBytes.length > 0) {
                                            watermarkImageCache.put(currentRowIndex, imageBytes);
                                            log.info("预加载水印图 - 行: {}, 大小: {}KB", currentRowIndex, imageBytes.length / 1024);
                                        }
                                    }
                                } finally {
                                    if (outputStream != null) outputStream.close();
                                    if (inputStream != null) inputStream.close();
                                    if (connection != null) connection.disconnect();
                                }
                            } catch (Exception e) {
                                log.error("预加载水印图失败 - 行: {}, URL: {}, 错误: {}", currentRowIndex, watermarkUrl, e.getMessage());
                            }
                        }
                    }, executorService);

                    downloadFutures.add(watermarkFuture);
                }

                // 等待所有下载任务完成
                CompletableFuture<Void> allDownloads = CompletableFuture.allOf(
                        downloadFutures.toArray(new CompletableFuture[0]));

                try {
                    // 设置超时等待，防止无限等待
                    allDownloads.get(60, TimeUnit.SECONDS);
                } catch (Exception e) {
                    log.warn("部分图片下载超时，继续处理已下载的图片");
                }

                // 关闭线程池
                executorService.shutdown();

                log.info("图片预加载完成，开始写入Excel");

                // 处理每行数据和图片
                for (int rowIndex = 0; rowIndex < data.size(); rowIndex++) {
                    // 获取当前行的数据
                    Map<String, Object> rowData = data.get(rowIndex);

                    // 计算Excel中的实际行号 (数据索引+2，因为有标题行和表头行)
                    int excelRowNum = rowIndex + 2;

                    // 创建数据行
                    Row dataRow = sheet.createRow(excelRowNum);
                    dataRow.setHeight((short) 1500);

                    // 处理每一列的数据
                    for (int colIndex = 0; colIndex < fieldKeys.size(); colIndex++) {
                        String fieldKey = fieldKeys.get(colIndex);
                        Object value = rowData.get(fieldKey);

                        // 创建单元格并设置样式
                        Cell cell = dataRow.createCell(colIndex);

                        // 对于图片字段，不设置内容，后面单独处理
                        if (fieldKey.equals(imageField) || fieldKey.equals(watermarkImageField)) {
                            continue; // 图片字段留空
                        }

                        // 根据数据类型设置单元格值和样式
                        if (value instanceof Date) {
                            cell.setCellValue((Date) value);
                            cell.setCellStyle(dateStyle);
                        } else if (value instanceof Number) {
                            cell.setCellValue(((Number) value).doubleValue());
                            cell.setCellStyle(dataStyle);
                        } else {
                            cell.setCellValue(value != null ? value.toString() : "");
                            cell.setCellStyle(dataStyle);
                        }
                    }

                    // 从缓存中获取并添加原图到第一列
                    byte[] originalImageBytes = originalImageCache.get(rowIndex);
                    if (originalImageBytes != null && originalImageBytes.length > 0) {
                        try {
                            // 添加图片到工作簿
                            int pictureIdx = workbook.addPicture(originalImageBytes, Workbook.PICTURE_TYPE_JPEG);

                            // 创建图片锚点
                            ClientAnchor anchor = createCompatibleAnchor(workbook, drawing, 0, excelRowNum, 1, excelRowNum + 1);
                            anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);

                            // 添加图片
                            drawing.createPicture(anchor, pictureIdx);
                            log.info("添加原图({}KB)到第{}行", originalImageBytes.length/1024, excelRowNum);
                        } catch (Exception e) {
                            log.error("处理原图失败，行{}: {}", excelRowNum, e.getMessage());
                        }
                    }

                    // 从缓存中获取并添加水印图到第二列
                    byte[] watermarkImageBytes = watermarkImageCache.get(rowIndex);
                    if (watermarkImageBytes != null && watermarkImageBytes.length > 0) {
                        try {
                            // 添加图片到工作簿
                            int pictureIdx = workbook.addPicture(watermarkImageBytes, Workbook.PICTURE_TYPE_JPEG);

                            // 创建图片锚点
                            ClientAnchor anchor = createCompatibleAnchor(workbook, drawing, 1, excelRowNum, 2, excelRowNum + 1);
                            anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);

                            // 添加图片
                            drawing.createPicture(anchor, pictureIdx);
                            log.info("添加水印图({}KB)到第{}行", watermarkImageBytes.length/1024, excelRowNum);
                        } catch (Exception e) {
                            log.error("处理水印图失败，行{}: {}", excelRowNum, e.getMessage());
                        }
                    }
                }

                // 如果启用筛选功能，为表头行添加自动筛选
                if (enableFilter) {
                    // 设置自动筛选范围：从表头行开始到最后一行和最后一列
                    int lastRowNum = sheet.getLastRowNum();
                    int lastColNum = fieldKeys.size() - 1;
                    if (lastRowNum > 1 && lastColNum >= 0) {
                        // 筛选范围：表头行(第1行)到最后一行，第0列到最后一列
                        org.apache.poi.ss.util.CellRangeAddress filterRange =
                            new org.apache.poi.ss.util.CellRangeAddress(1, lastRowNum, 0, lastColNum);
                        sheet.setAutoFilter(filterRange);
                        log.info("已为Excel表格启用自动筛选功能，范围: 行{}-{}, 列{}-{}",
                                1, lastRowNum, 0, lastColNum);
                    }
                }

                // 冻结标题行和表头行
                sheet.createFreezePane(0, 2);

                // 先保存到临时文件
                workbook.write(fileOut);
            }

            // 从临时文件读取并写入到响应
            try (FileInputStream fileIn = new FileInputStream(tempFile);
                 OutputStream out = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fileIn.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }

            // 删除临时文件
            if (!tempFile.delete()) {
                tempFile.deleteOnExit();
            }

        } catch (Exception e) {
            log.error("导出照片台账失败: {}", e.getMessage(), e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"导出照片台账失败: " + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("设置错误响应失败", ex);
            }
        }
    }

    /**
     * 导出照片台账到指定的OutputStream
     *
     * @param outputStream 输出流
     * @param data 数据列表
     * @param headerMap 表头映射
     * @param imageField 图片字段名
     * @param title 标题
     * @throws IOException IO异常
     */
    public static void exportPhotoLedgerToStream(OutputStream outputStream,
                                          List<Map<String, Object>> data,
                                          Map<String, String> headerMap,
                                          String imageField,
                                          String title) throws IOException {
        exportPhotoLedgerToStream(outputStream, data, headerMap, imageField, title, false);
    }

    /**
     * 导出照片台账到指定的OutputStream
     *
     * @param outputStream 输出流
     * @param data 数据列表
     * @param headerMap 表头映射
     * @param imageField 图片字段名
     * @param title 标题
     * @param enableFilter 是否启用筛选功能
     * @throws IOException IO异常
     */
    public static void exportPhotoLedgerToStream(OutputStream outputStream,
                                          List<Map<String, Object>> data,
                                          Map<String, String> headerMap,
                                          String imageField,
                                          String title,
                                          boolean enableFilter) throws IOException {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("导出数据不能为空");
        }

        // 确定原图和水印图字段名
        String originalImageField = null;
        String watermarkImageField = null;

        // 检查数据中是否包含原图和水印图字段
        if (!data.isEmpty()) {
            Map<String, Object> firstRow = data.get(0);
            if (firstRow.containsKey("originalPhotoUrl")) {
                originalImageField = "originalPhotoUrl";
            }
            if (firstRow.containsKey("watermarkPhotoUrl")) {
                watermarkImageField = "watermarkPhotoUrl";
            }
        }

        log.info("检测到的图片字段 - 原图: {}, 水印图: {}, 主字段: {}",
                originalImageField, watermarkImageField, imageField);

        // 创建数据副本，避免修改原始数据影响图片处理
        List<Map<String, Object>> processedData = new ArrayList<>();
        for (Map<String, Object> originalRow : data) {
            Map<String, Object> processedRow = new LinkedHashMap<>(originalRow);
            // 处理图片字段，将其替换为空值，以便EasyExcel处理时不显示URL文本
            if (originalImageField != null && processedRow.containsKey(originalImageField)) {
                processedRow.put(originalImageField, "");
            }
            if (watermarkImageField != null && processedRow.containsKey(watermarkImageField)) {
                processedRow.put(watermarkImageField, "");
            }
            processedData.add(processedRow);
        }

        // 计算图片字段在表头中的列索引
        List<String> headerKeys = new ArrayList<>(headerMap.keySet());
        int originalImageColumnIndex = -1;
        int watermarkImageColumnIndex = -1;

        if (originalImageField != null) {
            originalImageColumnIndex = headerKeys.indexOf(originalImageField);
        }
        if (watermarkImageField != null) {
            watermarkImageColumnIndex = headerKeys.indexOf(watermarkImageField);
        }

        log.info("图片字段列索引 - 原图: {} (字段: {}), 水印图: {} (字段: {})",
                originalImageColumnIndex, originalImageField,
                watermarkImageColumnIndex, watermarkImageField);

        // 使用原始数据（包含URL）进行图片预加载和处理
        log.info("创建图片处理器前，检查原始数据中的URL:");
        if (!data.isEmpty()) {
            Map<String, Object> firstRow = data.get(0);
            String origUrl = (String) firstRow.get(originalImageField);
            String waterUrl = (String) firstRow.get(watermarkImageField);
            log.info("第一行原始数据 - 原图URL: {}, 水印图URL: {}",
                    origUrl != null ? origUrl : "null",
                    waterUrl != null ? waterUrl : "null");
        }

        PhotoImageHandler photoImageHandler = new PhotoImageHandler(data, originalImageField, watermarkImageField,
                originalImageColumnIndex, watermarkImageColumnIndex);

        // 转换数据格式，添加列序号
        List<List<Object>> rowDataList = new ArrayList<>();

        // 添加表头
        List<Object> headerRow = new ArrayList<>();
        List<String> headerList = new ArrayList<>(headerMap.values());
        for (String header : headerList) {
            headerRow.add(header);
        }
        rowDataList.add(headerRow);

        // 添加数据行 - 使用处理后的数据（URL已清空，避免在Excel中显示URL文本）
        for (Map<String, Object> row : processedData) {
            List<Object> rowData = new ArrayList<>();
            for (String fieldName : headerMap.keySet()) {
                Object value = row.get(fieldName);

                // 排除字体颜色和背景色字段
                if (fieldName.endsWith("_FONT_COLOR") || fieldName.endsWith("_BACKGROUND_COLOR")) {
                    continue;
                }

                // 格式化单元格值
                rowData.add(formatCellValue(fieldName, value));
            }
            rowDataList.add(rowData);
        }

        // 设置表头和内容的样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
        WriteFont headWriteFont = new WriteFont();
        headWriteFont.setFontHeightInPoints((short) 12);
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);

        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        WriteFont contentWriteFont = new WriteFont();
        contentWriteFont.setFontHeightInPoints((short) 11);
        contentWriteCellStyle.setWriteFont(contentWriteFont);

        HorizontalCellStyleStrategy horizontalCellStyleStrategy =
                new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);

        try {
            // 创建ExcelWriterBuilder
            ExcelWriterBuilder writerBuilder = EasyExcel.write(outputStream)
                    .excelType(ExcelTypeEnum.XLSX)
                    .registerWriteHandler(photoImageHandler)
                    .registerWriteHandler(new TitleHandler(title, headerList.size()))
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    .registerWriteHandler(new ColumnWidthHandler());

            // 如果启用筛选功能，添加筛选处理器
            // if (enableFilter) {
            //     writerBuilder.registerWriteHandler(new FilterHandler(headerList.size()));
            // }
            // FilterHandler 已删除，因为从未被使用

            // 构建ExcelWriter
            ExcelWriter excelWriter = writerBuilder.build();

            WriteSheet writeSheet = EasyExcel.writerSheet("Sheet1").build();
            excelWriter.write(rowDataList, writeSheet);
            excelWriter.finish();
        } catch (Exception e) {
            log.error("导出照片台账失败: {}", e.getMessage(), e);
            throw new IOException("导出照片台账失败: " + e.getMessage(), e);
        }
    }

    /**
     * 图片处理器，用于在Excel中插入图片
     * 使用并行预加载和缓存优化性能
     */
    private static class PhotoImageHandler implements RowWriteHandler {
        private final List<Map<String, Object>> data;
        private final String imageField;
        private final String watermarkImageField;
        private final int originalImageColumnIndex;
        private final int watermarkImageColumnIndex;
        // 使用Map<列索引, Map<行索引, 图片数据>>的结构来存储不同列的图片
        private final Map<Integer, Map<Integer, byte[]>> imageCache = new ConcurrentHashMap<>();

        public PhotoImageHandler(List<Map<String, Object>> data, String imageField, String watermarkImageField,
                                int originalImageColumnIndex, int watermarkImageColumnIndex) {
            this.data = data;
            this.imageField = imageField;
            this.watermarkImageField = watermarkImageField;
            this.originalImageColumnIndex = originalImageColumnIndex;
            this.watermarkImageColumnIndex = watermarkImageColumnIndex;

            // 确保重置图片缓存并预分配足够空间
            int capacity = Math.max(data.size() * 2, 100); // 每行两个索引，至少100个容量

            // 根据实际的列索引初始化缓存
            if (originalImageColumnIndex >= 0) {
                imageCache.put(originalImageColumnIndex, new ConcurrentHashMap<>(capacity));
            }
            if (watermarkImageColumnIndex >= 0) {
                imageCache.put(watermarkImageColumnIndex, new ConcurrentHashMap<>(capacity));
            }

            log.info("初始化图片处理器, 数据行数: {}, 原图列索引: {}, 水印图列索引: {}, 预分配缓存容量: {}",
                    data.size(), originalImageColumnIndex, watermarkImageColumnIndex, capacity);
            log.info("图片字段名 - 原图: {}, 水印图: {}", imageField, watermarkImageField);

            // 禁用预加载，直接在行处理时下载图片（更简单可靠）
            log.info("跳过预加载，将在行处理时直接下载图片");
        }

        /**
         * 并行预加载图片数据 - 性能优化版本
         */
        private void preloadImages() {
            try {
                // 批次加载策略，避免创建过多线程和连接
                int batchSize = 10; // 每批处理10张照片
                int cpuCount = Runtime.getRuntime().availableProcessors();
                int threadCount = Math.min(cpuCount * 2, 20); // CPU核心数*2，最多20个线程

                if (data.isEmpty()) return;

                // 使用限定大小的线程池，防止过多线程资源占用
                ExecutorService executorService = Executors.newFixedThreadPool(threadCount);

                // 分批次提交任务
                int totalRecords = data.size();
                int totalBatches = (totalRecords + batchSize - 1) / batchSize;

                // 清空缓存，确保不受先前数据影响
                for (Map<Integer, byte[]> cache : imageCache.values()) {
                    if (cache != null) {
                        cache.clear();
                    }
                }

                // 打印日志便于调试
                log.info("开始预加载{}条记录的图片数据", totalRecords);

                // 打印所有行的图片字段值，用于调试
                for (int i = 0; i < Math.min(totalRecords, 5); i++) {
                    Map<String, Object> rowData = data.get(i);
                    String originalUrl = (String) rowData.get(imageField);
                    String watermarkUrl = (String) rowData.get(watermarkImageField);

                    // 获取图片ID用于日志打印
                    String originalId = originalUrl != null && originalUrl.contains("id=") ?
                            originalUrl.substring(originalUrl.lastIndexOf("id=") + 3) : "无";
                    String watermarkId = watermarkUrl != null && watermarkUrl.contains("id=") ?
                            watermarkUrl.substring(watermarkUrl.lastIndexOf("id=") + 3) : "无";

                    log.info("数据[{}]图片ID - 原图: {}, 水印图: {}", i, originalId, watermarkId);
                }

                for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
                    // 计算当前批次的起止索引
                    int startIndex = batchIndex * batchSize;
                    int endIndex = Math.min(startIndex + batchSize, totalRecords);

                    // 计算实际需要处理的任务数
                    int taskCount = 0;
                    if (originalImageColumnIndex >= 0 && imageField != null) taskCount++;
                    if (watermarkImageColumnIndex >= 0 && watermarkImageField != null) taskCount++;

                    log.info("处理批次 {} - 索引范围: {}-{}, 任务数: {}", batchIndex, startIndex, endIndex - 1, taskCount);

                    CountDownLatch batchLatch = new CountDownLatch((endIndex - startIndex) * taskCount);

                    // 处理当前批次的图片
                    for (int i = startIndex; i < endIndex; i++) {
                        final int rowIndex = i;

                        log.info("准备处理数据索引: {}, 原图列索引: {}, 水印图列索引: {}",
                                rowIndex, originalImageColumnIndex, watermarkImageColumnIndex);

                        // 加载原图（如果存在）
                        if (originalImageColumnIndex >= 0 && imageField != null) {
                            executorService.execute(() -> {
                                try {
                                    Map<String, Object> rowData = data.get(rowIndex);
                                    String imagePath = (String) rowData.get(imageField);

                                    // 获取图片ID，用于日志
                                    String imageId = imagePath != null && imagePath.contains("id=") ?
                                            imagePath.substring(imagePath.lastIndexOf("id=") + 3) : "无";

                                    if (imagePath != null && !imagePath.isEmpty()) {
                                        byte[] imageBytes = loadImage(imagePath);
                                        if (imageBytes != null && imageBytes.length > 0) {
                                            // 计算Excel中的行号 (数据索引+2，因为有标题行和表头行)
                                            int excelRowNum = rowIndex + 2;

                                            // 使用正确的列索引存储
                                            imageCache.get(originalImageColumnIndex).put(rowIndex, imageBytes);
                                            imageCache.get(originalImageColumnIndex).put(excelRowNum, imageBytes);

                                            log.info("缓存原图 - 数据索引: {}, Excel行: {}, 列索引: {}, ID: {}",
                                                    rowIndex, excelRowNum, originalImageColumnIndex, imageId);
                                        } else {
                                            log.warn("图片加载失败 - 数据索引: {}, ID: {}", rowIndex, imageId);
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("预加载原图失败, 行 {}: {}", rowIndex, e.getMessage());
                                } finally {
                                    batchLatch.countDown();
                                }
                            });
                        } else {
                            // 如果没有原图列或字段名为空，直接减少计数
                            batchLatch.countDown();
                        }

                        // 加载水印图（如果存在）
                        if (watermarkImageColumnIndex >= 0 && watermarkImageField != null) {
                            log.info("提交水印图预加载任务 - 数据索引: {}", rowIndex);
                            executorService.execute(() -> {
                                try {
                                    Map<String, Object> rowData = data.get(rowIndex);
                                    String imagePath = (String) rowData.get(watermarkImageField);

                                    // 获取图片ID，用于日志
                                    String imageId = imagePath != null && imagePath.contains("id=") ?
                                            imagePath.substring(imagePath.lastIndexOf("id=") + 3) : "无";

                                    log.info("预加载水印图 - 数据索引: {}, 字段名: {}, URL: {}, ID: {}",
                                            rowIndex, watermarkImageField, imagePath, imageId);

                                    if (imagePath != null && !imagePath.isEmpty()) {
                                        byte[] imageBytes = loadImage(imagePath);
                                        if (imageBytes != null && imageBytes.length > 0) {
                                            // 计算Excel中的行号 (数据索引+2，因为有标题行和表头行)
                                            int excelRowNum = rowIndex + 2;

                                            // 使用正确的列索引存储
                                            imageCache.get(watermarkImageColumnIndex).put(rowIndex, imageBytes);
                                            imageCache.get(watermarkImageColumnIndex).put(excelRowNum, imageBytes);

                                            log.info("缓存水印图 - 数据索引: {}, Excel行: {}, 列索引: {}, ID: {}",
                                                    rowIndex, excelRowNum, watermarkImageColumnIndex, imageId);
                                        } else {
                                            log.warn("水印图加载失败 - 数据索引: {}, ID: {}", rowIndex, imageId);
                                        }
                                    }
                                } catch (Exception e) {
                                    log.error("预加载水印图失败, 行 {}: {}", rowIndex, e.getMessage());
                                } finally {
                                    batchLatch.countDown();
                                }
                            });
                        } else {
                            // 如果没有水印图列或字段名为空，直接减少计数
                            log.info("跳过水印图预加载 - 数据索引: {}, 列索引: {}, 字段名: {}",
                                    rowIndex, watermarkImageColumnIndex, watermarkImageField);
                            batchLatch.countDown();
                        }
                    }

                    // 等待当前批次完成
                    try {
                        // 设置超时，避免某些图片下载过慢影响整体进度
                        boolean completed = batchLatch.await(30, TimeUnit.SECONDS);
                        if (!completed) {
                            log.warn("批次 {}/{} 图片预加载超时", batchIndex + 1, totalBatches);
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.error("图片预加载被中断", e);
                    }
                }

                executorService.shutdown();
                // 设置总体超时，避免永久等待
                if (!executorService.awaitTermination(2, TimeUnit.MINUTES)) {
                    executorService.shutdownNow();
                    log.warn("图片预加载线程池被强制关闭");
                }
            } catch (Exception e) {
                log.error("预加载图片过程中出错", e);
            }

            // 预加载完成后，检查缓存状态
            log.info("预加载完成，检查缓存状态:");
            for (Map.Entry<Integer, Map<Integer, byte[]>> entry : imageCache.entrySet()) {
                int columnIndex = entry.getKey();
                Map<Integer, byte[]> cache = entry.getValue();
                if (cache != null && !cache.isEmpty()) {
                    log.info("列索引 {} 的缓存包含 {} 个图片，键: {}",
                            columnIndex, cache.size(), cache.keySet());

                    // 特别检查第一行数据的缓存
                    if (cache.containsKey(0)) {
                        log.info("第一行数据(索引0)的图片已缓存，大小: {}KB", cache.get(0).length / 1024);
                    } else {
                        log.warn("第一行数据(索引0)的图片未缓存，尝试同步加载");
                        // 尝试同步加载第一行数据
                        if (columnIndex == watermarkImageColumnIndex && watermarkImageField != null && !data.isEmpty()) {
                            Map<String, Object> firstRow = data.get(0);
                            String imagePath = (String) firstRow.get(watermarkImageField);
                            if (imagePath != null && !imagePath.isEmpty()) {
                                try {
                                    byte[] imageBytes = loadImage(imagePath);
                                    if (imageBytes != null && imageBytes.length > 0) {
                                        cache.put(0, imageBytes);
                                        cache.put(2, imageBytes); // Excel行号2
                                        log.info("同步加载第一行图片成功，大小: {}KB", imageBytes.length / 1024);
                                    }
                                } catch (Exception e) {
                                    log.error("同步加载第一行图片失败: {}", e.getMessage());
                                }
                            }
                        }
                    }
                    if (cache.containsKey(2)) {
                        log.info("第一行Excel(行号2)的图片已缓存，大小: {}KB", cache.get(2).length / 1024);
                    }
                } else {
                    log.warn("列索引 {} 的缓存为空", columnIndex);
                }
            }
        }

        /**
         * 加载图片（支持本地文件和远程URL）
         */
        private byte[] loadImage(String imagePath) {
            // 检查是否为URL
            if (imagePath.startsWith("http://") || imagePath.startsWith("https://")) {
                // 使用包装方法
                return downloadImage(imagePath);
            } else {
                // 本地文件
                try {
                    return Files.readAllBytes(Paths.get(imagePath));
                } catch (IOException e) {
                    log.error("读取本地图片失败: {}", e.getMessage());
                    return null;
                }
            }
        }

        /**
         * 内部类中的完整实现 - 下载图片方法
         */
        private byte[] downloadImage(String imageUrl) {
            // 防止无限递归，添加重试标记检查
            if (imageUrl == null || imageUrl.isEmpty()) {
                log.error("图片URL为空");
                return null;
            }

            // 增加日志，记录尝试下载的URL
            log.info("尝试下载图片: {}", imageUrl);

            HttpURLConnection connection = null;
            InputStream inputStream = null;
            ByteArrayOutputStream outputStream = null;

            try {
                URL url = new URL(imageUrl);
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(30000);  // 30秒连接超时
                connection.setReadTimeout(60000);     // 60秒读取超时

                // 添加请求头，模拟浏览器请求
                connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                connection.setRequestProperty("Accept", "image/webp,image/apng,image/*,*/*;q=0.8");
                connection.setRequestProperty("Accept-Encoding", "gzip, deflate");
                connection.setRequestProperty("Connection", "keep-alive");
                connection.setUseCaches(false);
                connection.setInstanceFollowRedirects(true);

                // 记录连接信息
                log.info("开始连接图片URL: {}", imageUrl);

                // 获取响应状态
                int responseCode = connection.getResponseCode();
                String responseMessage = connection.getResponseMessage();
                log.info("图片URL响应状态: {} {}", responseCode, responseMessage);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    // 获取图片大小
                    int contentLength = connection.getContentLength();
                    String contentType = connection.getContentType();
                    log.info("图片内容长度: {}字节, 内容类型: {}", contentLength, contentType);

                    inputStream = connection.getInputStream();
                    log.info("成功获取输入流");

                    // 预分配足够大的缓冲区
                    int bufferSize = contentLength > 0 ? contentLength : 1024 * 1024;  // 如果未知大小则分配1MB
                    outputStream = new ByteArrayOutputStream(bufferSize);

                    // 使用较大的缓冲区提高效率
                    byte[] buffer = new byte[16384];  // 16KB缓冲区
                    int bytesRead;
                    long totalRead = 0;

                    log.info("开始读取图片数据...");
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                        totalRead += bytesRead;

                        // 每读取1MB输出一次进度
                        if (totalRead % (1024 * 1024) == 0) {
                            log.info("已读取: {}MB", totalRead / (1024 * 1024));
                        }
                    }
                    log.info("读取完成，总读取: {}字节 ({}KB)", totalRead, totalRead / 1024);

                    byte[] imageBytes = outputStream.toByteArray();
                    log.info("下载的图片大小: {}KB, 是否有效: {}",
                            imageBytes.length / 1024,
                            imageBytes.length > 0 ? "是" : "否");

                    // 检查图片数据是否有效
                    if (imageBytes.length == 0) {
                        log.error("下载的图片数据为空");
                        return null;
                    }

                    // 验证图片数据是否有效
                    if (!isValidImageData(imageBytes)) {
                        log.error("下载的数据不是有效的图片格式");
                        return null;
                    }

                    return imageBytes;
                } else {
                    log.error("图片下载失败，HTTP状态码: {}, 消息: {}", responseCode, responseMessage);

                    // 如果是404或其他错误，尝试不同的URL参数
                    if (!imageUrl.contains("RETRY") && imageUrl.contains("isPic=")) {
                        String alternativeUrl;
                        if (imageUrl.contains("isPic=0")) {
                            alternativeUrl = imageUrl.replace("isPic=0", "isPic=1") + "&RETRY=1";
                        } else {
                            alternativeUrl = imageUrl.replace("isPic=1", "isPic=0") + "&RETRY=1";
                        }
                        log.info("尝试替代URL: {}", alternativeUrl);
                        return downloadImage(alternativeUrl);
                    }
                }
            } catch (IOException e) {
                log.error("图片下载IO异常: {}, URL: {}", e.getMessage(), imageUrl, e);
            } catch (Exception e) {
                log.error("图片下载过程中发生异常: {}, URL: {}", e.getMessage(), imageUrl, e);
            } finally {
                // 确保资源正确关闭
                if (outputStream != null) {
                    try { outputStream.close(); } catch (IOException e) { log.error("关闭输出流失败", e); }
                }
                if (inputStream != null) {
                    try { inputStream.close(); } catch (IOException e) { log.error("关闭输入流失败", e); }
                }
                if (connection != null) {
                    connection.disconnect();
                }
            }

            log.error("图片下载最终失败: {}", imageUrl);
            return null;
        }



        @Override
        public void afterRowDispose(WriteSheetHolder writeSheetHolder, WriteTableHolder writeTableHolder, Row row, Integer relativeRowIndex, Boolean isHead) {
            // 只处理数据行，跳过标题行和表头行
            int rowNum = row.getRowNum();

            // 标题行是0，表头行是1，所以我们只处理>=2的行
            if (rowNum < 2) {
                return;
            }

            // 计算在data中的索引 (减2是因为有标题行和表头行)
            int dataIndex = rowNum - 2;

            // 检查数据索引是否有效
            if (dataIndex < 0 || dataIndex >= data.size()) {
                log.info("跳过处理行 {}: 索引 {} 超出范围 0-{}", rowNum, dataIndex, data.size() - 1);
                return;
            }

            log.info("🚀 === 开始处理第{}行图片 ===", rowNum);
            log.info("📊 Excel行号: {}, 数据索引: {}", rowNum, dataIndex);

            // 直接从当前行数据中获取图片URL
            Map<String, Object> rowData = data.get(dataIndex);

            // 显示当前行的关键数据（调试用）
            log.info("📋 第{}行数据字段: {}", rowNum, rowData.keySet());

            try {
                // 处理原图（如果需要显示）
                if (originalImageColumnIndex >= 0 && imageField != null) {
                    String originalUrl = (String) rowData.get(imageField);
                    log.info("🔍 第{}行原图字段[{}]的值: {}", rowNum, imageField, originalUrl);

                    if (originalUrl != null && !originalUrl.isEmpty()) {
                        processImageForCell(writeSheetHolder, row, rowNum, originalImageColumnIndex,
                                          originalUrl, "原图");
                    } else {
                        log.info("⚠️ 第{}行没有原图URL", rowNum);
                    }
                } else {
                    log.info("⏭️ 跳过原图处理 - 列索引: {}, 字段名: {}", originalImageColumnIndex, imageField);
                }

                // 处理水印图（如果需要显示）
                if (watermarkImageColumnIndex >= 0 && watermarkImageField != null) {
                    String watermarkUrl = (String) rowData.get(watermarkImageField);
                    log.info("🔍 第{}行水印图字段[{}]的值: {}", rowNum, watermarkImageField, watermarkUrl);

                    if (watermarkUrl != null && !watermarkUrl.isEmpty()) {
                        processImageForCell(writeSheetHolder, row, rowNum, watermarkImageColumnIndex,
                                          watermarkUrl, "水印图");
                    } else {
                        log.info("⚠️ 第{}行没有水印图URL", rowNum);
                    }
                } else {
                    log.info("⏭️ 跳过水印图处理 - 列索引: {}, 字段名: {}", watermarkImageColumnIndex, watermarkImageField);
                }

                log.info("✅ === 第{}行图片处理完成 ===", rowNum);
            } catch (Exception e) {
                log.error("处理第{}行图片时出错: {}", rowNum, e.getMessage(), e);
            }
        }

        /**
         * 处理单个单元格的图片 - 完全基于当前行数据，不依赖缓存
         */
        private void processImageForCell(WriteSheetHolder writeSheetHolder, Row row, int rowNum,
                                       int columnIndex, String imageUrl, String imageType) {
            try {
                // 获取图片ID用于日志
                String imageId = imageUrl.contains("id=") ?
                        imageUrl.substring(imageUrl.lastIndexOf("id=") + 3) : "未知";

                log.info("🔄 开始处理第{}行{}(列{}) - ID: {}", rowNum, imageType, columnIndex, imageId);
                log.info("📥 图片URL: {}", imageUrl);

                // 直接下载图片，不使用任何缓存
                log.info("⬇️ 直接下载{}...", imageType);
                byte[] imageBytes = loadImage(imageUrl);

                // 添加图片到单元格
                if (imageBytes != null && imageBytes.length > 0) {
                    log.info("✅ {}下载成功 - 大小: {}KB", imageType, imageBytes.length / 1024);

                    // 详细检查行和列信息
                    log.info("🎯 准备插入图片 - Excel行号: {}, 列索引: {}, Row对象: {}",
                            rowNum, columnIndex, row != null ? "存在" : "null");
                    log.info("🎯 Row实际行号: {}, 期望行号: {}", row.getRowNum(), rowNum);

                    boolean success = addPictureToCell(writeSheetHolder, row, columnIndex, imageBytes);
                    if (success) {
                        log.info("🎉 成功添加{}到第{}行第{}列", imageType, rowNum, columnIndex);
                    } else {
                        log.error("💥 添加{}到第{}行第{}列失败", imageType, rowNum, columnIndex);
                    }
                } else {
                    log.error("❌ 第{}行{}下载失败或数据为空", rowNum, imageType);
                }

            } catch (Exception e) {
                log.error("💥 处理第{}行{}时发生异常: {}", rowNum, imageType, e.getMessage(), e);
            }
        }

        // 缓存相关方法已移除，采用完全即时处理模式

        /**
         * 添加图片到单元格 - 返回成功状态
         */
        private boolean addPictureToCell(WriteSheetHolder writeSheetHolder, Row row, int columnIndex, byte[] imageBytes) {
            // 记录行号便于调试 - 移到try外面避免作用域问题
            int rowNum = row.getRowNum();

            try {
                log.info("🖼️ 正在处理第{}行第{}列的图片, 图片大小: {}KB", rowNum, columnIndex, imageBytes.length / 1024);

                // 详细检查行和列的状态
                log.info("🔍 行状态检查 - 行号: {}, 行对象: {}", rowNum, row != null ? "存在" : "null");
                log.info("🔍 列状态检查 - 列索引: {}, 是否有效: {}", columnIndex, columnIndex >= 0);

                // 检查目标单元格 - 确保单元格为空白状态以便显示图片
                Cell targetCell = row.getCell(columnIndex);
                if (targetCell == null) {
                    targetCell = row.createCell(columnIndex);
                    log.info("🆕 创建了新的单元格 [{}, {}]", rowNum, columnIndex);
                } else {
                    log.info("📋 发现现有单元格 [{}, {}], 当前值: '{}'", rowNum, columnIndex, targetCell.toString());
                }

                // 清空单元格内容，确保图片能正确显示
                targetCell.setBlank();
                log.info("🧹 清空单元格 [{}, {}] 以便显示图片", rowNum, columnIndex);

                // 限制图片大小，超过100KB则进行压缩
                byte[] processedImageBytes = imageBytes;
                if (imageBytes.length > 100 * 1024) {
                    processedImageBytes = compressImage(imageBytes);
                }

                // 添加图片到工作簿
                Workbook workbook = writeSheetHolder.getSheet().getWorkbook();
                log.debug("工作簿类型: {}", workbook.getClass().getSimpleName());

                int pictureIndex = workbook.addPicture(processedImageBytes, Workbook.PICTURE_TYPE_JPEG);

                // 获取或创建绘图对象
                Sheet sheet = writeSheetHolder.getSheet();
                Drawing<?> drawing = sheet.getDrawingPatriarch();
                if (drawing == null) {
                    drawing = sheet.createDrawingPatriarch();
                }
                log.debug("绘图对象类型: {}", drawing.getClass().getSimpleName());

                // 创建锚点，使图片更紧凑地显示在单元格内
                ClientAnchor anchor = createCompatibleAnchor(workbook, drawing, columnIndex, rowNum, columnIndex + 1, rowNum + 1);
                log.debug("创建锚点成功: 列{}, 行{}, 锚点类型: {}", columnIndex, rowNum, anchor.getClass().getSimpleName());

                // 设置锚点类型 - 尝试不同的锚点类型确保图片可见
                try {
                    // 使用 DONT_MOVE_AND_RESIZE 确保图片位置固定
                    anchor.setAnchorType(ClientAnchor.AnchorType.DONT_MOVE_AND_RESIZE);
                    log.info("🔧 设置锚点类型为 DONT_MOVE_AND_RESIZE");
                } catch (Exception e) {
                    log.warn("设置锚点类型失败: {}", e.getMessage());
                    try {
                        anchor.setAnchorType(ClientAnchor.AnchorType.MOVE_AND_RESIZE);
                        log.info("🔧 回退到 MOVE_AND_RESIZE 锚点类型");
                    } catch (Exception e2) {
                        log.warn("设置备用锚点类型也失败: {}", e2.getMessage());
                    }
                }

                // 添加图片
                Picture picture = drawing.createPicture(anchor, pictureIndex);
                log.info("🎨 成功创建图片对象: {}", picture.getClass().getSimpleName());

                // 验证图片是否真的被添加
                log.info("🔍 图片验证 - 锚点位置: 列{}-{}, 行{}-{}",
                        anchor.getCol1(), anchor.getCol2(), anchor.getRow1(), anchor.getRow2());
                log.info("🔍 图片索引: {}, 图片对象: {}", pictureIndex, picture != null ? "存在" : "null");

                // 记录日志
                log.info("🎉 图片成功插入到第{}行第{}列 - 这是第{}行数据", rowNum, columnIndex, rowNum - 1);
                return true;
            } catch (Exception e) {
                log.error("💥 添加图片到单元格失败 - 行: {}, 列: {}, 错误: {}", rowNum, columnIndex, e.getMessage(), e);
                return false;
            }
        }


    }

    /**
     * 标题处理器 - 添加美化的标题行
     */
    private static class TitleHandler implements SheetWriteHandler {
        private final String title;
        private final int columnCount;

        public TitleHandler(String title, int columnCount) {
            this.title = title;
            this.columnCount = columnCount;
        }

        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Sheet sheet = writeSheetHolder.getSheet();
            Workbook workbook = writeWorkbookHolder.getWorkbook();

            // 设置打印和显示属性
            sheet.setDisplayGridlines(true); // 显示网格线
            sheet.setPrintGridlines(true); // 打印网格线

            // 获取原始的表头行
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) return;

            // 在表头行前插入标题行
            sheet.shiftRows(0, sheet.getLastRowNum(), 1); // 所有行下移一行

            // 创建标题行
            Row titleRow = sheet.createRow(0);

            // 首先为整行创建单元格
            for (int i = 0; i < columnCount; i++) {
                titleRow.createCell(i);
            }

            // 第一个单元格设置标题文本
            Cell titleCell = titleRow.getCell(0);
            if (titleCell == null) titleCell = titleRow.createCell(0);
            titleCell.setCellValue(title);

            // 创建美观的标题样式 - 突出的底色
            CellStyle titleStyle = workbook.createCellStyle();
            titleStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
            titleStyle.setFillForegroundColor(IndexedColors.ROYAL_BLUE.getIndex()); // 更鲜明的皇家蓝

            // 设置粗体，大号字体 - 白色字体突出显示
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 16); // 更大字体
            titleFont.setColor(IndexedColors.WHITE.getIndex()); // 白色字体与蓝色背景对比强烈
            titleStyle.setFont(titleFont);

            // 应用样式到所有单元格
            for (int i = 0; i < columnCount; i++) {
                Cell cell = titleRow.getCell(i);
                if (cell != null) {
                    cell.setCellStyle(titleStyle);
                }
            }

            // 居中对齐
            titleStyle.setAlignment(HorizontalAlignment.CENTER);
            titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

            // 设置边框
            titleStyle.setBorderBottom(BorderStyle.THIN);
            titleStyle.setBorderTop(BorderStyle.THIN);
            titleStyle.setBorderLeft(BorderStyle.THIN);
            titleStyle.setBorderRight(BorderStyle.THIN);

            titleCell.setCellStyle(titleStyle);

            // 合并标题单元格
            sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, columnCount - 1));

            // 设置标题行高
            titleRow.setHeight((short) 600);

            // 为表头行单独设置样式 (现在是第1行)
            Row newHeaderRow = sheet.getRow(1);
            if (newHeaderRow != null) {
                CellStyle headerStyle = workbook.createCellStyle();
                headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
                headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
                Font headerFont = workbook.createFont();
                headerFont.setBold(true);
                headerFont.setColor(IndexedColors.BLACK.getIndex());
                headerFont.setFontHeightInPoints((short) 11);
                headerStyle.setFont(headerFont);
                headerStyle.setBorderBottom(BorderStyle.THIN);
                headerStyle.setBorderTop(BorderStyle.THIN);
                headerStyle.setBorderLeft(BorderStyle.THIN);
                headerStyle.setBorderRight(BorderStyle.THIN);
                headerStyle.setAlignment(HorizontalAlignment.CENTER);

                // 应用样式到所有表头单元格
                for (int i = 0; i < newHeaderRow.getLastCellNum(); i++) {
                    Cell cell = newHeaderRow.getCell(i);
                    if (cell != null) {
                        cell.setCellStyle(headerStyle);
                    }
                }
            }

            // 冻结前两行（标题行和表头行）
            sheet.createFreezePane(0, 2);
        }
    }

    /**
     * 列宽处理器，确保图片列有足够的宽度，其他列根据内容自适应
     */
    private static class ColumnWidthHandler implements SheetWriteHandler {
        @Override
        public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
        }

        @Override
        public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {
            Workbook workbook = writeWorkbookHolder.getWorkbook();
            Sheet sheet = writeSheetHolder.getSheet();

            // 获取表头行，确定列数
            Row headerRow = sheet.getRow(0);
            if (headerRow == null) return;

            int columnCount = headerRow.getLastCellNum();

            // 设置图片列宽度 - 增加宽度以更好地显示图片
            sheet.setColumnWidth(0, 256 * 35); // 原图列 - 35个字符宽度
            sheet.setColumnWidth(1, 256 * 35); // 水印图列 - 35个字符宽度

            // 为数据列设置合理的宽度
            sheet.setColumnWidth(2, 256 * 20); // 照片编号/代码列 - 更宽

            // 为其他数据列设置合适宽度
            for (int i = 3; i < columnCount; i++) {
                // 根据不同列类型设置不同宽度
                if (i <= 6) { // 位置和时间等关键信息列
                    sheet.setColumnWidth(i, 256 * 18);
                } else { // 其他数据列
                    sheet.setColumnWidth(i, 256 * 12);
                }
            }

            // 设置统一行高
            for (int i = 0; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    if (i == 0) { // 标题行
                        row.setHeight((short)600);
                    } else if (i == 1) { // 表头行
                        row.setHeight((short)500);
                    } else { // 数据行 - 增加行高以更好地显示图片
                        row.setHeight((short)2400); // 增加到2400，约120像素高度
                    }
                }
            }

            // 设置整个工作表的默认行高 - 增加以更好地显示图片
            sheet.setDefaultRowHeight((short)2400);
        }
    }

    /**
     * 下载并压缩图片
     */
    private static byte[] downloadAndCompressImage(String imageUrl) {
        try {
            // 直接在此下载图片，不调用可能有问题的方法
            byte[] imageBytes = null;
            HttpURLConnection connection = null;
            InputStream inputStream = null;
            ByteArrayOutputStream outputStream = null;

            try {
                URL url = new URL(imageUrl);
                connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);

                if (connection.getResponseCode() == HttpURLConnection.HTTP_OK) {
                    int contentLength = connection.getContentLength();
                    inputStream = connection.getInputStream();

                    outputStream = new ByteArrayOutputStream(
                            contentLength > 0 ? contentLength : 1024 * 1024);

                    byte[] buffer = new byte[16384];
                    int bytesRead;
                    while ((bytesRead = inputStream.read(buffer)) != -1) {
                        outputStream.write(buffer, 0, bytesRead);
                    }

                    imageBytes = outputStream.toByteArray();
                }
            } finally {
                if (outputStream != null) outputStream.close();
                if (inputStream != null) inputStream.close();
                if (connection != null) connection.disconnect();
            }

            if (imageBytes != null && imageBytes.length > 0) {
                // 压缩图片
                return compressImage(imageBytes);
            }
        } catch (Exception e) {
            log.error("下载或压缩图片失败: {}, URL: {}", e.getMessage(), imageUrl);
        }

        return null;
    }

    /**
     * 轻度压缩JPEG图片 - 仅对超大图片进行轻度压缩，保持较高质量
     */
    private static byte[] compressImage(byte[] imageBytes) {
        try {
            // 图片大小检查 - 仅压缩超过1MB的图片
            if (imageBytes.length < 1024 * 1024) {
                return imageBytes; // 小于1MB的图片不压缩
            }

            // 将字节数组转换为BufferedImage
            ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
            BufferedImage originalImage = ImageIO.read(bis);
            bis.close();

            if (originalImage == null) {
                return imageBytes; // 如果无法读取，返回原始数据
            }

            // 计算压缩后的尺寸 - 保持纵横比，但压缩程度较轻
            int originalWidth = originalImage.getWidth();
            int originalHeight = originalImage.getHeight();
            int maxDimension = 800; // 使用较大尺寸，保持更多细节

            // 如果原图已经小于这个尺寸，就不进行大小调整
            if (originalWidth <= maxDimension && originalHeight <= maxDimension) {
                // 只进行质量压缩，不调整尺寸
                ByteArrayOutputStream bos = new ByteArrayOutputStream();
                ImageWriter writer = ImageIO.getImageWritersByFormatName("jpeg").next();
                ImageWriteParam param = writer.getDefaultWriteParam();
                param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
                param.setCompressionQuality(0.9f); // 90%的高质量

                ImageOutputStream ios = ImageIO.createImageOutputStream(bos);
                writer.setOutput(ios);
                writer.write(null, new IIOImage(originalImage, null, null), param);
                writer.dispose();
                ios.close();

                return bos.toByteArray();
            }

            // 计算调整后的尺寸
            int targetWidth, targetHeight;
            if (originalWidth > originalHeight) {
                targetWidth = maxDimension;
                targetHeight = (int) (originalHeight * ((double) maxDimension / originalWidth));
            } else {
                targetHeight = maxDimension;
                targetWidth = (int) (originalWidth * ((double) maxDimension / originalHeight));
            }

            // 创建缩略图
            BufferedImage resizedImage = new BufferedImage(targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
            Graphics2D g2d = resizedImage.createGraphics();
            g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC); // 使用更高质量的插值
            g2d.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
            g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
            g2d.drawImage(originalImage, 0, 0, targetWidth, targetHeight, null);
            g2d.dispose();

            // 写入到新的字节数组
            ByteArrayOutputStream bos = new ByteArrayOutputStream();

            // 使用JPEG格式输出，设置高压缩质量
            ImageWriter writer = ImageIO.getImageWritersByFormatName("jpeg").next();
            ImageWriteParam param = writer.getDefaultWriteParam();
            param.setCompressionMode(ImageWriteParam.MODE_EXPLICIT);
            param.setCompressionQuality(0.85f); // 85%的质量，保持较好画质

            ImageOutputStream ios = ImageIO.createImageOutputStream(bos);
            writer.setOutput(ios);
            writer.write(null, new IIOImage(resizedImage, null, null), param);
            writer.dispose();
            ios.close();

            byte[] result = bos.toByteArray();
            log.info("图片压缩: 原始大小={}KB, 压缩后={}KB, 压缩率={}%",
                    imageBytes.length/1024, result.length/1024,
                    Math.round((1 - (double)result.length/imageBytes.length) * 100));
            return result;
        } catch (Exception e) {
            log.error("压缩图片失败: {}", e.getMessage());
            return imageBytes; // 出错时返回原始图片
        }
    }

    /**
     * 测试图片URL是否可访问
     * @param imageUrl 图片URL
     * @return 是否可访问
     */
    public static boolean isImageUrlAccessible(String imageUrl) {
        if (imageUrl == null || imageUrl.isEmpty()) {
            return false;
        }

        HttpURLConnection connection = null;
        try {
            URL url = new URL(imageUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("HEAD");
            connection.setConnectTimeout(10000);  // 增加超时时间
            connection.setReadTimeout(10000);

            // 添加请求头
            connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            connection.setRequestProperty("Accept", "image/*,*/*;q=0.8");
            connection.setUseCaches(false);
            connection.setInstanceFollowRedirects(true);

            int responseCode = connection.getResponseCode();
            log.info("测试图片URL: {}, 响应: {}", imageUrl, responseCode);

            // 接受200和302等成功状态码
            return (responseCode == HttpURLConnection.HTTP_OK ||
                    responseCode == HttpURLConnection.HTTP_MOVED_TEMP ||
                    responseCode == HttpURLConnection.HTTP_MOVED_PERM ||
                    responseCode == HttpURLConnection.HTTP_SEE_OTHER);
        } catch (Exception e) {
            log.warn("测试图片URL失败: {}, URL: {}", e.getMessage(), imageUrl);
            // 如果HEAD请求失败，不代表图片不存在，可能服务器不支持HEAD请求
            // 返回true让后续的GET请求去验证
            return true;
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 创建适合当前工作簿格式的锚点
     * @param workbook 工作簿
     * @param drawing 绘图对象
     * @param col1 起始列
     * @param row1 起始行
     * @param col2 结束列
     * @param row2 结束行
     * @return 锚点对象
     */
    private static ClientAnchor createCompatibleAnchor(Workbook workbook, Drawing<?> drawing,
                                                      int col1, int row1, int col2, int row2) {
        // 为了避免类型转换问题，直接使用Drawing的通用方法创建锚点
        // 这个方法会根据实际的Drawing类型自动创建正确的锚点类型
        try {
            // 设置边距，让图片在单元格中有适当的留白
            int leftMargin = 50;    // 左边距
            int topMargin = 50;     // 上边距
            int rightMargin = -50;  // 右边距
            int bottomMargin = -50; // 下边距

            ClientAnchor anchor = drawing.createAnchor(leftMargin, topMargin, rightMargin, bottomMargin,
                                                     col1, row1, col2, row2);
            log.debug("使用通用方法创建锚点: 列{}-{}, 行{}-{}, 锚点类型: {}",
                    col1, col2, row1, row2, anchor.getClass().getSimpleName());
            return anchor;
        } catch (Exception e) {
            log.error("创建锚点失败: {}", e.getMessage(), e);
            // 如果通用方法也失败，尝试手动创建
            try {
                if (workbook instanceof XSSFWorkbook) {
                    return new XSSFClientAnchor(50, 50, -50, -50, col1, row1, col2, row2);
                } else {
                    return new HSSFClientAnchor(50, 50, -50, -50, (short)col1, row1, (short)col2, row2);
                }
            } catch (Exception e2) {
                log.error("手动创建锚点也失败: {}", e2.getMessage(), e2);
                throw new RuntimeException("无法创建图片锚点", e2);
            }
        }
    }

    /**
     * 验证图片数据是否有效
     * @param imageBytes 图片字节数据
     * @return 是否为有效图片
     */
    private static boolean isValidImageData(byte[] imageBytes) {
        if (imageBytes == null || imageBytes.length < 4) {
            return false;
        }

        // 检查文件头，判断是否为常见图片格式
        String header = String.format("%02X%02X%02X%02X",
            imageBytes[0] & 0xFF, imageBytes[1] & 0xFF,
            imageBytes[2] & 0xFF, imageBytes[3] & 0xFF);

        boolean isValid = header.startsWith("FFD8") || // JPEG
                         header.startsWith("8950") || // PNG
                         header.startsWith("4749") || // GIF
                         header.startsWith("424D") || // BMP
                         header.startsWith("5249"); // WEBP

        log.info("图片数据验证 - 大小: {}字节, 文件头: {}, 有效: {}",
                imageBytes.length, header, isValid);

        return isValid;
    }

    // FilterHandler 类已删除，因为从未被使用（enableFilter 始终为 false）
}