package com.hualu.watermask.modules.watermaskphoto.vo;

import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhoto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 照片日报统计VO，用于显示某一日期的照片统计信息和照片列表
 */
public class PhotoDailySummaryVO {
    
    /**
     * 统计日期
     */
    private Date statisticsDate;
    
    /**
     * 照片总数
     */
    private Integer totalPhotos;
    
    /**
     * 工时（最早照片时间到最晚照片时间差，以小时为单位，保留一位小数）
     */
    private BigDecimal workingHours;
    
    /**
     * 上班时间（最早照片时间）
     */
    private Date startTime;
    
    /**
     * 下班时间（最晚照片时间）
     */
    private Date endTime;
    
    /**
     * 拍摄人员姓名（兼容原有接口，仅返回第一个拍摄人员）
     */
    private String photographerName;
    
    /**
     * 拍摄人员角色（兼容原有接口，仅返回第一个拍摄人员的角色）
     */
    private String photographerRole;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 拍摄人员姓名
     */
    private String name;
    
    /**
     * 拍摄人员角色
     */
    private String role;
    
    /**
     * 拍摄照片数量
     */
    private Integer photoCount;
    
    /**
     * 照片列表
     */
    private List<WatermarkPhoto> photos;

    /**
     * 格式化的工时显示（如：2小时30分钟、1天3小时、3天前等）
     */
    private String formattedWorkingTime;

    /**
     * 格式化的距离显示（如：500米、1.25千米等）
     */
    private String formattedDistance;

    /**
     * 团队ID
     */
    private String teamId;

    /**
     * 团队名称
     */
    private String teamName;

    /**
     * 地点信息
     */
    private String location;

    /**
     * 时间分组标识（用于5分钟内分组）
     */
    private String timeGroupKey;

    /**
     * 与上一次拍摄的时间差（带单位）
     */
    private String formattedTimeDiff;

    public Date getStatisticsDate() {
        return statisticsDate;
    }

    public void setStatisticsDate(Date statisticsDate) {
        this.statisticsDate = statisticsDate;
    }

    public Integer getTotalPhotos() {
        return totalPhotos;
    }

    public void setTotalPhotos(Integer totalPhotos) {
        this.totalPhotos = totalPhotos;
    }

    public BigDecimal getWorkingHours() {
        return workingHours;
    }

    public void setWorkingHours(BigDecimal workingHours) {
        this.workingHours = workingHours;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
    public String getPhotographerName() {
        return photographerName;
    }

    public void setPhotographerName(String photographerName) {
        this.photographerName = photographerName;
    }

    public String getPhotographerRole() {
        return photographerRole;
    }

    public void setPhotographerRole(String photographerRole) {
        this.photographerRole = photographerRole;
    }
    
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getRole() {
        return role;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public Integer getPhotoCount() {
        return photoCount;
    }

    public void setPhotoCount(Integer photoCount) {
        this.photoCount = photoCount;
    }

    public List<WatermarkPhoto> getPhotos() {
        return photos;
    }

    public void setPhotos(List<WatermarkPhoto> photos) {
        this.photos = photos;
    }

    public String getFormattedWorkingTime() {
        return formattedWorkingTime;
    }

    public void setFormattedWorkingTime(String formattedWorkingTime) {
        this.formattedWorkingTime = formattedWorkingTime;
    }

    public String getFormattedDistance() {
        return formattedDistance;
    }

    public void setFormattedDistance(String formattedDistance) {
        this.formattedDistance = formattedDistance;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getTimeGroupKey() {
        return timeGroupKey;
    }

    public void setTimeGroupKey(String timeGroupKey) {
        this.timeGroupKey = timeGroupKey;
    }

    public String getFormattedTimeDiff() {
        return formattedTimeDiff;
    }

    public void setFormattedTimeDiff(String formattedTimeDiff) {
        this.formattedTimeDiff = formattedTimeDiff;
    }
    
    /**
     * 照片时间VO，用于显示照片和拍摄时间
     */
    public static class PhotoTimeVO {
        
        /**
         * 照片ID
         */
        private String photoId;
        
        /**
         * 照片编号
         */
        private String photoCode;
        
        /**
         * 拍摄时间
         */
        private Date captureTime;
        
        /**
         * 拍摄地点
         */
        private String location;
        
        /**
         * 拍摄人员
         */
        private String photographer;
        
        /**
         * 原图ID
         */
        private String originalPhotoId;
        
        /**
         * 水印图ID
         */
        private String watermarkPhotoId;

        public String getPhotoId() {
            return photoId;
        }

        public void setPhotoId(String photoId) {
            this.photoId = photoId;
        }

        public String getPhotoCode() {
            return photoCode;
        }

        public void setPhotoCode(String photoCode) {
            this.photoCode = photoCode;
        }

        public Date getCaptureTime() {
            return captureTime;
        }

        public void setCaptureTime(Date captureTime) {
            this.captureTime = captureTime;
        }

        public String getLocation() {
            return location;
        }

        public void setLocation(String location) {
            this.location = location;
        }

        public String getPhotographer() {
            return photographer;
        }

        public void setPhotographer(String photographer) {
            this.photographer = photographer;
        }

        public String getOriginalPhotoId() {
            return originalPhotoId;
        }

        public void setOriginalPhotoId(String originalPhotoId) {
            this.originalPhotoId = originalPhotoId;
        }

        public String getWatermarkPhotoId() {
            return watermarkPhotoId;
        }

        public void setWatermarkPhotoId(String watermarkPhotoId) {
            this.watermarkPhotoId = watermarkPhotoId;
        }
        
        /**
         * 从WatermarkPhoto创建PhotoTimeVO
         * 
         * @param photo WatermarkPhoto对象
         * @return PhotoTimeVO对象
         */
        public static PhotoTimeVO fromWatermarkPhoto(WatermarkPhoto photo) {
            PhotoTimeVO vo = new PhotoTimeVO();
            vo.setPhotoId(photo.getPhotoId());
            vo.setPhotoCode(photo.getPhotoCode());
            vo.setCaptureTime(photo.getCaptureTime());
            vo.setLocation(photo.getLocation());
            vo.setPhotographer(photo.getPhotographer());
            vo.setOriginalPhotoId(photo.getOriginalPhotoId());
            vo.setWatermarkPhotoId(photo.getWatermarkPhotoId());
            return vo;
        }
    }
} 