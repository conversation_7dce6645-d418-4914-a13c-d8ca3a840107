package com.hualu.watermask.modules.watermaskphoto.service;

import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhotoCustom;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.watermask.modules.common.vo.RestResult;
import java.util.List;

public interface WatermarkPhotoCustomService extends IService<WatermarkPhotoCustom>{

    /**
     * 更新照片自定义内容
     *
     * @param customId 自定义内容ID
     * @param content 内容
     * @param fontColor 字体颜色
     * @param backgroundColor 背景颜色
     * @return 更新结果
     */
    RestResult<WatermarkPhotoCustom> updateCustomContent(String customId, String content, String fontColor, String backgroundColor);
    
    /**
     * 批量更新照片自定义内容
     *
     * @param customContents 自定义内容列表
     * @return 更新结果
     */
    RestResult<Boolean> batchUpdateCustomContent(List<WatermarkPhotoCustom> customContents);
}
