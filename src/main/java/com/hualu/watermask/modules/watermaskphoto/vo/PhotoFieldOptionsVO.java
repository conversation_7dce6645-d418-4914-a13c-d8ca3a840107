package com.hualu.watermask.modules.watermaskphoto.vo;

import java.util.List;

/**
 * 照片字段选项VO，用于表示照片字段选项和模板自定义内容选项
 */
public class PhotoFieldOptionsVO {
    
    /**
     * 固定字段选项列表
     */
    private List<FieldOption> fixedOptions;
    
    /**
     * 模板自定义内容选项列表
     */
    private List<FieldOption> templateOptions;

    public List<FieldOption> getFixedOptions() {
        return fixedOptions;
    }

    public void setFixedOptions(List<FieldOption> fixedOptions) {
        this.fixedOptions = fixedOptions;
    }

    public List<FieldOption> getTemplateOptions() {
        return templateOptions;
    }

    public void setTemplateOptions(List<FieldOption> templateOptions) {
        this.templateOptions = templateOptions;
    }
    
    /**
     * 字段选项
     */
    public static class FieldOption {
        
        /**
         * 字段编码
         */
        private String code;
        
        /**
         * 字段名称
         */
        private String name;
        
        /**
         * 字段描述
         */
        private String description;
        
        /**
         * 字体颜色
         */
        private String fontColor;
        
        /**
         * 背景颜色
         */
        private String backgroundColor;
        
        public FieldOption() {
        }
        
        public FieldOption(String code, String name, String description) {
            this.code = code;
            this.name = name;
            this.description = description;
        }
        
        public FieldOption(String code, String name, String description, String fontColor, String backgroundColor) {
            this.code = code;
            this.name = name;
            this.description = description;
            this.fontColor = fontColor;
            this.backgroundColor = backgroundColor;
        }

        public String getCode() {
            return code;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }
        
        public String getFontColor() {
            return fontColor;
        }
        
        public void setFontColor(String fontColor) {
            this.fontColor = fontColor;
        }
        
        public String getBackgroundColor() {
            return backgroundColor;
        }
        
        public void setBackgroundColor(String backgroundColor) {
            this.backgroundColor = backgroundColor;
        }
    }
} 