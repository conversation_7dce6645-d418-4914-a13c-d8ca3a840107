package com.hualu.watermask.modules.watermaskphoto.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 文件压缩工具类
 */
public class FileZipUtils {

    private static final Logger log = LoggerFactory.getLogger(FileZipUtils.class);

    /**
     * 将指定文件夹压缩成zip文件
     *
     * @param sourceFolderPath 要压缩的文件夹路径
     * @param zipFilePath 生成的zip文件路径
     * @return 是否压缩成功
     */
    public static boolean zipFolder(String sourceFolderPath, String zipFilePath) {
        try {
            File sourceFolder = new File(sourceFolderPath);
            File zipFile = new File(zipFilePath);
            
            // 确保源文件夹存在
            if (!sourceFolder.exists() || !sourceFolder.isDirectory()) {
                log.error("源文件夹不存在或不是目录: {}", sourceFolderPath);
                return false;
            }
            
            // 确保目标ZIP文件的父目录存在
            File parentDir = zipFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }
            
            try (FileOutputStream fos = new FileOutputStream(zipFile);
                 ZipOutputStream zos = new ZipOutputStream(fos)) {
                
                // 使用Java 8 NIO遍历文件夹并添加到ZIP
                Path sourcePath = Paths.get(sourceFolderPath);
                Files.walkFileTree(sourcePath, new SimpleFileVisitor<Path>() {
                    @Override
                    public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                        // 获取相对路径作为ZIP中的路径
                        String relativePath = sourcePath.relativize(file).toString().replace('\\', '/');
                        ZipEntry zipEntry = new ZipEntry(relativePath);
                        zos.putNextEntry(zipEntry);
                        
                        // 写入文件内容
                        Files.copy(file, zos);
                        zos.closeEntry();
                        
                        return FileVisitResult.CONTINUE;
                    }
                    
                    @Override
                    public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                        if (!dir.equals(sourcePath)) {
                            // 为目录创建ZIP条目
                            String relativePath = sourcePath.relativize(dir).toString().replace('\\', '/') + "/";
                            ZipEntry zipEntry = new ZipEntry(relativePath);
                            zos.putNextEntry(zipEntry);
                            zos.closeEntry();
                        }
                        return FileVisitResult.CONTINUE;
                    }
                });
                
                return true;
            }
        } catch (Exception e) {
            log.error("压缩文件夹失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 递归删除文件夹及其内容
     *
     * @param folderPath 要删除的文件夹路径
     * @return 是否删除成功
     */
    public static boolean deleteFolder(String folderPath) {
        try {
            Path directory = Paths.get(folderPath);
            if (Files.exists(directory)) {
                Files.walkFileTree(directory, new SimpleFileVisitor<Path>() {
                    @Override
                    public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                        Files.delete(file);
                        return FileVisitResult.CONTINUE;
                    }
                    
                    @Override
                    public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                        Files.delete(dir);
                        return FileVisitResult.CONTINUE;
                    }
                });
                return true;
            }
            return false;
        } catch (Exception e) {
            log.error("删除文件夹失败: {}", e.getMessage(), e);
            return false;
        }
    }
    
    /**
     * 创建临时文件夹
     *
     * @param prefix 文件夹名称前缀
     * @return 临时文件夹路径
     */
    public static String createTempFolder(String prefix) {
        try {
            Path tempDirectory = Files.createTempDirectory(prefix);
            return tempDirectory.toString();
        } catch (IOException e) {
            log.error("创建临时文件夹失败: {}", e.getMessage(), e);
            return null;
        }
    }
    
    /**
     * 确保文件夹存在，如果不存在则创建
     *
     * @param folderPath 文件夹路径
     * @return 是否创建成功（已存在也返回true）
     */
    public static boolean ensureFolderExists(String folderPath) {
        try {
            Path path = Paths.get(folderPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
            }
            return true;
        } catch (Exception e) {
            log.error("创建文件夹失败: {}", e.getMessage(), e);
            return false;
        }
    }
} 