package com.hualu.watermask.modules.watermaskphoto.vo;

import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhoto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 照片分组VO，用于按照创建人员和拍照地点分组显示照片
 */
public class PhotoGroupVO {
    
    /**
     * 拍摄人员
     */
    private String photographer;
    
    /**
     * 拍摄地点
     */
    private String location;
    
    /**
     * 经度
     */
    private BigDecimal longitude;
    
    /**
     * 纬度
     */
    private BigDecimal latitude;
    
    /**
     * 照片列表
     */
    private List<WatermarkPhoto> photos;
    
    /**
     * 照片数量
     */
    private Integer photoCount;
    
    /**
     * 距离上次拍照地点的距离（单位：米）
     */
    private Double distanceFromLastLocation;
    
    /**
     * 距离上次拍照的时间差（单位：分钟）
     */
    private Long timeDiffFromLastCapture;
    
    /**
     * 最早拍摄时间
     */
    private Date earliestCaptureTime;

    /**
     * 拍摄人员角色（兼容原有接口，仅返回第一个拍摄人员的角色）
     */
    private String photographerRole;
    
    /**
     * 最晚拍摄时间
     */
    private Date latestCaptureTime;

    public String getPhotographer() {
        return photographer;
    }

    public void setPhotographer(String photographer) {
        this.photographer = photographer;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public BigDecimal getLongitude() {
        return longitude;
    }

    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    public BigDecimal getLatitude() {
        return latitude;
    }

    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    public List<WatermarkPhoto> getPhotos() {
        return photos;
    }

    public void setPhotos(List<WatermarkPhoto> photos) {
        this.photos = photos;
    }

    public Integer getPhotoCount() {
        return photoCount;
    }

    public void setPhotoCount(Integer photoCount) {
        this.photoCount = photoCount;
    }

    public Double getDistanceFromLastLocation() {
        return distanceFromLastLocation;
    }

    public void setDistanceFromLastLocation(Double distanceFromLastLocation) {
        this.distanceFromLastLocation = distanceFromLastLocation;
    }

    public Long getTimeDiffFromLastCapture() {
        return timeDiffFromLastCapture;
    }

    public void setTimeDiffFromLastCapture(Long timeDiffFromLastCapture) {
        this.timeDiffFromLastCapture = timeDiffFromLastCapture;
    }

    public Date getEarliestCaptureTime() {
        return earliestCaptureTime;
    }

    public void setEarliestCaptureTime(Date earliestCaptureTime) {
        this.earliestCaptureTime = earliestCaptureTime;
    }

    public Date getLatestCaptureTime() {
        return latestCaptureTime;
    }

    public void setLatestCaptureTime(Date latestCaptureTime) {
        this.latestCaptureTime = latestCaptureTime;
    }

    public String getPhotographerRole() {
        return photographerRole;
    }

    public void setPhotographerRole(String photographerRole) {
        this.photographerRole = photographerRole;
    }
}