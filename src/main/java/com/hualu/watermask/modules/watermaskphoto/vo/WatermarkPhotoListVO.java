package com.hualu.watermask.modules.watermaskphoto.vo;

import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhoto;

import java.util.HashMap;
import java.util.Map;

/**
 * 水印照片列表VO，用于展示照片基本信息和动态自定义字段
 */
public class WatermarkPhotoListVO extends WatermarkPhoto {
    
    /**
     * 动态自定义字段，key为字段标题，value为字段内容
     */
    private Map<String, String> customFields = new HashMap<>();

    public Map<String, String> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(Map<String, String> customFields) {
        this.customFields = customFields;
    }
    
    /**
     * 添加自定义字段
     * 
     * @param title 字段标题
     * @param content 字段内容
     */
    public void addCustomField(String title, String content) {
        this.customFields.put(title, content);
    }
} 