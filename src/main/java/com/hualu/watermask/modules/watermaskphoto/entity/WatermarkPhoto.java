package com.hualu.watermask.modules.watermaskphoto.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 水印照片主表
 */
@TableName(value = "WATERMARK_PHOTO")
public class WatermarkPhoto {
    /**
     * 照片唯一标识符
     */
    @TableId(value = "PHOTO_ID")
    private String photoId;

    /**
     * 照片编号/编码
     */
    @TableField(value = "PHOTO_CODE")
    private String photoCode;

    /**
     * 照片拍摄时间（精确到秒）
     */
    @TableField(value = "CAPTURE_TIME")
    private Date captureTime;

    /**
     * 天气状况描述
     */
    @TableField(value = "WEATHER")
    private String weather;

    /**
     * 拍摄地点描述
     */
    @TableField(value = "\"LOCATION\"")
    private String location;

    /**
     * 海拔高度（单位：米，保留2位小数）
     */
    @TableField(value = "ELEVATION")
    private BigDecimal elevation;

    /**
     * 方位角（0-360度，保留3位小数）
     */
    @TableField(value = "AZIMUTH")
    private BigDecimal azimuth;

    /**
     * 移动速度（单位：km/h，保留3位小数）
     */
    @TableField(value = "SPEED")
    private BigDecimal speed;

    /**
     * 经度值(精度:小数点后8位)
     */
    @TableField(value = "LONGITUDE")
    private BigDecimal longitude;

    /**
     * 纬度值(精度:小数点后8位)
     */
    @TableField(value = "LATITUDE")
    private BigDecimal latitude;

    /**
     * 拍摄人姓名
     */
    @TableField(value = "PHOTOGRAPHER")
    private String photographer;

    /**
     * 参考文字描述
     */
    @TableField(value = "REFERENCE_TEXT")
    private String referenceText;

    /**
     * 系统创建人ID
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 系统创建时间
     */
    @TableField(value = "CREATED_TIME")
    private Date createdTime;

    /**
     * 删除标记(0:未删除 1:已删除)
     */
    @TableField(value = "DEL_FLAG")
    private BigDecimal delFlag;

    /**
     * 原图ID
     */
    @TableField(value = "ORIGINAL_PHOTO_ID")
    private String originalPhotoId;

    /**
     * 水印图ID
     */
    @TableField(value = "WATERMARK_PHOTO_ID")
    private String watermarkPhotoId;

    /**
     * 模板ID
     */
    @TableField(value = "TEMPLATE_ID")
    private String templateId;

    /**
     * 图标
     */
    @TableField(value = "ICON")
    private String icon;

    @TableField(exist = false)
    private List<WatermarkPhotoCustom> watermarkPhotoCustoms;

    /**
     * 获取照片唯一标识符
     *
     * @return PHOTO_ID - 照片唯一标识符
     */
    public String getPhotoId() {
        return photoId;
    }

    /**
     * 设置照片唯一标识符
     *
     * @param photoId 照片唯一标识符
     */
    public void setPhotoId(String photoId) {
        this.photoId = photoId;
    }

    /**
     * 获取照片编号/编码
     *
     * @return PHOTO_CODE - 照片编号/编码
     */
    public String getPhotoCode() {
        return photoCode;
    }

    /**
     * 设置照片编号/编码
     *
     * @param photoCode 照片编号/编码
     */
    public void setPhotoCode(String photoCode) {
        this.photoCode = photoCode;
    }

    /**
     * 获取照片拍摄时间（精确到秒）
     *
     * @return CAPTURE_TIME - 照片拍摄时间（精确到秒）
     */
    public Date getCaptureTime() {
        return captureTime;
    }

    /**
     * 设置照片拍摄时间（精确到秒）
     *
     * @param captureTime 照片拍摄时间（精确到秒）
     */
    public void setCaptureTime(Date captureTime) {
        this.captureTime = captureTime;
    }

    /**
     * 获取天气状况描述
     *
     * @return WEATHER - 天气状况描述
     */
    public String getWeather() {
        return weather;
    }

    /**
     * 设置天气状况描述
     *
     * @param weather 天气状况描述
     */
    public void setWeather(String weather) {
        this.weather = weather;
    }

    /**
     * 获取拍摄地点描述
     *
     * @return LOCATION - 拍摄地点描述
     */
    public String getLocation() {
        return location;
    }

    /**
     * 设置拍摄地点描述
     *
     * @param location 拍摄地点描述
     */
    public void setLocation(String location) {
        this.location = location;
    }

    /**
     * 获取海拔高度（单位：米，保留2位小数）
     *
     * @return ELEVATION - 海拔高度（单位：米，保留2位小数）
     */
    public BigDecimal getElevation() {
        return elevation;
    }

    /**
     * 设置海拔高度（单位：米，保留2位小数）
     *
     * @param elevation 海拔高度（单位：米，保留2位小数）
     */
    public void setElevation(BigDecimal elevation) {
        this.elevation = elevation;
    }

    /**
     * 获取方位角（0-360度，保留3位小数）
     *
     * @return AZIMUTH - 方位角（0-360度，保留3位小数）
     */
    public BigDecimal getAzimuth() {
        return azimuth;
    }

    /**
     * 设置方位角（0-360度，保留3位小数）
     *
     * @param azimuth 方位角（0-360度，保留3位小数）
     */
    public void setAzimuth(BigDecimal azimuth) {
        this.azimuth = azimuth;
    }

    /**
     * 获取移动速度（单位：km/h，保留3位小数）
     *
     * @return SPEED - 移动速度（单位：km/h，保留3位小数）
     */
    public BigDecimal getSpeed() {
        return speed;
    }

    /**
     * 设置移动速度（单位：km/h，保留3位小数）
     *
     * @param speed 移动速度（单位：km/h，保留3位小数）
     */
    public void setSpeed(BigDecimal speed) {
        this.speed = speed;
    }

    /**
     * 获取经度值(精度:小数点后8位)
     *
     * @return LONGITUDE - 经度值(精度:小数点后8位)
     */
    public BigDecimal getLongitude() {
        return longitude;
    }

    /**
     * 设置经度值(精度:小数点后8位)
     *
     * @param longitude 经度值(精度:小数点后8位)
     */
    public void setLongitude(BigDecimal longitude) {
        this.longitude = longitude;
    }

    /**
     * 获取纬度值(精度:小数点后8位)
     *
     * @return LATITUDE - 纬度值(精度:小数点后8位)
     */
    public BigDecimal getLatitude() {
        return latitude;
    }

    /**
     * 设置纬度值(精度:小数点后8位)
     *
     * @param latitude 纬度值(精度:小数点后8位)
     */
    public void setLatitude(BigDecimal latitude) {
        this.latitude = latitude;
    }

    /**
     * 获取拍摄人姓名
     *
     * @return PHOTOGRAPHER - 拍摄人姓名
     */
    public String getPhotographer() {
        return photographer;
    }

    /**
     * 设置拍摄人姓名
     *
     * @param photographer 拍摄人姓名
     */
    public void setPhotographer(String photographer) {
        this.photographer = photographer;
    }

    /**
     * 获取参考文字描述
     *
     * @return REFERENCE_TEXT - 参考文字描述
     */
    public String getReferenceText() {
        return referenceText;
    }

    /**
     * 设置参考文字描述
     *
     * @param referenceText 参考文字描述
     */
    public void setReferenceText(String referenceText) {
        this.referenceText = referenceText;
    }

    /**
     * 获取系统创建人ID
     *
     * @return CREATED_BY - 系统创建人ID
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 设置系统创建人ID
     *
     * @param createdBy 系统创建人ID
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 获取系统创建时间
     *
     * @return CREATED_TIME - 系统创建时间
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * 设置系统创建时间
     *
     * @param createdTime 系统创建时间
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * 获取删除标记(0:未删除 1:已删除)
     *
     * @return DEL_FLAG - 删除标记(0:未删除 1:已删除)
     */
    public BigDecimal getDelFlag() {
        return delFlag;
    }

    /**
     * 设置删除标记(0:未删除 1:已删除)
     *
     * @param delFlag 删除标记(0:未删除 1:已删除)
     */
    public void setDelFlag(BigDecimal delFlag) {
        this.delFlag = delFlag;
    }

    public String getOriginalPhotoId() {
        return originalPhotoId;
    }

    public void setOriginalPhotoId(String originalPhotoId) {
        this.originalPhotoId = originalPhotoId;
    }

    public String getWatermarkPhotoId() {
        return watermarkPhotoId;
    }

    public void setWatermarkPhotoId(String watermarkPhotoId) {
        this.watermarkPhotoId = watermarkPhotoId;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public List<WatermarkPhotoCustom> getWatermarkPhotoCustoms() {
        return watermarkPhotoCustoms;
    }

    public void setWatermarkPhotoCustoms(List<WatermarkPhotoCustom> watermarkPhotoCustoms) {
        this.watermarkPhotoCustoms = watermarkPhotoCustoms;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}