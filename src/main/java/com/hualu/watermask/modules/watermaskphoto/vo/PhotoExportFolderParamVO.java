package com.hualu.watermask.modules.watermaskphoto.vo;

import java.util.Date;
import java.util.List;

/**
 * 照片文件夹导出参数VO
 */
public class PhotoExportFolderParamVO {
    
    /**
     * 照片ID列表（如果指定，则仅导出这些照片）
     */
    private List<String> photoIds;
    
    /**
     * 开始时间（查询条件）
     */
    private Date startTime;
    
    /**
     * 结束时间（查询条件）
     */
    private Date endTime;
    
    /**
     * 团队ID（查询条件）
     */
    private String teamId;
    
    /**
     * 拍摄人员（查询条件）
     */
    private String photographer;
    
    /**
     * 拍摄地点（查询条件）
     */
    private String location;
    
    /**
     * 模板ID
     */
    private String templateId;
    
    /**
     * 归类方式：
     * 1-按拍摄人员归类
     * 2-按拍摄日期归类
     * 3-按自定义属性归类
     * 0-不归类（直接存放在根目录）
     */
    private Integer groupType;
    
    /**
     * 自定义归类属性（当groupType=3时使用）
     */
    private String customGroupField;
    
    /**
     * 文件命名模式（使用属性字段名，以逗号分隔）
     * 例如：photographer,captureTime,templateName
     * 如果为空，则使用默认命名格式
     */
    private String fileNamePattern;
    
    /**
     * 是否导出水印图（true）或原图（false）
     */
    private Boolean exportWatermark = true;

    public List<String> getPhotoIds() {
        return photoIds;
    }

    public void setPhotoIds(List<String> photoIds) {
        this.photoIds = photoIds;
    }

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getPhotographer() {
        return photographer;
    }

    public void setPhotographer(String photographer) {
        this.photographer = photographer;
    }

    public String getLocation() {
        return location;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Integer getGroupType() {
        return groupType;
    }

    public void setGroupType(Integer groupType) {
        this.groupType = groupType;
    }

    public String getCustomGroupField() {
        return customGroupField;
    }

    public void setCustomGroupField(String customGroupField) {
        this.customGroupField = customGroupField;
    }

    public String getFileNamePattern() {
        return fileNamePattern;
    }

    public void setFileNamePattern(String fileNamePattern) {
        this.fileNamePattern = fileNamePattern;
    }

    public Boolean getExportWatermark() {
        return exportWatermark;
    }

    public void setExportWatermark(Boolean exportWatermark) {
        this.exportWatermark = exportWatermark;
    }
}