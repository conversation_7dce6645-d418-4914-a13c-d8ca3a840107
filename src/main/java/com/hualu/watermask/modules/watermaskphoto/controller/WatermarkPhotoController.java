package com.hualu.watermask.modules.watermaskphoto.controller;

import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhoto;
import com.hualu.watermask.modules.watermaskphoto.service.WatermarkPhotoService;
import com.hualu.watermask.modules.watermaskphoto.vo.PhotoDailySummaryVO;
import com.hualu.watermask.modules.watermaskphoto.vo.PhotoExportFolderParamVO;
import com.hualu.watermask.modules.watermaskphoto.vo.PhotoExportParamVO;
import com.hualu.watermask.modules.watermaskphoto.vo.PhotoFieldOptionsVO;
import com.hualu.watermask.modules.watermaskphoto.vo.PhotoGroupVO;
import com.hualu.watermask.modules.watermaskphoto.vo.WatermarkPhotoListVO;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import cn.dev33.satoken.stp.StpUtil;

@RestController
@RequestMapping("/api/watermark/photo")
@CrossOrigin
public class WatermarkPhotoController {

    private static final Logger log = LoggerFactory.getLogger(WatermarkPhotoController.class);

    @Autowired
    private WatermarkPhotoService watermarkPhotoService;

    /**
     * 创建水印照片
     *
     * @param originalPhoto 原始照片文件
     * @param watermarkPhoto 水印照片文件
     * @param photo 水印照片信息
     * @param teamIds 同步团队ID集合，逗号分隔，非必填。如果不填则按默认逻辑同步，如果填了则按指定团队同步
     * @return 创建结果
     */
    @PostMapping("/create")
    @OperationLog(businessType = "水印照片管理", operationContent = "创建水印照片", operationType = "INSERT")
    public RestResult<WatermarkPhoto> createWatermarkPhoto(
            @RequestPart("originalPhoto") MultipartFile originalPhoto,
            @RequestPart("watermarkPhoto") MultipartFile watermarkPhoto,
            @RequestPart("photo") WatermarkPhoto photo,
            @RequestParam(required = false) String teamIds) {

        // 获取当前用户ID
        String userId = StpUtil.getLoginIdAsString();

        // 调用服务创建照片并进行团队同步
        return watermarkPhotoService.createWatermarkPhotoWithSync(originalPhoto, watermarkPhoto, photo, userId, teamIds);
    }

    /**
     * 获取水印照片详情
     *
     * @param photoId 照片ID
     * @return 照片详情（包含基本信息和自定义内容）
     */
    @GetMapping("/detail")
    public RestResult<WatermarkPhoto> getPhotoDetail(@RequestParam String photoId) {
        return watermarkPhotoService.getPhotoDetail(photoId);
    }

    /**
     * 分页查询水印照片列表
     *
     * @param page 当前页码
     * @param pageSize 每页大小
     * @param keywords 关键字（多个关键字用逗号分隔，支持模糊搜索）
     * @param startDate 开始日期（格式：yyyy-MM-dd）
     * @param endDate 结束日期（格式：yyyy-MM-dd）
     * @param photographers 拍摄人员列表（多个用逗号分隔）
     * @param templateIds 模板ID列表（多个用逗号分隔）
     * @param location 拍摄地点
     * @param params 其他查询参数
     * @return 照片列表（包含基本信息和自定义内容）
     */
    @GetMapping("/list")
    public RestResult<List<WatermarkPhotoListVO>> getPhotoList(
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestParam(required = false) String keywords,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String photographers,
            @RequestParam(required = false) String templateIds,
            @RequestParam(required = false) String location,
            @RequestParam Map<String, Object> params) {

        // 如果提供了开始日期和结束日期，添加到查询参数中
        if (startDate != null) {
            params.put("startTime", startDate);
        }
        if (endDate != null) {
            // 将结束日期设置为当天的23:59:59
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            params.put("endTime", calendar.getTime());
        }

        // 处理多关键字搜索
        if (keywords != null && !keywords.trim().isEmpty()) {
            params.put("keywords", keywords.trim());
        }

        // 处理拍摄人员列表
        if (photographers != null && !photographers.trim().isEmpty()) {
            params.put("photographers", photographers.trim());
        }

        // 处理模板ID列表
        if (templateIds != null && !templateIds.trim().isEmpty()) {
            params.put("templateIds", templateIds.trim());
        }

        // 处理拍摄地点
        if (location != null && !location.trim().isEmpty()) {
            params.put("location", location.trim());
        }

        return watermarkPhotoService.getPhotoList(page, pageSize, params);
    }

    /**
     * 获取所有不重复的拍摄地点列表（用于下拉选项）
     *
     * @return 拍摄地点列表，格式为 { id: "", name: '469县道', desc: '广东省云浮市云城区安塘街道' }
     */
    @GetMapping("/locations")
    public RestResult<List<Map<String, String>>> getLocationOptions(@RequestParam(required = false) String teamId) {
        return watermarkPhotoService.getLocationOptions(teamId);
    }

    /**
     * 按照人员分组（带分页）
     *
     * @param page 当前页码
     * @param pageSize 每页大小
     * @param teamId 团队ID
     * @param captureDate 拍摄日期
     * @param userIds 成员搜索字段，逗号分隔（可选）
     * @param childrenTeamIds 子部门ID，逗号分隔（可选）
     * @return 分组后的照片列表
     */
    @GetMapping("/group/team")
    public RestResult<List<PhotoGroupVO>> getPhotoGroupsByTeam(
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestParam String teamId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date captureDate,
            @RequestParam(required = false) String userIds,
            @RequestParam(required = false) String childrenTeamIds) {
        return watermarkPhotoService.getPhotoGroupsByTeam(page, pageSize, teamId, captureDate, userIds, childrenTeamIds);
    }

    /**
     * 查询某一日期的照片日报统计
     *
     * @param teamId 团队ID
     * @param date 统计日期
     * @param page 当前页码
     * @param pageSize 每页大小
     * @param userIds 成员搜索字段，逗号分隔（可选）
     * @param childrenTeamIds 子团队ID，逗号分隔（可选）
     * @return 照片日报统计信息列表
     */
    @GetMapping("/daily/summary")
    public RestResult<List<PhotoDailySummaryVO>> getPhotoDailySummary(
            @RequestParam String teamId,
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd") Date date,
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestParam(required = false) String userIds,
            @RequestParam(required = false) String childrenTeamIds) {
        return watermarkPhotoService.getPhotoDailySummary(teamId, date, page, pageSize, userIds, childrenTeamIds);
    }

    /**
     * 获取照片字段选项和模板自定义内容选项
     *
     * @param templateId 模板ID
     * @return 字段选项列表
     */
    @GetMapping("/field/options")
    public RestResult<PhotoFieldOptionsVO> getPhotoFieldOptions(
            @RequestParam(required = false) String templateId) {
        return watermarkPhotoService.getPhotoFieldOptions(templateId);
    }

    /**
     * 导出照片台账（异步处理，先记录导出任务，再异步生成文件）
     *
     * @param params 导出参数
     * @param havPic 是否包含照片（true-有照片，false-无照片，默认true）。在非自定义导出属性时，如果选择无照片，则不导出原图和水印图；自定义属性下载时此参数无效
     * @return 导出任务ID
     */
    @PostMapping("/export")
    @OperationLog(businessType = "水印照片管理", operationContent = "导出照片台账", operationType = "SELECT")
    public RestResult<String> exportPhotoLedger(
            @RequestBody PhotoExportParamVO params,
            @RequestParam(value = "havPic", defaultValue = "true") Boolean havPic) {
        // 获取当前登录用户ID
        String userId = StpUtil.getLoginIdAsString();
        return watermarkPhotoService.asyncExportPhotoLedger(params, userId, havPic);
    }
    


    /**
     * 导出照片文件夹（按分类存放照片并打包成ZIP）
     *
     * @param params 导出参数
     * @param removeWatermark 是否去除水印（true-导出原图，false-导出水印图，默认false）
     * @return 导出任务ID
     */
    @PostMapping("/export/folder")
    @OperationLog(businessType = "水印照片管理", operationContent = "导出照片文件夹", operationType = "SELECT")
    public RestResult<String> exportPhotoFolder(
            @RequestBody PhotoExportFolderParamVO params,
            @RequestParam(value = "removeWatermark", defaultValue = "false") Boolean removeWatermark) {

        // 设置导出类型：true表示去除水印（导出原图），false表示保留水印（导出水印图）
        params.setExportWatermark(!removeWatermark);

        // 获取当前登录用户ID
        String userId = StpUtil.getLoginIdAsString();

        // 根据是否去除水印设置不同的文件类型标识
        String fileType = removeWatermark ? "全部照片-文件夹-原图" : "全部照片-文件夹-水印图";

        return watermarkPhotoService.asyncExportPhotoFolder(params, userId, fileType);
    }

    /**
     * 获取照片台账
     *
     * @param teamId 团队ID
     * @param keywords 水印关键词搜索（水印名称）
     * @param startDate 开始日期（拍照时间）
     * @param endDate 结束日期（拍照时间）
     * @param userId 筛选人员
     * @param templateId 筛选水印（模板ID）
     * @param current 当前页码
     * @param size 每页大小
     * @return 照片台账数据（包含表头和数据内容）
     */
    @GetMapping("/ledger")
    @OperationLog(businessType = "水印照片管理", operationContent = "获取照片台账", operationType = "SELECT")
    public RestResult<Map<String, Object>> getPhotoLedger(
            @RequestParam String teamId,
            @RequestParam(required = false) String keywords,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
            @RequestParam(required = false) String userId,
            @RequestParam(required = false) String templateId,
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size) {

        return watermarkPhotoService.getPhotoLedger(teamId, keywords, startDate, endDate, userId, templateId, current, size);
    }
} 