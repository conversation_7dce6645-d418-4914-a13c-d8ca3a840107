package com.hualu.watermask.modules.watermaskphoto.utils;

import com.alibaba.fastjson2.JSONObject;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

public class FileUtils {

    private static final Logger log = LoggerFactory.getLogger(FileUtils.class);

    /**
     * 上传图片文件到远程服务器
     * @param files 文件数组
     * @return 上传成功后的文件ID列表
     */
    public static List<String> uploadImage(List<MultipartFile> files) {
        if (files == null || files.isEmpty()) {
            return new ArrayList<>();
        }

        String url = "http://***********:7001/hwaymems/baseFileEntity/uploadImage";

        try {
            // 创建OkHttpClient
            OkHttpClient client = new OkHttpClient.Builder()
                    .connectTimeout(100, TimeUnit.SECONDS)
                    .readTimeout(100, TimeUnit.SECONDS)
                    .writeTimeout(100, TimeUnit.SECONDS)
                    .build();

            // 创建MultipartBody
            MultipartBody.Builder builder = new MultipartBody.Builder()
                    .setType(MultipartBody.FORM)
                    .addFormDataPart("userCode", "sjzx-zengxj");

            // 添加文件
            for (MultipartFile file : files) {
                builder.addFormDataPart(
                        "files",
                        file.getOriginalFilename(),
                        okhttp3.RequestBody.create(
                                okhttp3.MediaType.parse(file.getContentType()),
                                file.getBytes()
                        )
                );
            }

            // 构建请求
            Request request = new Request.Builder()
                    .url(url)
                    .post(builder.build())
                    .build();

            // 执行请求
            Response response = client.newCall(request).execute();

            if (response.isSuccessful() && response.body() != null) {
                String result = response.body().string();

                // 解析返回结果
                JSONObject jsonObject = JSONObject.parseObject(result);
                if (jsonObject != null && jsonObject.getInteger("code") == 1) {
                    return jsonObject.getJSONArray("data").toJavaList(String.class);
                } else {
                    log.error("上传图片失败: {}", result);
                }
            } else {
                log.error("上传图片请求失败: {}", response.code());
            }
        } catch (Exception e) {
            log.error("上传图片异常: {}", e.getMessage(), e);
        }

        return new ArrayList<>();
    }
}
