package com.hualu.watermask.modules.watermaskphoto.vo;

import java.util.Date;
import java.util.List;

/**
 * 照片导出参数VO
 */
public class PhotoExportParamVO {
    
    /**
     * 开始时间
     */
    private Date startTime;
    
    /**
     * 结束时间
     */
    private Date endTime;
    
    /**
     * 团队ID
     */
    private String teamId;

    /**
     * 拍摄人员
     */
    private String photographer;
    
    /**
     * 要导出的字段列表
     */
    private List<String> fields;
    
    /**
     * 模板ID
     */
    private String templateId;

    /**
     * 导出类型：
     * 0 - 支持筛选（在导出的Excel中默认开启筛选功能）
     * 1 - 支持缩放（保持现有功能不变）
     */
    private Integer type;

    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }
    
    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getPhotographer() {
        return photographer;
    }

    public void setPhotographer(String photographer) {
        this.photographer = photographer;
    }

    public List<String> getFields() {
        return fields;
    }

    public void setFields(List<String> fields) {
        this.fields = fields;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
}