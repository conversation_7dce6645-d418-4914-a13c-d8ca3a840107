package com.hualu.watermask.modules.team.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.team.entity.WsTeam;
import com.hualu.watermask.modules.team.service.WsTeamService;
import com.hualu.watermask.modules.team.vo.TeamTreeVO;
import com.hualu.watermask.modules.user.entity.WsUser;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import com.hualu.watermask.modules.user.service.WsUserService;
import com.hualu.watermask.modules.user.service.WsUserTeamsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;
import com.hualu.watermask.modules.team.vo.TeamTreeWithMembersVO;
import com.hualu.watermask.modules.team.vo.TeamLevelVO;
import com.hualu.watermask.modules.watermaskphoto.utils.FileUtils;
import cn.dev33.satoken.stp.StpUtil;
import com.hualu.watermask.modules.role.entity.WsRole;
import com.hualu.watermask.modules.role.service.WsRoleService;

import javax.servlet.http.HttpServletResponse;
import java.util.HashSet;
import java.util.Set;

/**
 * 团队管理控制器
 */
@RestController
@RequestMapping("/api/team")
@CrossOrigin
public class WsTeamController {

    @Autowired
    private WsTeamService wsTeamService;
    
    @Autowired
    private WsUserTeamsService wsUserTeamsService;
    
    @Autowired
    private WsUserService wsUserService;

    @Autowired
    private WsRoleService wsRoleService;

    /**
     * 创建团队
     *
     * @param teamName 团队名称
     * @param teamDescription 团队描述
     * @param parentTeamId 父团队ID（可选，为空则创建顶级团队）
     * @param icon 团队图标（可选）
     * @return 创建结果
     */
    @PostMapping("/create")
    @OperationLog(businessType = "团队管理", operationContent = "创建团队", operationType = "INSERT")
    public RestResult<WsTeam> create(
            @RequestParam String teamName,
            @RequestParam(required = false) String teamDescription,
            @RequestParam(required = false) String parentTeamId,
            @RequestPart(required = false) MultipartFile icon) {
            
        // 获取当前登录用户
        String loginUserId = StpUtil.getLoginIdAsString();
        
        // 调用服务层处理业务逻辑
        return wsTeamService.createTeamWithIcon(teamName, teamDescription, parentTeamId, loginUserId, icon);
    }

    /**
     * 更新团队名称
     *
     * @param teamId 团队ID
     * @param teamName 更新的团队名称
     * @param icon 团队图标（可选）
     * @return 更新结果
     */
    @PostMapping("/rename")
    @OperationLog(businessType = "团队管理", operationContent = "更新团队", operationType = "UPDATE")
    public RestResult<WsTeam> update(
            @RequestParam String teamId, 
            @RequestParam String teamName,
            @RequestPart(required = false) MultipartFile icon) {
        
        return wsTeamService.updateTeamNameAndIcon(teamId, teamName, icon);
    }

    /**
     * 删除团队（逻辑删除）
     *
     * @param teamId 团队ID
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @OperationLog(businessType = "团队管理", operationContent = "删除团队", operationType = "DELETE")
    public RestResult<Object> delete(@RequestParam String teamId) {
        return wsTeamService.deleteTeam(teamId);
    }

    /**
     * 获取团队详情
     *
     * @param teamId 团队ID
     * @return 团队详情
     */
    @GetMapping("/get")
    @OperationLog(businessType = "团队管理", operationContent = "获取团队详情", operationType = "SELECT")
    public RestResult<WsTeam> getById(@RequestParam String teamId) {
        WsTeam team = wsTeamService.getById(teamId);
        if (team != null && team.getDelFlag().compareTo(new BigDecimal(0)) == 0) {
            return RestResult.success(team);
        } else {
            return RestResult.error("未找到指定的团队或团队已被删除");
        }
    }

    /**
     * 分页查询团队列表
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @param teamName 团队名称（可选，模糊查询）
     * @param teamCode 团队编码（可选，精确查询）
     * @param parentTeamId 父团队ID（可选，精确查询）
     * @return 分页结果
     */
    @GetMapping("/list")
    @OperationLog(businessType = "团队管理", operationContent = "分页查询团队列表", operationType = "SELECT")
    public RestResult<List<WsTeam>> list(
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestParam(required = false) String teamName,
            @RequestParam(required = false) String teamCode,
            @RequestParam(required = false) String parentTeamId) {
        
        Page<WsTeam> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<WsTeam> queryWrapper = new LambdaQueryWrapper<>();
        
        // 只查询未删除的记录
        queryWrapper.eq(WsTeam::getDelFlag, new BigDecimal(0));
        
        // 根据条件过滤
        if (teamName != null && !teamName.isEmpty()) {
            queryWrapper.like(WsTeam::getTeamName, teamName);
        }
        
        if (teamCode != null && !teamCode.isEmpty()) {
            queryWrapper.eq(WsTeam::getTeamCode, teamCode);
        }
        
        if (parentTeamId != null && !parentTeamId.isEmpty()) {
            queryWrapper.eq(WsTeam::getParentTeamId, parentTeamId);
        }
        
        // 按团队名称排序
        queryWrapper.orderByAsc(WsTeam::getTeamName);
        
        IPage<WsTeam> result = wsTeamService.page(pageParam, queryWrapper);
        return RestResult.success(result.getRecords(), result.getTotal(), page, pageSize);
    }

    /**
     * 获取子团队列表
     *
     * @param parentId 父团队ID
     * @return 子团队列表
     */
    @GetMapping("/getChildren")
    @OperationLog(businessType = "团队管理", operationContent = "获取子团队列表", operationType = "SELECT")
    public RestResult<List<WsTeam>> getChildren(@RequestParam String parentId) {
        LambdaQueryWrapper<WsTeam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsTeam::getParentTeamId, parentId);
        queryWrapper.eq(WsTeam::getDelFlag, new BigDecimal(0));
        queryWrapper.orderByAsc(WsTeam::getTeamName);
        
        List<WsTeam> teams = wsTeamService.list(queryWrapper);
        return RestResult.success(teams);
    }

    /**
     * 获取顶级团队列表（没有父团队的团队）
     *
     * @return 顶级团队列表
     */
    @GetMapping("/top")
    @OperationLog(businessType = "团队管理", operationContent = "获取顶级团队列表", operationType = "SELECT")
    public RestResult<List<WsTeam>> getTopTeams() {
        LambdaQueryWrapper<WsTeam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.isNull(WsTeam::getParentTeamId).or().eq(WsTeam::getParentTeamId, "");
        queryWrapper.eq(WsTeam::getDelFlag, new BigDecimal(0));
        queryWrapper.orderByAsc(WsTeam::getTeamName);
        
        List<WsTeam> teams = wsTeamService.list(queryWrapper);
        return RestResult.success(teams);
    }

    /**
     * 通过团队编码查询团队
     *
     * @param teamCode 团队编码
     * @return 团队信息
     */
    @GetMapping("/getByCode")
    @OperationLog(businessType = "团队管理", operationContent = "通过团队编码查询团队", operationType = "SELECT")
    public RestResult<WsTeam> getByCode(@RequestParam String teamCode) {
        LambdaQueryWrapper<WsTeam> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsTeam::getTeamCode, teamCode);
        queryWrapper.eq(WsTeam::getDelFlag, new BigDecimal(0));
        
        WsTeam team = wsTeamService.getOne(queryWrapper);
        if (team != null) {
            return RestResult.success(team);
        } else {
            return RestResult.error("未找到指定编码的团队");
        }
    }
    
    /**
     * 获取团队的树形结构
     *
     * @param teamId 团队ID（可选，不提供则获取所有团队的树形结构）
     * @return 团队树形结构
     */
    @GetMapping("/tree")
    @OperationLog(businessType = "团队管理", operationContent = "获取团队树形结构", operationType = "SELECT")
    public RestResult<List<TeamTreeVO>> getTeamTree(@RequestParam(required = false) String teamId) {
        return wsTeamService.getTeamTreeWithParentFirst(teamId);
    }
    
    /**
     * 递归查找所有子团队ID
     *
     * @param parentId 父团队ID
     * @param teamIds 用于存储结果的列表
     */
    private void findAllChildTeamIds(String parentId, List<String> teamIds) {
        LambdaQueryWrapper<WsTeam> childQuery = new LambdaQueryWrapper<>();
        childQuery.eq(WsTeam::getParentTeamId, parentId);
        childQuery.eq(WsTeam::getDelFlag, new BigDecimal(0));
        
        List<WsTeam> children = wsTeamService.list(childQuery);
        for (WsTeam child : children) {
            String childId = child.getTeamId();
            teamIds.add(childId);
            // 递归查找子团队的子团队
            findAllChildTeamIds(childId, teamIds);
        }
    }

    /**
     * 邀请用户加入团队
     *
     * @param teamId 团队ID
     * @param userCode 用户编码
     * @return 邀请结果
     */
    @PostMapping("/invite")
    @OperationLog(businessType = "团队管理", operationContent = "邀请用户加入团队", operationType = "INSERT")
    public RestResult<Object> inviteUsers(
            @RequestParam String teamId,
            @RequestParam String userCode) {
        
        // 从Header获取saToken信息
        String loginUserId = StpUtil.getLoginIdAsString();
        
        // 检查团队是否存在
        WsTeam team = wsTeamService.getById(teamId);
        if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
            return RestResult.error("团队不存在或已被删除");
        }
        
        // 检查当前用户是否有权限邀请（简化处理：创建者或管理员有权限）
        if (!loginUserId.equals(team.getCreatedBy())) {
            // 可以在此添加更复杂的权限检查逻辑
            return RestResult.error("您没有邀请用户加入该团队的权限");
        }
        
        // 根据userCode查找用户
        LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
        userQuery.eq(WsUser::getUserCode, userCode);
        userQuery.eq(WsUser::getDelFlag, new BigDecimal(0));
        WsUser user = wsUserService.getOne(userQuery);
        
        if (user == null) {
            return RestResult.error("未找到用户编码为 " + userCode + " 的用户");
        }
        
        String userId = user.getUserId();
        
        // 检查用户是否已在团队中
        LambdaQueryWrapper<WsUserTeams> checkQuery = new LambdaQueryWrapper<>();
        checkQuery.eq(WsUserTeams::getUserId, userId);
        checkQuery.eq(WsUserTeams::getTeamId, teamId);
        WsUserTeams existingRelation = wsUserTeamsService.getOne(checkQuery);
        
        if (existingRelation != null) {
            if (existingRelation.getStatus().compareTo(new BigDecimal(1)) == 0) {
                return RestResult.error("该用户已经是团队成员");
            } else {
                return RestResult.error("该用户已收到邀请，等待确认中");
            }
        }
        
        // 创建邀请记录
        WsUserTeams userTeam = new WsUserTeams();
        userTeam.setId(UUID.randomUUID().toString().replace("-", ""));
        userTeam.setUserId(userId);
        userTeam.setTeamId(teamId);
        userTeam.setAssignedDate(new Date());
        userTeam.setAssignedBy(loginUserId); // 邀请人ID
        userTeam.setStatus(new BigDecimal(0)); // 待确认状态
        userTeam.setIsSyn(new BigDecimal(0)); // 默认不同步
        
        boolean result = wsUserTeamsService.save(userTeam);
        
        if (result) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("userId", userId);
            resultMap.put("username", user.getUsername());
            resultMap.put("userCode", userCode);
            resultMap.put("teamId", teamId);
            resultMap.put("teamName", team.getTeamName());
            return RestResult.success(resultMap, "已成功发送邀请");
        } else {
            return RestResult.error("邀请发送失败，请稍后重试");
        }
    }
    
    /**
     * 获取未加入指定团队且不是团队创建者的用户列表
     *
     * @param teamId 团队ID
     * @return 未加入团队且不是创建者的用户列表
     */
    @GetMapping("/unjoined-users")
    @OperationLog(businessType = "团队管理", operationContent = "获取未加入团队的用户列表", operationType = "SELECT")
    public RestResult<List<WsUser>> getUnjoinedUsers(@RequestParam String teamId) {
        // 检查团队是否存在
        WsTeam team = wsTeamService.getById(teamId);
        if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
            return RestResult.error("团队不存在或已被删除");
        }
        
        // 获取团队创建者ID
        String createdBy = team.getCreatedBy();
        
        // 获取已加入该团队的用户ID列表
        LambdaQueryWrapper<WsUserTeams> joinedQuery = new LambdaQueryWrapper<>();
        joinedQuery.eq(WsUserTeams::getTeamId, teamId);
        List<WsUserTeams> joinedUserTeams = wsUserTeamsService.list(joinedQuery);
        
        List<String> joinedUserIds = joinedUserTeams.stream()
                .map(WsUserTeams::getUserId)
                .collect(Collectors.toList());
        
        // 查询未加入该团队的用户（未删除且已启用的用户，且不是团队创建者）
        LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
        userQuery.eq(WsUser::getDelFlag, new BigDecimal(0));
        userQuery.eq(WsUser::getEnabled, new BigDecimal(1));
        
        // 排除团队创建者
        userQuery.ne(WsUser::getUserId, createdBy);
        
        if (!joinedUserIds.isEmpty()) {
            userQuery.notIn(WsUser::getUserId, joinedUserIds);
        }
        
        // 按用户名排序
        userQuery.orderByAsc(WsUser::getUsername);
        
        List<WsUser> unjoinedUsers = wsUserService.list(userQuery);
        return RestResult.success(unjoinedUsers);
    }
    
        /**
     * 批量删除团队成员
     *
     * @param teamId 团队ID
     * @param userIds 要删除的用户ID列表
     * @return 删除结果
     */
    @PostMapping("/remove-members")
    @SaCheckPermission("team:delete")
    @OperationLog(businessType = "团队管理", operationContent = "批量删除团队成员", operationType = "DELETE")
    public RestResult<Object> removeMembers(
            @RequestParam String teamId,
            @RequestBody List<String> userIds) {
            
        // 从Header获取saToken信息
        String loginUserId = StpUtil.getLoginIdAsString();
        
        // 调用服务层处理业务逻辑
        return wsTeamService.removeTeamMembers(teamId, userIds, loginUserId);
    }
    
    /**
     * 获取用户当前团队的树形结构，包含成员信息
     *
     * @param teamId 起始团队ID，如果为空则获取用户所属的所有团队
     * @param isNotJoinTeamId 筛选团队成员的团队ID（获取在teamId团队中但不在isNotJoinTeamId团队中的成员）
     * @param isNowTeam 是否只返回当前团队（不包含子团队）
     * @return 包含成员信息的团队树形结构
     */
    @GetMapping("/tree-with-members")
    @OperationLog(businessType = "团队管理", operationContent = "获取用户当前团队的树形结构", operationType = "SELECT")
    public RestResult<List<TeamTreeWithMembersVO>> getTeamTreeWithMembers(
            @RequestParam String teamId,
            @RequestParam(required = false) String isNotJoinTeamId,
            @RequestParam(required = false) Boolean isNowTeam) {
        
        // 获取当前登录用户
        String loginUserId = StpUtil.getLoginIdAsString();
        
        // 调用服务层处理业务逻辑
        return wsTeamService.getTeamTreeWithMembers(teamId, loginUserId, isNotJoinTeamId, isNowTeam);
    }
    
    /**
     * 获取部门/成员列表
     *
     * @param teamId 团队ID
     * @param keyword 搜索关键词（姓名或手机号）
     * @param current 当前页码
     * @param size 每页数量
     * @return 部门/成员列表
     */
    @GetMapping("/members")
    @OperationLog(businessType = "团队管理", operationContent = "获取部门/成员列表", operationType = "SELECT")
    public RestResult<Map<String, Object>> getTeamMembers(
            @RequestParam String teamId,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size) {
        
        return wsTeamService.getTeamMembersPagination(teamId, keyword, current, size);
    }
    
    /**
     * 获取团队层级信息（当前团队、成员和下级团队）
     *
     * @param teamId 团队ID（可选，不提供则获取用户所属的最高层级团队）
     * @return 团队层级信息
     */
    @GetMapping("/level-info")
    @OperationLog(businessType = "团队管理", operationContent = "获取团队层级信息", operationType = "SELECT")
    public RestResult<TeamLevelVO> getTeamLevelInfo(
            @RequestParam(required = false) String teamId) {
        
        // 获取当前登录用户
        String loginUserId = StpUtil.getLoginIdAsString();
        WsTeam currentTeam = null;
        
        // 如果没有提供团队ID，查找用户所属的最高层级团队
        if (teamId == null || teamId.isEmpty()) {
            // 查询用户所属的所有团队
            LambdaQueryWrapper<WsUserTeams> userTeamsQuery = new LambdaQueryWrapper<>();
            userTeamsQuery.eq(WsUserTeams::getUserId, loginUserId);
            userTeamsQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            List<WsUserTeams> userTeams = wsUserTeamsService.list(userTeamsQuery);
            
            if (userTeams.isEmpty()) {
                return RestResult.error("用户未加入任何团队");
            }
            
            // 获取这些团队的详细信息
            List<String> teamIds = userTeams.stream()
                    .map(WsUserTeams::getTeamId)
                    .collect(Collectors.toList());
            
            LambdaQueryWrapper<WsTeam> teamsQuery = new LambdaQueryWrapper<>();
            teamsQuery.in(WsTeam::getTeamId, teamIds);
            teamsQuery.eq(WsTeam::getDelFlag, new BigDecimal(0));
            List<WsTeam> teams = wsTeamService.list(teamsQuery);
            
            // 筛选出最高层级的团队（parentTeamId为空或不在用户所属的团队列表中）
            for (WsTeam team : teams) {
                String parentId = team.getParentTeamId();
                if (parentId == null || parentId.isEmpty() || !teamIds.contains(parentId)) {
                    // 找到一个最高层级团队
                    currentTeam = team;
                    break;
                }
            }
            
            // 如果没有找到最高层级团队，选择第一个团队
            if (currentTeam == null && !teams.isEmpty()) {
                currentTeam = teams.get(0);
            }
        } else {
            // 如果提供了团队ID，直接获取该团队
            currentTeam = wsTeamService.getById(teamId);
            
            // 验证团队是否存在且未被删除
            if (currentTeam == null || currentTeam.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                return RestResult.error("团队不存在或已被删除");
            }
            
            // 验证用户是否为该团队成员或创建人
            boolean isCreator = loginUserId.equals(currentTeam.getCreatedBy());
            
            // 如果不是创建人，检查是否为团队成员
            if (!isCreator) {
                LambdaQueryWrapper<WsUserTeams> checkMemberQuery = new LambdaQueryWrapper<>();
                checkMemberQuery.eq(WsUserTeams::getTeamId, teamId);
                checkMemberQuery.eq(WsUserTeams::getUserId, loginUserId);
                checkMemberQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
                
                boolean isMember = wsUserTeamsService.count(checkMemberQuery) > 0;
                if (!isMember) {
                    // 这里可以添加全局管理员检查逻辑
                    return RestResult.error("您不是该团队成员或创建人");
                }
            }
        }
        
        // 如果还是没有找到团队，返回错误
        if (currentTeam == null) {
            return RestResult.error("未找到团队信息");
        }
        
        // 构建结果对象
        TeamLevelVO result = new TeamLevelVO();

        // 为当前团队设置人数属性（包含当前团队及其所有下级团队的人数总和）
        int currentTeamTotalPersonCount = calculateTeamTotalPersonCount(currentTeam.getTeamId());
        currentTeam.setTotalPerson(currentTeamTotalPersonCount);

        result.setCurrentTeam(currentTeam);
        result.setParentTeamId(currentTeam.getParentTeamId());
        
        // 查询子团队列表
        LambdaQueryWrapper<WsTeam> childTeamsQuery = new LambdaQueryWrapper<>();
        childTeamsQuery.eq(WsTeam::getParentTeamId, currentTeam.getTeamId());
        childTeamsQuery.eq(WsTeam::getDelFlag, new BigDecimal(0));
        childTeamsQuery.orderByAsc(WsTeam::getTeamName);
        List<WsTeam> childTeams = wsTeamService.list(childTeamsQuery);

        // 为每个子团队设置人数属性（包含子团队及其下级团队的人数总和）
        for (WsTeam childTeam : childTeams) {
            int totalPersonCount = calculateTeamTotalPersonCount(childTeam.getTeamId());
            childTeam.setTotalPerson(totalPersonCount);
        }

        result.setChildTeams(childTeams);
        
        // 查询当前团队的成员
        LambdaQueryWrapper<WsUserTeams> teamMembersQuery = new LambdaQueryWrapper<>();
        teamMembersQuery.eq(WsUserTeams::getTeamId, currentTeam.getTeamId());
        teamMembersQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
        List<WsUserTeams> teamMembers = wsUserTeamsService.list(teamMembersQuery);
        
        // 获取成员用户信息
        if (!teamMembers.isEmpty()) {
            List<String> memberIds = teamMembers.stream()
                    .map(WsUserTeams::getUserId)
                    .collect(Collectors.toList());
            
            LambdaQueryWrapper<WsUser> membersQuery = new LambdaQueryWrapper<>();
            membersQuery.in(WsUser::getUserId, memberIds);
            membersQuery.eq(WsUser::getDelFlag, new BigDecimal(0));
            membersQuery.orderByAsc(WsUser::getUsername);
            
            List<WsUser> members = wsUserService.list(membersQuery);
            result.setMembers(members);
        }
        
        return RestResult.success(result);
    }

    /**
     * 通过手机号或用户编码邀请外部成员加入团队
     *
     * @param teamId 团队ID
     * @param phoneNumber 用户手机号（可选）
     * @param userCode 用户编码（可选）
     * @return 邀请结果
     */
    @PostMapping("/invite-by-phone")
    @OperationLog(businessType = "团队管理", operationContent = "通过手机号或用户编码邀请外部成员加入团队", operationType = "INSERT")
    public RestResult<Object> inviteUserByPhone(
            @RequestParam String teamId,
            @RequestParam(required = false) String phoneNumber,
            @RequestParam(required = false) String userCode) {
        
        // 参数验证
        if ((phoneNumber == null || phoneNumber.trim().isEmpty()) &&
            (userCode == null || userCode.trim().isEmpty())) {
            return RestResult.error("请提供手机号或用户编码");
        }

        // 从Header获取saToken信息
        String loginUserId = StpUtil.getLoginIdAsString();

        // 检查团队是否存在
        WsTeam team = wsTeamService.getById(teamId);
        if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
            return RestResult.error("团队不存在或已被删除");
        }

        // 检查当前用户是否有权限邀请（只有管理员和主管理员可以邀请）
        String inviterRole = wsTeamService.getUserRoleInTeam(loginUserId, teamId);
        if (!"admin".equals(inviterRole) && !"mainAdmin".equals(inviterRole)) {
            return RestResult.error("只有管理员或主管理员才能邀请用户加入团队");
        }
        
        // 根据手机号或用户编码查找用户
        WsUser user = null;
        if (phoneNumber != null && !phoneNumber.trim().isEmpty()) {
            // 优先使用手机号查找
            LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.eq(WsUser::getPhoneNumber, phoneNumber.trim());
            userQuery.eq(WsUser::getDelFlag, new BigDecimal(0));
            user = wsUserService.getOne(userQuery);
        } else if (userCode != null && !userCode.trim().isEmpty()) {
            // 使用用户编码查找
            LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.eq(WsUser::getUserCode, userCode.trim());
            userQuery.eq(WsUser::getDelFlag, new BigDecimal(0));
            user = wsUserService.getOne(userQuery);
        }

        if (user == null) {
            return RestResult.error("未找到对应的用户");
        }

        String userId = user.getUserId();

        // 检查被邀请用户是否在同一团队树中
        String inviteTeamTopLevelId = wsTeamService.getTopLevelTeamId(teamId);
        if (wsTeamService.isUserInTeamTree(userId, inviteTeamTopLevelId)) {
            return RestResult.error("该用户已在同一团队树中，无法邀请");
        }
        
        // 检查用户是否已在团队中
        LambdaQueryWrapper<WsUserTeams> checkQuery = new LambdaQueryWrapper<>();
        checkQuery.eq(WsUserTeams::getUserId, userId);
        checkQuery.eq(WsUserTeams::getTeamId, teamId);
        WsUserTeams existingRelation = wsUserTeamsService.getOne(checkQuery);
        
        if (existingRelation != null) {
            if (existingRelation.getStatus().compareTo(new BigDecimal(1)) == 0) {
                return RestResult.error("该用户已经是团队成员");
            } else {
                return RestResult.error("该用户已收到邀请，等待确认中");
            }
        }
        
        // 创建邀请记录
        WsUserTeams userTeam = new WsUserTeams();
        userTeam.setId(UUID.randomUUID().toString().replace("-", ""));
        userTeam.setUserId(userId);
        userTeam.setTeamId(teamId);
        userTeam.setAssignedDate(new Date());
        userTeam.setAssignedBy(loginUserId); // 邀请人ID
        userTeam.setStatus(new BigDecimal(0)); // 待确认状态
        userTeam.setIsSyn(new BigDecimal(0)); // 默认不同步
        
        boolean result = wsUserTeamsService.save(userTeam);
        
        if (result) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("userId", userId);
            resultMap.put("username", user.getUsername());
            resultMap.put("phoneNumber", user.getPhoneNumber());
            resultMap.put("userCode", user.getUserCode());
            resultMap.put("teamId", teamId);
            resultMap.put("teamName", team.getTeamName());
            return RestResult.success(resultMap, "已成功发送邀请");
        } else {
            return RestResult.error("邀请发送失败，请稍后重试");
        }
    }
    
    /**
     * 批量邀请成员加入团队
     *
     * @param teamId 团队ID
     * @param userIds 用户ID列表，多个ID用逗号分隔
     * @return 邀请结果
     */
    @PostMapping("/invite-batch")
    @OperationLog(businessType = "团队管理", operationContent = "批量邀请成员加入团队", operationType = "INSERT")
    public RestResult<Object> inviteBatchUsers(
            @RequestParam String teamId,
            @RequestParam String userIds) {
        
        // 从Header获取saToken信息
        String loginUserId = StpUtil.getLoginIdAsString();
        
        // 检查团队是否存在
        WsTeam team = wsTeamService.getById(teamId);
        if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
            return RestResult.error("团队不存在或已被删除");
        }
        
        // 检查当前用户是否有权限邀请（只有管理员和主管理员可以邀请）
        String inviterRole = wsTeamService.getUserRoleInTeam(loginUserId, teamId);
        if (!"admin".equals(inviterRole) && !"mainAdmin".equals(inviterRole)) {
            return RestResult.error("只有管理员或主管理员才能邀请用户加入团队");
        }
        
        // 解析用户ID列表
        String[] userIdArray = userIds.split(",");
        if (userIdArray.length == 0) {
            return RestResult.error("未提供有效的用户ID");
        }
        
        List<String> successUserIds = new ArrayList<>();
        List<String> failedUserIds = new ArrayList<>();
        
        for (String userId : userIdArray) {
            userId = userId.trim();
            if (userId.isEmpty()) {
                continue;
            }
            
            // 检查用户是否存在
            WsUser user = wsUserService.getById(userId);
            if (user == null || user.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                failedUserIds.add(userId);
                continue;
            }
            
            // 检查用户是否已在团队中
            LambdaQueryWrapper<WsUserTeams> checkQuery = new LambdaQueryWrapper<>();
            checkQuery.eq(WsUserTeams::getUserId, userId);
            checkQuery.eq(WsUserTeams::getTeamId, teamId);
            WsUserTeams existingRelation = wsUserTeamsService.getOne(checkQuery);

            if (existingRelation != null) {
                // 用户已在团队中或已被邀请
                failedUserIds.add(userId);
                continue;
            }

            // 检查被邀请用户是否在同一团队树中（树内成员邀请）
            String inviteTeamTopLevelId = wsTeamService.getTopLevelTeamId(teamId);
            if (!wsTeamService.isUserInTeamTree(userId, inviteTeamTopLevelId)) {
                // 不在同一团队树中，无法邀请
                failedUserIds.add(userId);
                continue;
            }

            // 检查是否为同一团队（同一团队不能邀请）
            String userCurrentTeamId = wsTeamService.getUserCurrentTeamInTree(userId, inviteTeamTopLevelId);
            if (teamId.equals(userCurrentTeamId)) {
                // 同一团队，不能邀请
                failedUserIds.add(userId);
                continue;
            }

            // 检查层级关系（下级不能邀请上级）
            if (wsTeamService.isUserInUpperLevel(userId, teamId)) {
                // 被邀请用户在上级团队，不能邀请
                failedUserIds.add(userId);
                continue;
            }
            
            // 创建邀请记录
            WsUserTeams userTeam = new WsUserTeams();
            userTeam.setId(UUID.randomUUID().toString().replace("-", ""));
            userTeam.setUserId(userId);
            userTeam.setTeamId(teamId);
            userTeam.setAssignedDate(new Date());
            userTeam.setAssignedBy(loginUserId);
            userTeam.setStatus(new BigDecimal(0)); // 待确认状态
            userTeam.setIsSyn(new BigDecimal(0)); // 默认不同步
            
            boolean result = wsUserTeamsService.save(userTeam);
            if (result) {
                successUserIds.add(userId);
            } else {
                failedUserIds.add(userId);
            }
        }
        
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("teamId", teamId);
        resultMap.put("teamName", team.getTeamName());
        resultMap.put("successCount", successUserIds.size());
        resultMap.put("failedCount", failedUserIds.size());
        resultMap.put("successUserIds", successUserIds);
        resultMap.put("failedUserIds", failedUserIds);
        
        return RestResult.success(resultMap, "已成功发送邀请");
    }
    
    /**
     * 导出团队成员列表
     *
     * @param teamId 团队ID
     * @param keyword 搜索关键词（姓名或手机号）
     * @return 导出任务ID
     */
    @PostMapping("/export-members")
    @OperationLog(businessType = "团队管理", operationContent = "导出团队成员列表", operationType = "SELECT")
    public RestResult<String> exportTeamMembers(
            @RequestParam String teamId,
            @RequestParam(required = false) String keyword) {

        // 获取当前登录用户ID
        String userId = StpUtil.getLoginIdAsString();
        return wsTeamService.asyncExportTeamMembers(teamId, keyword, userId);
    }



    /**
     * 团队成员管理（搜索成员）
     *
     * @param teamId 团队ID
     * @param keyword 搜索关键词（姓名或手机号）
     * @param current 当前页码
     * @param size 每页数量
     * @return 团队成员列表
     */
    @GetMapping("/manage-members")
    @OperationLog(businessType = "团队管理", operationContent = "团队成员管理", operationType = "SELECT")
    public RestResult<List<Map<String, Object>>> manageTeamMembers(
            @RequestParam String teamId,
            @RequestParam(required = false) String keyword,
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size) {
        
        // 检查团队是否存在
        WsTeam team = wsTeamService.getById(teamId);
        if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
            return RestResult.error("团队不存在或已被删除");
        }
        
        // 查询当前团队的成员
        LambdaQueryWrapper<WsUserTeams> teamMembersQuery = new LambdaQueryWrapper<>();
        teamMembersQuery.eq(WsUserTeams::getTeamId, teamId);
        teamMembersQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
        List<WsUserTeams> teamMembers = wsUserTeamsService.list(teamMembersQuery);
        
        if (teamMembers.isEmpty()) {
            // 没有成员，返回空结果
            return RestResult.success(new ArrayList<>(), 0, current, size);
        }
        
        // 获取成员ID列表
        List<String> memberIds = teamMembers.stream()
                .map(WsUserTeams::getUserId)
                .collect(Collectors.toList());
        
        // 查询成员信息
        LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
        userQuery.in(WsUser::getUserId, memberIds);
        userQuery.eq(WsUser::getDelFlag, new BigDecimal(0));
        
        // 如果提供了关键字，进行模糊搜索
        if (keyword != null && !keyword.isEmpty()) {
            userQuery.and(wrapper -> wrapper
                    .like(WsUser::getUsername, keyword)
                    .or()
                    .like(WsUser::getPhoneNumber, keyword));
        }
        
        // 计算总数
        long total = wsUserService.count(userQuery);
        
        // 分页查询
        Page<WsUser> page = new Page<>(current, size);
        userQuery.orderByAsc(WsUser::getUsername);
        Page<WsUser> userPage = wsUserService.page(page, userQuery);
        List<WsUser> users = userPage.getRecords();
        
        // 获取用户ID到团队角色ID的映射
        Map<String, String> userRoleMap = new HashMap<>();
        for (WsUserTeams ut : teamMembers) {
            if (ut.getRoleId() != null) {
                userRoleMap.put(ut.getUserId(), ut.getRoleId());
            }
        }
        
        // 获取所有需要的角色信息
        Set<String> roleIds = new HashSet<>(userRoleMap.values());
        Map<String, String> roleNameMap = new HashMap<>();
        
        if (!roleIds.isEmpty()) {
            LambdaQueryWrapper<WsRole> roleQuery = new LambdaQueryWrapper<>();
            roleQuery.in(WsRole::getRoleId, roleIds);
            roleQuery.eq(WsRole::getDelFlag, new BigDecimal(0));
            List<WsRole> roles = wsRoleService.list(roleQuery);
            
            // 构建角色ID到角色名称的映射
            for (WsRole role : roles) {
                roleNameMap.put(role.getRoleId(), role.getRoleName());
            }
        }
        
        // 构建结果数据
        List<Map<String, Object>> data = new ArrayList<>();
        for (WsUser user : users) {
            Map<String, Object> item = new HashMap<>();
            item.put("userId", user.getUserId());
            item.put("username", user.getUsername());
            item.put("phoneNumber", user.getPhoneNumber());
            item.put("dept", team.getTeamName());
            
            // 获取角色名称
            String roleId = userRoleMap.get(user.getUserId());
            String roleName = roleNameMap.getOrDefault(roleId, "普通成员");
            item.put("type", roleName);
            
            // 格式化上次登录时间
            Date loginDate = user.getLoginDate();
            String lastActive = loginDate != null ? new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(loginDate) : "";
            item.put("lastActive", lastActive);
            
            data.add(item);
        }
        
        return RestResult.success(data, total, current, size);
    }

    /**
     * 修改用户姓名
     *
     * @param userId 用户ID
     * @param newUsername 新用户姓名
     * @return 修改结果
     */
    @PostMapping("/update-username")
    @OperationLog(businessType = "团队管理", operationContent = "修改用户姓名", operationType = "UPDATE")
    public RestResult<Object> updateUsername(
            @RequestParam String userId,
            @RequestParam String newUsername) {
        
        // 获取当前登录用户
        String loginUserId = StpUtil.getLoginIdAsString();
        
        // 调用Service层处理业务逻辑
        return wsTeamService.updateUsername(userId, newUsername, loginUserId);
    }
    
    /**
     * 撤销用户的管理员角色（改为普通成员）
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @return 撤销结果
     */
    @PostMapping("/revoke-admin")
    @OperationLog(businessType = "团队管理", operationContent = "撤销管理员角色", operationType = "UPDATE")
    public RestResult<Object> revokeAdminRole(
            @RequestParam String teamId,
            @RequestParam String userId) {
        
        // 获取当前登录用户
        String loginUserId = StpUtil.getLoginIdAsString();
        
        // 调用Service层处理业务逻辑
        return wsTeamService.revokeAdminRole(teamId, userId, loginUserId);
    }
    
    /**
     * 退出团队（非主管理员可以退出团队）
     *
     * @param teamId 团队ID
     * @return 退出结果
     */
    @PostMapping("/leave")
    @OperationLog(businessType = "团队管理", operationContent = "退出团队", operationType = "DELETE")
    public RestResult<Object> leaveTeam(@RequestParam String teamId) {
        // 获取当前登录用户
        String userId = StpUtil.getLoginIdAsString();

        // 调用Service层处理业务逻辑
        return wsTeamService.leaveTeam(teamId, userId);
    }

    /**
     * 同意团队邀请
     *
     * @param inviteId 邀请记录ID
     * @return 处理结果
     */
    @PostMapping("/accept-invite")
    @OperationLog(businessType = "团队管理", operationContent = "同意团队邀请", operationType = "UPDATE")
    public RestResult<Object> acceptInvite(@RequestParam String inviteId) {
        String userId = StpUtil.getLoginIdAsString();
        return wsTeamService.acceptInvite(inviteId, userId);
    }

    /**
     * 拒绝团队邀请
     *
     * @param inviteId 邀请记录ID
     * @return 处理结果
     */
    @PostMapping("/reject-invite")
    @OperationLog(businessType = "团队管理", operationContent = "拒绝团队邀请", operationType = "DELETE")
    public RestResult<Object> rejectInvite(@RequestParam String inviteId) {
        String userId = StpUtil.getLoginIdAsString();
        return wsTeamService.rejectInvite(inviteId, userId);
    }

    /**
     * 计算团队总人数（包含当前团队及其所有下级团队的人数）
     *
     * @param teamId 团队ID
     * @return 总人数
     */
    private int calculateTeamTotalPersonCount(String teamId) {
        try {
            // 获取当前团队及其所有子团队的ID列表
            List<String> allTeamIds = wsTeamService.getAllTeamIdsInTree(teamId);

            // 统计所有这些团队的人数
            int totalCount = 0;
            for (String tId : allTeamIds) {
                // 查询每个团队的成员数量
                LambdaQueryWrapper<WsUserTeams> memberQuery = new LambdaQueryWrapper<>();
                memberQuery.eq(WsUserTeams::getTeamId, tId);
                memberQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态

                long memberCount = wsUserTeamsService.count(memberQuery);
                totalCount += (int) memberCount;
            }

            return totalCount;

        } catch (Exception e) {
            return 0;
        }
    }
}