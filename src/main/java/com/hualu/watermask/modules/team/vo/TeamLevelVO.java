package com.hualu.watermask.modules.team.vo;

import com.hualu.watermask.modules.team.entity.WsTeam;
import com.hualu.watermask.modules.user.entity.WsUser;
import java.util.ArrayList;
import java.util.List;

/**
 * 团队层级视图VO，包含当前团队信息、成员列表和下一级团队列表
 */
public class TeamLevelVO {
    
    /**
     * 当前团队信息
     */
    private WsTeam currentTeam;
    
    /**
     * 当前团队的成员列表
     */
    private List<WsUser> members = new ArrayList<>();
    
    /**
     * 下一级子团队列表
     */
    private List<WsTeam> childTeams = new ArrayList<>();
    
    /**
     * 当前团队的父团队ID（便于导航）
     */
    private String parentTeamId;
    
    /**
     * 获取当前团队信息
     * 
     * @return 当前团队
     */
    public WsTeam getCurrentTeam() {
        return currentTeam;
    }
    
    /**
     * 设置当前团队信息
     * 
     * @param currentTeam 当前团队
     */
    public void setCurrentTeam(WsTeam currentTeam) {
        this.currentTeam = currentTeam;
    }
    
    /**
     * 获取团队成员列表
     * 
     * @return 成员列表
     */
    public List<WsUser> getMembers() {
        return members;
    }
    
    /**
     * 设置团队成员列表
     * 
     * @param members 成员列表
     */
    public void setMembers(List<WsUser> members) {
        this.members = members;
    }
    
    /**
     * 获取下一级子团队列表
     * 
     * @return 子团队列表
     */
    public List<WsTeam> getChildTeams() {
        return childTeams;
    }
    
    /**
     * 设置下一级子团队列表
     * 
     * @param childTeams 子团队列表
     */
    public void setChildTeams(List<WsTeam> childTeams) {
        this.childTeams = childTeams;
    }
    
    /**
     * 获取父团队ID
     * 
     * @return 父团队ID
     */
    public String getParentTeamId() {
        return parentTeamId;
    }
    
    /**
     * 设置父团队ID
     * 
     * @param parentTeamId 父团队ID
     */
    public void setParentTeamId(String parentTeamId) {
        this.parentTeamId = parentTeamId;
    }
} 