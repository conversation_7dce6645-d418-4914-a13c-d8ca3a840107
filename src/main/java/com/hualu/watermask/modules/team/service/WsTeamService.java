package com.hualu.watermask.modules.team.service;

import com.hualu.watermask.modules.team.entity.WsTeam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.team.vo.TeamLevelVO;
import com.hualu.watermask.modules.team.vo.TeamTreeWithMembersVO;
import com.hualu.watermask.modules.team.vo.TeamTreeVO;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

public interface WsTeamService extends IService<WsTeam>{

    /**
     * 创建团队
     *
     * @param teamName 团队名称
     * @param teamDescription 团队描述
     * @param parentTeamId 父团队ID（可选，为空则创建顶级团队）
     * @param creatorId 创建者ID
     * @return 创建结果
     */
    RestResult<WsTeam> createTeam(String teamName, String teamDescription, String parentTeamId, String creatorId);
    
    /**
     * 创建团队（带图标）
     *
     * @param teamName 团队名称
     * @param teamDescription 团队描述
     * @param parentTeamId 父团队ID（可选，为空则创建顶级团队）
     * @param creatorId 创建者ID
     * @param icon 团队图标
     * @return 创建结果
     */
    RestResult<WsTeam> createTeamWithIcon(String teamName, String teamDescription, String parentTeamId, String creatorId, MultipartFile icon);
    
    /**
     * 批量删除团队成员
     *
     * @param teamId 团队ID
     * @param userIds 要删除的用户ID列表
     * @param operatorId 当前操作用户ID
     * @return 删除结果
     */
    RestResult<Object> removeTeamMembers(String teamId, List<String> userIds, String operatorId);
    
    /**
     * 获取团队层级信息（当前团队、成员和下级团队）
     *
     * @param teamId 团队ID（可选，不提供则获取用户所属的最高层级团队）
     * @param userId 当前用户ID
     * @return 团队层级信息
     */
    RestResult<TeamLevelVO> getTeamLevelInfo(String teamId, String userId);
    
    /**
     * 获取用户当前团队的树形结构，包含成员信息
     *
     * @param teamId 起始团队ID，如果为空则获取用户所属的所有团队
     * @param userId 当前用户ID
     * @param isNotJoinTeamId 筛选团队成员的团队ID（获取在teamId团队中但不在isNotJoinTeamId团队中的成员）
     * @param isNowTeam 是否只返回当前团队（不包含子团队）
     * @return 包含成员信息的团队树形结构
     */
    RestResult<List<TeamTreeWithMembersVO>> getTeamTreeWithMembers(
            String teamId, 
            String userId, 
            String isNotJoinTeamId, 
            Boolean isNowTeam);
    
    /**
     * 获取部门/成员列表（分页）
     *
     * @param teamId 团队ID
     * @param keyword 搜索关键词（姓名或手机号）
     * @param current 当前页码
     * @param size 每页数量
     * @return 部门/成员列表
     */
    RestResult<Map<String, Object>> getTeamMembersPagination(
            String teamId,
            String keyword,
            Long current,
            Long size);

    /**
     * 异步导出团队成员列表
     *
     * @param teamId 团队ID
     * @param keyword 搜索关键词（姓名或手机号）
     * @param userId 用户ID
     * @return 导出任务ID
     */
    RestResult<String> asyncExportTeamMembers(String teamId, String keyword, String userId);


    
    /**
     * 更新团队名称
     *
     * @param teamId 团队ID
     * @param teamName 更新的团队名称
     * @return 更新结果
     */
    RestResult<WsTeam> updateTeamName(String teamId, String teamName);
    
    /**
     * 更新团队名称和图标
     *
     * @param teamId 团队ID
     * @param teamName 更新的团队名称
     * @param icon 团队图标
     * @return 更新结果
     */
    RestResult<WsTeam> updateTeamNameAndIcon(String teamId, String teamName, MultipartFile icon);
    
    /**
     * 删除团队（逻辑删除）
     *
     * @param teamId 团队ID
     * @return 删除结果
     */
    RestResult<Object> deleteTeam(String teamId);
    
    /**
     * 获取团队的树形结构，按父级团队优先排序
     *
     * @param teamId 团队ID（可选，不提供则获取所有团队的树形结构）
     * @return 团队树形结构
     */
    RestResult<List<TeamTreeVO>> getTeamTreeWithParentFirst(String teamId);
    
    /**
     * 递归查找所有子团队ID
     *
     * @param parentId 父团队ID
     * @param teamIds 存储团队ID的列表
     */
    void findAllChildTeamIds(String parentId, List<String> teamIds);

    /**
     * 修改用户姓名
     *
     * @param userId 用户ID
     * @param newUsername 新用户姓名
     * @param operatorId 操作人ID
     * @return 修改结果
     */
    RestResult<Object> updateUsername(String userId, String newUsername, String operatorId);

    /**
     * 撤销用户的管理员角色
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param operatorId 操作人ID
     * @return 撤销结果
     */
    RestResult<Object> revokeAdminRole(String teamId, String userId, String operatorId);
    
    /**
     * 退出团队（非主管理员可以退出团队）
     *
     * @param teamId 团队ID
     * @param userId 当前用户ID
     * @return 退出结果
     */
    RestResult<Object> leaveTeam(String teamId, String userId);

    /**
     * 获取顶级团队ID
     *
     * @param teamId 团队ID
     * @return 顶级团队ID
     */
    String getTopLevelTeamId(String teamId);

    /**
     * 检查用户是否在指定团队树中
     *
     * @param userId 用户ID
     * @param topLevelTeamId 顶级团队ID
     * @return 是否在团队树中
     */
    boolean isUserInTeamTree(String userId, String topLevelTeamId);

    /**
     * 获取用户在团队中的角色
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 角色代码
     */
    String getUserRoleInTeam(String userId, String teamId);

    /**
     * 获取用户在团队中的角色(可以返回空的)
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 角色代码
     */
    String getUserRoleInTeamForNull(String userId, String teamId);

    /**
     * 获取用户在团队树中当前所在的团队ID
     *
     * @param userId 用户ID
     * @param topLevelTeamId 顶级团队ID
     * @return 当前团队ID
     */
    String getUserCurrentTeamInTree(String userId, String topLevelTeamId);

    /**
     * 检查用户是否在指定团队的上级
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 是否在上级
     */
    boolean isUserInUpperLevel(String userId, String teamId);

    /**
     * 获取团队树中的所有团队ID（包含当前团队及其所有子团队）
     *
     * @param teamId 团队ID
     * @return 团队ID列表
     */
    List<String> getAllTeamIdsInTree(String teamId);

    /**
     * 同意团队邀请
     *
     * @param inviteId 邀请记录ID
     * @param userId 用户ID
     * @return 处理结果
     */
    RestResult<Object> acceptInvite(String inviteId, String userId);

    /**
     * 拒绝团队邀请
     *
     * @param inviteId 邀请记录ID
     * @param userId 用户ID
     * @return 处理结果
     */
    RestResult<Object> rejectInvite(String inviteId, String userId);
}
