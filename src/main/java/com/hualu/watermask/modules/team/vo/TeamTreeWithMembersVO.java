package com.hualu.watermask.modules.team.vo;

import com.hualu.watermask.modules.user.entity.WsUser;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 包含团队成员的团队树形结构VO
 */
public class TeamTreeWithMembersVO {
    
    /**
     * 显示名称（团队名称或用户名）
     */
    private String label;
    
    /**
     * 值（团队ID或用户ID）
     */
    private String value;
    
    /**
     * 头像URL（用户专用）
     */
    private String avatar;
    
    /**
     * 子节点列表（可以是子团队或用户）
     */
    private List<TeamTreeWithMembersVO> children = new ArrayList<>();
    
    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getLabel() {
        return label;
    }
    
    /**
     * 设置显示名称
     * 
     * @param label 显示名称
     */
    public void setLabel(String label) {
        this.label = label;
    }
    
    /**
     * 获取值
     * 
     * @return 值
     */
    public String getValue() {
        return value;
    }
    
    /**
     * 设置值
     * 
     * @param value 值
     */
    public void setValue(String value) {
        this.value = value;
    }
    
    /**
     * 获取头像URL
     * 
     * @return 头像URL
     */
    public String getAvatar() {
        return avatar;
    }
    
    /**
     * 设置头像URL
     * 
     * @param avatar 头像URL
     */
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
    
    /**
     * 获取子节点列表
     * 
     * @return 子节点列表
     */
    public List<TeamTreeWithMembersVO> getChildren() {
        return children;
    }
    
    /**
     * 设置子节点列表
     * 
     * @param children 子节点列表
     */
    public void setChildren(List<TeamTreeWithMembersVO> children) {
        this.children = children;
    }
    
    /**
     * 添加子节点
     * 
     * @param child 子节点
     */
    public void addChild(TeamTreeWithMembersVO child) {
        this.children.add(child);
    }
} 