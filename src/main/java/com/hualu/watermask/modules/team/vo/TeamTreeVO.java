package com.hualu.watermask.modules.team.vo;

import java.util.ArrayList;
import java.util.List;

/**
 * 团队树形结构VO
 */
public class TeamTreeVO {
    
    /**
     * 团队ID
     */
    private String teamId;
    
    /**
     * 团队名称
     */
    private String teamName;
    
    /**
     * 团队编码
     */
    private String teamCode;
    
    /**
     * 团队描述
     */
    private String teamDescription;
    
    /**
     * 父团队ID
     */
    private String parentTeamId;

    private String label;

    private String value;

    
    /**
     * 子团队列表
     */
    private List<TeamTreeVO> children = new ArrayList<>();

    public String getTeamId() {
        return teamId;
    }

    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    public String getTeamName() {
        return teamName;
    }

    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    public String getTeamCode() {
        return teamCode;
    }

    public void setTeamCode(String teamCode) {
        this.teamCode = teamCode;
    }

    public String getTeamDescription() {
        return teamDescription;
    }

    public void setTeamDescription(String teamDescription) {
        this.teamDescription = teamDescription;
    }

    public String getParentTeamId() {
        return parentTeamId;
    }

    public void setParentTeamId(String parentTeamId) {
        this.parentTeamId = parentTeamId;
    }

    public List<TeamTreeVO> getChildren() {
        return children;
    }

    public void setChildren(List<TeamTreeVO> children) {
        this.children = children;
    }
    
    /**
     * 添加子团队
     * 
     * @param child 子团队
     */
    public void addChild(TeamTreeVO child) {
        this.children.add(child);
    }

    public String getLabel() {
        return teamName;
    }

    public String getValue() {
        return teamId;
    }
}