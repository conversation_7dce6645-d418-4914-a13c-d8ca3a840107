package com.hualu.watermask.modules.team.service.impl;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.common.exception.BusinessException;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.role.entity.WsRole;
import com.hualu.watermask.modules.role.service.WsRoleService;
import com.hualu.watermask.modules.team.entity.WsTeam;
import com.hualu.watermask.modules.team.mapper.WsTeamMapper;
import com.hualu.watermask.modules.team.service.WsTeamService;
import com.hualu.watermask.modules.team.vo.TeamLevelVO;
import com.hualu.watermask.modules.team.vo.TeamTreeVO;
import com.hualu.watermask.modules.team.vo.TeamTreeWithMembersVO;
import com.hualu.watermask.modules.user.entity.WsUser;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import com.hualu.watermask.modules.user.service.WsUserService;
import com.hualu.watermask.modules.user.service.WsUserTeamsService;
import com.hualu.watermask.modules.watermaskphoto.utils.FileUtils;
import com.hualu.watermask.modules.record.entity.WsExportRecord;
import com.hualu.watermask.modules.record.service.WsExportRecordService;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Value;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.OutputStream;

@Service
public class WsTeamServiceImpl extends ServiceImpl<WsTeamMapper, WsTeam> implements WsTeamService{

    private static final Logger log = LoggerFactory.getLogger(WsTeamServiceImpl.class);

    @Autowired
    private WsUserTeamsService wsUserTeamsService;

    @Autowired
    private WsUserService wsUserService;

    @Autowired
    private WsRoleService wsRoleService;

    @Autowired
    private WsExportRecordService wsExportRecordService;

    @Value("${export.base-path:/tmp/export}")
    private String exportBasePath;

    /**
     * 创建团队
     *
     * @param teamName 团队名称
     * @param teamDescription 团队描述
     * @param parentTeamId 父团队ID（可选，为空则创建顶级团队）
     * @param creatorId 创建者ID
     * @return 创建结果
     */
    @Override
    @Transactional
    public RestResult<WsTeam> createTeam(String teamName, String teamDescription, String parentTeamId, String creatorId) {
        // 如果父团队ID不为空，则验证父团队存在性和用户权限
        if (parentTeamId != null && !parentTeamId.isEmpty()) {
            // 验证父团队是否存在
            WsTeam parentTeam = this.getById(parentTeamId);
            if (parentTeam == null || parentTeam.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                return RestResult.error("父团队不存在或已被删除");
            }

            // 验证用户是否为父团队的管理员或主管理员
            String creatorRoleInParent = getUserRoleInTeam(creatorId, parentTeamId);
            if (creatorRoleInParent == null) {
                return RestResult.error("您不是父团队成员，无法创建子团队");
            }

            // 只有管理员或主管理员才能创建子团队
            if (!"admin".equals(creatorRoleInParent) && !"mainAdmin".equals(creatorRoleInParent)) {
                return RestResult.error("只有管理员或主管理员才能创建子团队");
            }

            // 由于同个树下只能有一个成员在一个节点，创建子团队时不需要检查重复
            // 创建人已经在父团队中，不会加入子团队
        }
        
        // 生成随机16位数字作为团队编码
        StringBuilder teamCodeBuilder = new StringBuilder();
        Random random = new Random();
        for (int i = 0; i < 16; i++) {
            teamCodeBuilder.append(random.nextInt(10));
        }
        String teamCode = teamCodeBuilder.toString();
        
        // 创建新团队对象
        WsTeam team = new WsTeam();
        team.setTeamId(UUID.randomUUID().toString().replace("-", ""));
        team.setTeamName(teamName);
        team.setTeamDescription(teamDescription);
        team.setTeamCode(teamCode);
        
        // 只有当parentTeamId不为空时才设置
        if (parentTeamId != null && !parentTeamId.isEmpty()) {
            team.setParentTeamId(parentTeamId);
        }
        
        team.setCreatedBy(creatorId);
        team.setCreationDate(new Date());
        team.setDelFlag(new BigDecimal(0));
        
        boolean result = this.save(team);
        if (result) {
            // 根据是否有父团队来决定角色分配
            if (parentTeamId == null || parentTeamId.isEmpty()) {
                // 顶级团队：创建者设为主管理员
                addUserToTeam(team.getTeamId(), creatorId, "mainAdmin", creatorId);
                log.info("创建顶级团队成功，创建者设为主管理员");
            } else {
                // 子团队：创建者不加入子团队（同个树下只能在一个节点）
                log.info("创建子团队成功，创建者保持在父团队中，不加入子团队");
            }

            return RestResult.success(team, "创建团队成功");
        } else {
            return RestResult.error("创建团队失败");
        }
    }
    
    /**
     * 批量删除团队成员
     *
     * @param teamId 团队ID
     * @param userIds 要删除的用户ID列表
     * @param operatorId 当前操作用户ID
     * @return 删除结果
     */
    @Override
    @Transactional
    public RestResult<Object> removeTeamMembers(String teamId, List<String> userIds, String operatorId) {
        // 检查团队是否存在
        WsTeam team = this.getById(teamId);
        if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
            return RestResult.error("团队不存在或已被删除");
        }
        
        // 检查当前用户是否有权限移除成员（简化处理：创建者有权限）
        if (!operatorId.equals(team.getCreatedBy())) {
            // 可以在此添加更复杂的权限检查逻辑
            return RestResult.error("您没有移除团队成员的权限");
        }
        
        // 防止删除团队创建者自己
        if (userIds.contains(team.getCreatedBy())) {
            return RestResult.error("不能移除团队创建者");
        }
        
        // 构建删除条件
        LambdaQueryWrapper<WsUserTeams> deleteQuery = new LambdaQueryWrapper<>();
        deleteQuery.eq(WsUserTeams::getTeamId, teamId);
        deleteQuery.in(WsUserTeams::getUserId, userIds);
        deleteQuery.eq(WsUserTeams::getStatus, new BigDecimal(1));
        
        // 执行批量删除
        boolean result = wsUserTeamsService.remove(deleteQuery);
        
        if (result) {
            Map<String, Object> resultMap = new HashMap<>();
            resultMap.put("teamId", teamId);
            resultMap.put("removedCount", userIds.size());
            return RestResult.success(resultMap, "成功移除团队成员");
        } else {
            return RestResult.error("移除团队成员失败");
        }
    }

    /**
     * 获取团队层级信息（当前团队、成员和下级团队）
     *
     * @param teamId 团队ID（可选，不提供则获取用户所属的最高层级团队）
     * @param userId 当前用户ID
     * @return 团队层级信息
     */
    @Override
    public RestResult<TeamLevelVO> getTeamLevelInfo(String teamId, String userId) {
        WsTeam currentTeam = null;
        
        // 如果没有提供团队ID，查找用户所属的最高层级团队
        if (teamId == null || teamId.isEmpty()) {
            // 查询用户所属的所有团队
            LambdaQueryWrapper<WsUserTeams> userTeamsQuery = new LambdaQueryWrapper<>();
            userTeamsQuery.eq(WsUserTeams::getUserId, userId);
            userTeamsQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            List<WsUserTeams> userTeams = wsUserTeamsService.list(userTeamsQuery);
            
            if (userTeams.isEmpty()) {
                return RestResult.error("用户未加入任何团队");
            }
            
            // 获取这些团队的详细信息
            List<String> teamIds = userTeams.stream()
                    .map(WsUserTeams::getTeamId)
                    .collect(Collectors.toList());
            
            LambdaQueryWrapper<WsTeam> teamsQuery = new LambdaQueryWrapper<>();
            teamsQuery.in(WsTeam::getTeamId, teamIds);
            teamsQuery.eq(WsTeam::getDelFlag, new BigDecimal(0));
            List<WsTeam> teams = this.list(teamsQuery);
            
            // 筛选出最高层级的团队（parentTeamId为空或不在用户所属的团队列表中）
            for (WsTeam team : teams) {
                String parentId = team.getParentTeamId();
                if (parentId == null || parentId.isEmpty() || !teamIds.contains(parentId)) {
                    // 找到一个最高层级团队
                    currentTeam = team;
                    break;
                }
            }
            
            // 如果没有找到最高层级团队，选择第一个团队
            if (currentTeam == null && !teams.isEmpty()) {
                currentTeam = teams.get(0);
            }
        } else {
            // 如果提供了团队ID，直接获取该团队
            currentTeam = this.getById(teamId);
            
            // 验证团队是否存在且未被删除
            if (currentTeam == null || currentTeam.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                return RestResult.error("团队不存在或已被删除");
            }
            
            // 验证用户是否为该团队成员或创建人
            boolean isCreator = userId.equals(currentTeam.getCreatedBy());
            
            // 如果不是创建人，检查是否为团队成员
            if (!isCreator) {
                LambdaQueryWrapper<WsUserTeams> checkMemberQuery = new LambdaQueryWrapper<>();
                checkMemberQuery.eq(WsUserTeams::getTeamId, teamId);
                checkMemberQuery.eq(WsUserTeams::getUserId, userId);
                checkMemberQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
                
                boolean isMember = wsUserTeamsService.count(checkMemberQuery) > 0;
                if (!isMember) {
                    // 这里可以添加全局管理员检查逻辑
                    return RestResult.error("您不是该团队成员或创建人");
                }
            }
        }
        
        // 如果还是没有找到团队，返回错误
        if (currentTeam == null) {
            return RestResult.error("未找到团队信息");
        }
        
        // 构建结果对象
        TeamLevelVO result = new TeamLevelVO();
        result.setCurrentTeam(currentTeam);
        result.setParentTeamId(currentTeam.getParentTeamId());
        
        // 查询子团队列表
        LambdaQueryWrapper<WsTeam> childTeamsQuery = new LambdaQueryWrapper<>();
        childTeamsQuery.eq(WsTeam::getParentTeamId, currentTeam.getTeamId());
        childTeamsQuery.eq(WsTeam::getDelFlag, new BigDecimal(0));
        childTeamsQuery.orderByAsc(WsTeam::getTeamName);
        List<WsTeam> childTeams = this.list(childTeamsQuery);
        result.setChildTeams(childTeams);
        
        // 查询当前团队的成员
        LambdaQueryWrapper<WsUserTeams> teamMembersQuery = new LambdaQueryWrapper<>();
        teamMembersQuery.eq(WsUserTeams::getTeamId, currentTeam.getTeamId());
        teamMembersQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
        List<WsUserTeams> teamMembers = wsUserTeamsService.list(teamMembersQuery);
        
        // 获取成员用户信息
        if (!teamMembers.isEmpty()) {
            List<String> memberIds = teamMembers.stream()
                    .map(WsUserTeams::getUserId)
                    .collect(Collectors.toList());
            
            // 这里需要注入WsUserService来查询用户信息
            LambdaQueryWrapper<WsUser> membersQuery = new LambdaQueryWrapper<>();
            membersQuery.in(WsUser::getUserId, memberIds);
            membersQuery.eq(WsUser::getDelFlag, new BigDecimal(0));
            membersQuery.orderByAsc(WsUser::getUsername);
            
            List<WsUser> members = wsUserService.list(membersQuery);
            result.setMembers(members);
        }
        
        return RestResult.success(result);
    }
    
    /**
     * 获取用户当前团队的树形结构，包含成员信息
     *
     * @param teamId 起始团队ID，如果为空则获取用户所属的所有团队
     * @param userId 当前用户ID
     * @param isNotJoinTeamId 要筛选的团队ID（获取在teamId团队中但不在isNotJoinTeamId团队中的成员）
     * @param isNowTeam 是否只返回当前团队（不包含子团队）
     * @return 包含成员信息的团队树形结构
     */
    @Override
    public RestResult<List<TeamTreeWithMembersVO>> getTeamTreeWithMembers(
            String teamId, 
            String userId, 
            String isNotJoinTeamId, 
            Boolean isNowTeam) {
        List<String> rootTeamIds = new ArrayList<>();
        
        if (teamId != null && !teamId.isEmpty()) {
            // 如果指定了特定团队，则以该团队为根
            rootTeamIds.add(teamId);
        }
        
        if (rootTeamIds.isEmpty()) {
            return RestResult.success(new ArrayList<>());
        }
        
        // 查询所有未删除的团队
        LambdaQueryWrapper<WsTeam> teamQuery = new LambdaQueryWrapper<>();
        teamQuery.eq(WsTeam::getDelFlag, new BigDecimal(0));
        teamQuery.orderByAsc(WsTeam::getTeamName);
        List<WsTeam> allTeams = this.list(teamQuery);
        
        // 团队ID到团队对象的映射
        Map<String, WsTeam> teamMap = allTeams.stream()
                .collect(Collectors.toMap(WsTeam::getTeamId, team -> team));
        
        // 获取目标团队及其子团队的ID列表
        List<String> targetTeamIds = new ArrayList<>();
        targetTeamIds.add(teamId);
        if (isNowTeam == null || !isNowTeam) {
            // 如果不是只返回当前团队，则添加子团队
            findAllChildTeamIds(teamId, targetTeamIds);
        }
        
        // 获取在排除团队中的用户ID列表（如果提供了isNotJoinTeamId）
        List<String> excludeUserIds = new ArrayList<>();
        if (isNotJoinTeamId != null && !isNotJoinTeamId.isEmpty()) {
            // 获取排除团队及其子团队的ID列表
            List<String> excludeTeamIds = new ArrayList<>();
            excludeTeamIds.add(isNotJoinTeamId);
            findAllChildTeamIds(isNotJoinTeamId, excludeTeamIds);
            
            // 查询在排除团队中的用户
            LambdaQueryWrapper<WsUserTeams> excludeQuery = new LambdaQueryWrapper<>();
            excludeQuery.in(WsUserTeams::getTeamId, excludeTeamIds);
            excludeQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            List<WsUserTeams> excludeUserTeams = wsUserTeamsService.list(excludeQuery);
            
            if (!excludeUserTeams.isEmpty()) {
                excludeUserIds = excludeUserTeams.stream()
                        .map(WsUserTeams::getUserId)
                        .collect(Collectors.toList());
            }
        }
        
        // 查询团队成员（已加入状态，在目标团队中的成员）
        LambdaQueryWrapper<WsUserTeams> memberQuery = new LambdaQueryWrapper<>();
        memberQuery.in(WsUserTeams::getTeamId, targetTeamIds);
        memberQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
        
        // 如果有需要排除的用户，则添加条件
        if (!excludeUserIds.isEmpty()) {
            memberQuery.notIn(WsUserTeams::getUserId, excludeUserIds);
        }
        
        List<WsUserTeams> allUserTeams = wsUserTeamsService.list(memberQuery);
        
        // 团队ID到团队成员关联的映射
        Map<String, List<WsUserTeams>> teamMembersMap = allUserTeams.stream()
                .collect(Collectors.groupingBy(WsUserTeams::getTeamId));
        
        // 获取所有成员用户的详细信息
        List<String> userIds = allUserTeams.stream()
                .map(WsUserTeams::getUserId)
                .distinct()
                .collect(Collectors.toList());
        
        LambdaQueryWrapper<WsUser> usersQuery = new LambdaQueryWrapper<>();
        
        if (!userIds.isEmpty()) {
            usersQuery.in(WsUser::getUserId, userIds);
            usersQuery.eq(WsUser::getDelFlag, new BigDecimal(0));
        } else {
            // 如果没有符合条件的用户，返回空列表
            return RestResult.success(new ArrayList<>());
        }
        
        List<WsUser> allUsers = wsUserService.list(usersQuery);
        
        // 用户ID到用户对象的映射
        Map<String, WsUser> userMap = allUsers.stream()
                .collect(Collectors.toMap(WsUser::getUserId, user -> user));
        
        // 构建结果树
        List<TeamTreeWithMembersVO> result = new ArrayList<>();
        
        for (String rootId : rootTeamIds) {
            WsTeam rootTeam = teamMap.get(rootId);
            if (rootTeam != null) {
                TeamTreeWithMembersVO rootNode = buildTeamTreeWithMembers(
                        rootTeam, teamMap, teamMembersMap, userMap, 
                        isNowTeam != null && isNowTeam);
                
                // 只有当节点有子节点或成员时才添加
                if (rootNode.getChildren() != null && !rootNode.getChildren().isEmpty()) {
                    result.add(rootNode);
                }
            }
        }
        
        return RestResult.success(result);
    }
    
    /**
     * 递归构建团队树结构
     *
     * @param team 当前团队
     * @param teamMap 所有团队的映射
     * @param teamMembersMap 团队成员映射
     * @param userMap 用户信息映射
     * @param isNowTeamOnly 是否只返回当前团队（不包含子团队）
     * @return 团队树节点
     */
    private TeamTreeWithMembersVO buildTeamTreeWithMembers(
            WsTeam team, 
            Map<String, WsTeam> teamMap,
            Map<String, List<WsUserTeams>> teamMembersMap,
            Map<String, WsUser> userMap,
            boolean isNowTeamOnly) {
        
        // 创建当前团队节点
        TeamTreeWithMembersVO node = new TeamTreeWithMembersVO();
        
        // 获取团队成员
        List<WsUserTeams> teamMembers = teamMembersMap.getOrDefault(team.getTeamId(), new ArrayList<>());
        int memberCount = teamMembers.size();
        
        // 设置团队节点属性
        node.setValue(team.getTeamId());
        node.setLabel(team.getTeamName() + "(" + memberCount + ")");
        
        // 查找并添加子团队（除非isNowTeamOnly为true）
        List<TeamTreeWithMembersVO> childTeamNodes = new ArrayList<>();
        if (!isNowTeamOnly) {
            for (WsTeam potentialChild : teamMap.values()) {
                if (team.getTeamId().equals(potentialChild.getParentTeamId())) {
                    TeamTreeWithMembersVO childNode = buildTeamTreeWithMembers(
                            potentialChild, teamMap, teamMembersMap, userMap, isNowTeamOnly);
                    
                    // 只有当子节点有成员或子团队时才添加
                    if (childNode.getChildren() != null && !childNode.getChildren().isEmpty()) {
                        childTeamNodes.add(childNode);
                    }
                }
            }
        }
        
        // 添加团队成员作为叶子节点
        for (WsUserTeams userTeam : teamMembers) {
            WsUser user = userMap.get(userTeam.getUserId());
            if (user != null) {
                TeamTreeWithMembersVO userNode = new TeamTreeWithMembersVO();
                userNode.setValue(user.getUserId());
                userNode.setLabel(user.getUsername());
                userNode.setAvatar(user.getAvatar() != null ? user.getAvatar() : "");
                childTeamNodes.add(userNode);
            }
        }
        
        // 设置子节点列表
        if (!childTeamNodes.isEmpty()) {
            node.setChildren(childTeamNodes);
        }
        
        return node;
    }
    
    /**
     * 更新团队名称
     *
     * @param teamId 团队ID
     * @param teamName 更新的团队名称
     * @return 更新结果
     */
    @Override
    @Transactional
    public RestResult<WsTeam> updateTeamName(String teamId, String teamName) {
        WsTeam team = this.getById(teamId);
        if (team == null) {
            return RestResult.error("未找到指定的团队或团队已被删除");
        }
        if(StringUtils.isBlank(teamName)){
            return RestResult.error("团队名称不能为空");
        }
        team.setTeamName(teamName);
        boolean result = this.updateById(team);
        if (result) {
            return RestResult.success(team, "更新团队成功");
        } else {
            return RestResult.error("更新团队失败，团队可能不存在");
        }
    }
    
    /**
     * 删除团队（逻辑删除）
     *
     * @param teamId 团队ID
     * @return 删除结果
     */
    @Override
    @Transactional
    public RestResult<Object> deleteTeam(String teamId) {
        WsTeam team = new WsTeam();
        team.setTeamId(teamId);
        team.setDelFlag(new BigDecimal(1));
        
        boolean result = this.updateById(team);
        if (result) {
            return RestResult.success(null, "删除团队成功");
        } else {
            return RestResult.error("删除团队失败，团队可能不存在");
        }
    }
    
    /**
     * 创建团队（带图标）
     *
     * @param teamName 团队名称
     * @param teamDescription 团队描述
     * @param parentTeamId 父团队ID（可选，为空则创建顶级团队）
     * @param creatorId 创建者ID
     * @param icon 团队图标
     * @return 创建结果
     */
    @Override
    @Transactional
    public RestResult<WsTeam> createTeamWithIcon(String teamName, String teamDescription, String parentTeamId, String creatorId, MultipartFile icon) {
        try {
            // 先创建团队
            RestResult<WsTeam> result = createTeam(teamName, teamDescription, parentTeamId, creatorId);
            
            // 如果创建成功且提供了图标，则处理图标上传
            if (result.getCode() == RestResult.SUCCESS && icon != null && !icon.isEmpty() && result.getData() != null) {
                WsTeam team = result.getData();
                
                // 上传图标
                List<MultipartFile> iconFiles = new ArrayList<>();
                iconFiles.add(icon);
                List<String> iconIds = FileUtils.uploadImage(iconFiles);
                
                if (iconIds != null && !iconIds.isEmpty()) {
                    // 更新团队图标
                    team.setIcon(iconIds.get(0));
                    this.updateById(team);
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("创建团队（带图标）失败", e);
            return RestResult.error("创建团队失败：" + e.getMessage());
        }
    }
    
    /**
     * 更新团队名称和图标
     *
     * @param teamId 团队ID
     * @param teamName 更新的团队名称
     * @param icon 团队图标
     * @return 更新结果
     */
    @Override
    @Transactional
    public RestResult<WsTeam> updateTeamNameAndIcon(String teamId, String teamName, MultipartFile icon) {
        try {
            // 先更新团队名称
            RestResult<WsTeam> result = updateTeamName(teamId, teamName);
            
            // 如果更新成功且提供了图标，则处理图标上传
            if (result.getCode() == RestResult.SUCCESS && icon != null && !icon.isEmpty() && result.getData() != null) {
                WsTeam team = result.getData();
                
                // 上传图标
                List<MultipartFile> iconFiles = new ArrayList<>();
                iconFiles.add(icon);
                List<String> iconIds = FileUtils.uploadImage(iconFiles);
                
                if (iconIds != null && !iconIds.isEmpty()) {
                    // 更新团队图标
                    team.setIcon(iconIds.get(0));
                    this.updateById(team);
                }
            }
            
            return result;
        } catch (Exception e) {
            log.error("更新团队名称和图标失败", e);
            return RestResult.error("更新团队失败：" + e.getMessage());
        }
    }

    /**
     * 获取团队的树形结构，按父级团队优先排序
     *
     * @param teamId 团队ID（可选，不提供则获取所有团队的树形结构）
     * @return 团队树形结构
     */
    @Override
    public RestResult<List<TeamTreeVO>> getTeamTreeWithParentFirst(String teamId) {
        try {
            // 查询条件：未删除的团队
            LambdaQueryWrapper<WsTeam> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(WsTeam::getDelFlag, new BigDecimal(0));
            queryWrapper.orderByAsc(WsTeam::getTeamName);
            
            List<WsTeam> teamsToProcess;
            
            if (teamId != null && !teamId.isEmpty()) {
                // 如果提供了teamId参数，则只查询该团队及其子团队
                // 首先查询目标团队
                WsTeam targetTeam = this.getById(teamId);
                if (targetTeam == null || targetTeam.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                    return RestResult.success(new ArrayList<>());  // 团队不存在或已删除
                }
                
                // 递归查询所有子团队
                List<String> teamIds = new ArrayList<>();
                teamIds.add(teamId);
                findAllChildTeamIds(teamId, teamIds);
                
                // 查询目标团队及所有子团队
                queryWrapper.in(WsTeam::getTeamId, teamIds);
                teamsToProcess = this.list(queryWrapper);
            } else {
                // 如果没有提供teamId参数，则查询所有团队
                teamsToProcess = this.list(queryWrapper);
            }
            
            // 按照层级排序团队：父级团队排在前面，子团队排在后面
            Map<String, List<WsTeam>> parentChildrenMap = new HashMap<>();
            Map<String, WsTeam> teamMap = new HashMap<>();
            List<WsTeam> rootTeams = new ArrayList<>();
            
            // 构建映射关系
            for (WsTeam team : teamsToProcess) {
                teamMap.put(team.getTeamId(), team);
                
                String parentId = team.getParentTeamId();
                if (parentId == null || parentId.isEmpty()) {
                    // 顶级团队
                    rootTeams.add(team);
                } else {
                    // 将团队添加到父团队的子列表中
                    parentChildrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(team);
                }
            }
            
            // 按层级排序的团队列表
            List<WsTeam> sortedTeams = new ArrayList<>();
            
            // 递归添加团队，确保父级团队先于子团队
            if (teamId != null && !teamId.isEmpty()) {
                // 如果指定了teamId，则只从目标团队开始
                WsTeam targetTeam = teamMap.get(teamId);
                if (targetTeam != null) {
                    addTeamAndChildrenInOrder(targetTeam, parentChildrenMap, sortedTeams);
                }
            } else {
                // 否则从所有根团队开始
                for (WsTeam rootTeam : rootTeams) {
                    addTeamAndChildrenInOrder(rootTeam, parentChildrenMap, sortedTeams);
                }
            }
            
            // 转换为VO对象
            List<TeamTreeVO> teamVOList = sortedTeams.stream().map(team -> {
                TeamTreeVO vo = new TeamTreeVO();
                vo.setTeamId(team.getTeamId());
                vo.setTeamName(team.getTeamName());
                vo.setTeamCode(team.getTeamCode());
                vo.setTeamDescription(team.getTeamDescription());
                vo.setParentTeamId(team.getParentTeamId());
                return vo;
            }).collect(Collectors.toList());
            
            // 构建团队ID到团队VO对象的映射，方便快速查找
            Map<String, TeamTreeVO> teamVOMap = new HashMap<>();
            for (TeamTreeVO team : teamVOList) {
                teamVOMap.put(team.getTeamId(), team);
            }
            
            // 构建树形结构
            List<TeamTreeVO> resultTeams = new ArrayList<>();
            
            if (teamId != null && !teamId.isEmpty()) {
                // 只返回目标团队作为根节点
                TeamTreeVO rootTeam = teamVOMap.get(teamId);
                if (rootTeam != null) {
                    // 构建子树
                    for (TeamTreeVO team : teamVOList) {
                        if (!team.getTeamId().equals(teamId)) { // 不是根节点
                            String parentId = team.getParentTeamId();
                            if (parentId != null && !parentId.isEmpty()) {
                                TeamTreeVO parentTeam = teamVOMap.get(parentId);
                                if (parentTeam != null) {
                                    parentTeam.addChild(team);
                                }
                            }
                        }
                    }
                    resultTeams.add(rootTeam);
                }
            } else {
                // 构建完整的树形结构
                for (TeamTreeVO team : teamVOList) {
                    String parentId = team.getParentTeamId();
                    if (parentId == null || parentId.isEmpty()) {
                        // 顶级团队
                        resultTeams.add(team);
                    } else {
                        // 子团队，添加到父团队的children中
                        TeamTreeVO parentTeam = teamVOMap.get(parentId);
                        if (parentTeam != null) {
                            parentTeam.addChild(team);
                        } else {
                            // 如果找不到父团队，则作为顶级团队处理
                            resultTeams.add(team);
                        }
                    }
                }
            }
            
            return RestResult.success(resultTeams);
        } catch (Exception e) {
            log.error("获取团队树形结构失败", e);
            return RestResult.error("获取团队树形结构失败：" + e.getMessage());
        }
    }
    
    /**
     * 递归添加团队及其子团队，确保父级团队先于子团队
     *
     * @param team 当前团队
     * @param parentChildrenMap 父子团队映射
     * @param result 结果列表
     */
    private void addTeamAndChildrenInOrder(WsTeam team, Map<String, List<WsTeam>> parentChildrenMap, List<WsTeam> result) {
        // 先添加当前团队
        result.add(team);
        
        // 再添加子团队
        List<WsTeam> children = parentChildrenMap.get(team.getTeamId());
        if (children != null) {
            for (WsTeam child : children) {
                addTeamAndChildrenInOrder(child, parentChildrenMap, result);
            }
        }
    }
    
    /**
     * 递归查找所有子团队ID
     *
     * @param parentId 父团队ID
     * @param teamIds 存储团队ID的列表
     */
    @Override
    public void findAllChildTeamIds(String parentId, List<String> teamIds) {
        LambdaQueryWrapper<WsTeam> childQuery = new LambdaQueryWrapper<>();
        childQuery.eq(WsTeam::getParentTeamId, parentId);
        childQuery.eq(WsTeam::getDelFlag, new BigDecimal(0));
        
        List<WsTeam> children = this.list(childQuery);
        for (WsTeam child : children) {
            String childId = child.getTeamId();
            teamIds.add(childId);
            // 递归查找子团队的子团队
            findAllChildTeamIds(childId, teamIds);
        }
    }

    /**
     * 获取部门/成员列表（分页）
     *
     * @param teamId 团队ID
     * @param keyword 搜索关键词（姓名或手机号）
     * @param current 当前页码
     * @param size 每页数量
     * @return 部门/成员列表
     */
    @Override
    public RestResult<Map<String, Object>> getTeamMembersPagination(
            String teamId,
            String keyword,
            Long current,
            Long size) {
        try {
            // 查询团队及其子团队
            List<String> teamIds = new ArrayList<>();
            teamIds.add(teamId);
            findAllChildTeamIds(teamId, teamIds);
            
            // 查询团队成员关联
            LambdaQueryWrapper<WsUserTeams> userTeamsQuery = new LambdaQueryWrapper<>();
            userTeamsQuery.in(WsUserTeams::getTeamId, teamIds);
            userTeamsQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            List<WsUserTeams> userTeams = wsUserTeamsService.list(userTeamsQuery);
            
            // 获取团队ID到团队名称的映射
            Map<String, String> teamNameMap = new HashMap<>();
            for (String id : teamIds) {
                WsTeam team = getById(id);
                if (team != null) {
                    teamNameMap.put(id, team.getTeamName());
                }
            }
            
            // 构建用户ID到团队和角色的映射
            Map<String, String> userTeamMap = new HashMap<>();
            Map<String, String> userRoleIdMap = new HashMap<>();
            for (WsUserTeams ut : userTeams) {
                userTeamMap.put(ut.getUserId(), ut.getTeamId());
                userRoleIdMap.put(ut.getUserId(), ut.getRoleId());
            }
            
            // 获取所有角色
            List<WsRole> allRoles = wsRoleService.list();
            Map<String, String> roleIdToNameMap = allRoles.stream()
                    .collect(Collectors.toMap(WsRole::getRoleId, WsRole::getRoleName));
            
            // 查询条件：获取团队成员用户信息
            List<String> userIds = userTeams.stream()
                    .map(WsUserTeams::getUserId)
                    .collect(Collectors.toList());
            
            if (userIds.isEmpty()) {
                // 没有成员，返回空结果
                Map<String, Object> emptyResult = new HashMap<>();
                emptyResult.put("columns", getTeamMembersColumns());
                emptyResult.put("data", new ArrayList<>());
                emptyResult.put("total", 0);
                emptyResult.put("current", current);
                emptyResult.put("size", size);
                return RestResult.success(emptyResult);
            }
            
            // 查询用户
            LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.in(WsUser::getUserId, userIds);
            userQuery.eq(WsUser::getDelFlag, new BigDecimal(0)); // 未删除
            
            // 关键字搜索
            if (keyword != null && !keyword.isEmpty()) {
                userQuery.and(wrapper -> wrapper
                        .like(WsUser::getUsername, keyword)
                        .or()
                        .like(WsUser::getPhoneNumber, keyword));
            }
            
            // 计算总数
            long total = wsUserService.count(userQuery);
            
            // 分页查询
            Page<WsUser> page = new Page<>(current, size);
            userQuery.orderByAsc(WsUser::getUsername); // 按姓名排序
            Page<WsUser> userPage = wsUserService.page(page, userQuery);
            List<WsUser> users = userPage.getRecords();
            
            // 构建结果数据
            List<Map<String, Object>> data = new ArrayList<>();
            for (WsUser user : users) {
                Map<String, Object> item = new HashMap<>();
                item.put("userId", user.getUserId());
                item.put("username", user.getUsername());
                item.put("phoneNumber", user.getPhoneNumber());
                
                // 获取部门（团队名称）
                String userTeamId = userTeamMap.get(user.getUserId());
                String dept = teamNameMap.getOrDefault(userTeamId, "");
                item.put("dept", dept);
                item.put("currentTeamId", userTeamId);

                // 获取角色
                String roleId = userRoleIdMap.get(user.getUserId());
                String roleName = roleIdToNameMap.getOrDefault(roleId, "");
                item.put("type", roleName);
                
                // 上次登录时间
                item.put("lastActive", formatDate(user.getLoginDate()));
                
                data.add(item);
            }
            
            // 构建最终结果
            Map<String, Object> result = new HashMap<>();
            result.put("columns", getTeamMembersColumns());
            result.put("data", data);
            result.put("total", total);
            result.put("current", current);
            result.put("size", size);
            
            return RestResult.success(result);
        } catch (Exception e) {
            log.error("获取部门/成员列表失败", e);
            return RestResult.error("获取部门/成员列表失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取部门/成员列表的表头定义
     * 
     * @return 表头定义列表
     */
    private List<Map<String, Object>> getTeamMembersColumns() {
        List<Map<String, Object>> columns = new ArrayList<>();
        
        // 添加选择列
        Map<String, Object> selectColumn = new HashMap<>();
        selectColumn.put("colKey", "row-select");
        selectColumn.put("type", "multiple");
        selectColumn.put("width", 48);
        selectColumn.put("fixed", "left");
        columns.add(selectColumn);
        
        // 添加姓名列
        Map<String, Object> usernameColumn = new HashMap<>();
        usernameColumn.put("title", "姓名");
        usernameColumn.put("colKey", "username");
        usernameColumn.put("width", 160);
        usernameColumn.put("cell", "username");
        columns.add(usernameColumn);
        
        // 添加手机号列
        Map<String, Object> phoneColumn = new HashMap<>();
        phoneColumn.put("title", "手机号");
        phoneColumn.put("colKey", "phoneNumber");
        phoneColumn.put("width", 140);
        columns.add(phoneColumn);
        
        // 添加部门列
        Map<String, Object> deptColumn = new HashMap<>();
        deptColumn.put("title", "部门");
        deptColumn.put("colKey", "dept");
        deptColumn.put("width", 120);
        deptColumn.put("cell", "dept");
        columns.add(deptColumn);
        
        // 添加角色列
        Map<String, Object> roleColumn = new HashMap<>();
        roleColumn.put("title", "角色");
        roleColumn.put("colKey", "type");
        roleColumn.put("width", 120);
        roleColumn.put("cell", "type");
        columns.add(roleColumn);
        
        // 添加上次登录时间列
        Map<String, Object> lastActiveColumn = new HashMap<>();
        lastActiveColumn.put("title", "上次登录时间");
        lastActiveColumn.put("colKey", "lastActive");
        lastActiveColumn.put("width", 120);
        columns.add(lastActiveColumn);
        
        // 添加操作列
        Map<String, Object> opColumn = new HashMap<>();
        opColumn.put("title", "操作");
        opColumn.put("colKey", "op");
        opColumn.put("width", 120);
        opColumn.put("cell", "op");
        opColumn.put("fixed", "right");
        columns.add(opColumn);
        
        return columns;
    }
    
    /**
     * 格式化日期
     *
     * @param date 日期
     * @return 格式化后的日期字符串
     */
    private String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return sdf.format(date);
    }

    /**
     * 修改用户姓名
     *
     * @param userId 用户ID
     * @param newUsername 新用户姓名
     * @param operatorId 操作人ID
     * @return 修改结果
     */
    @Override
    @Transactional
    public RestResult<Object> updateUsername(String userId, String newUsername, String operatorId) {
        try {
            // 检查用户是否存在
            WsUser user = wsUserService.getById(userId);
            if (user == null || user.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                return RestResult.error("用户不存在或已被删除");
            }
            
            // 验证是否有权限修改（只有用户自己或管理员可以修改）
            if (!operatorId.equals(userId)) {
                // 这里可以添加管理员权限检查
                return RestResult.error("您没有权限修改其他用户的姓名");
            }
            
            // 验证新姓名不为空
            if (newUsername == null || newUsername.trim().isEmpty()) {
                return RestResult.error("新姓名不能为空");
            }
            
            // 更新用户姓名
            WsUser updateUser = new WsUser();
            updateUser.setUserId(userId);
            updateUser.setUsername(newUsername);
            
            boolean result = wsUserService.updateById(updateUser);
            
            if (result) {
                return RestResult.success(null, "用户姓名修改成功");
            } else {
                return RestResult.error("用户姓名修改失败");
            }
        } catch (Exception e) {
            log.error("修改用户姓名失败", e);
            return RestResult.error("修改用户姓名失败：" + e.getMessage());
        }
    }
    
    /**
     * 撤销用户的管理员角色
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param operatorId 操作人ID
     * @return 撤销结果
     */
    @Override
    @Transactional
    public RestResult<Object> revokeAdminRole(String teamId, String userId, String operatorId) {
        try {
            // 检查团队是否存在
            WsTeam team = this.getById(teamId);
            if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                return RestResult.error("团队不存在或已被删除");
            }
            
            // 检查当前用户是否有权限操作（简化处理：只有团队创建者有权限）
            if (!operatorId.equals(team.getCreatedBy())) {
                // 可以在此添加更复杂的权限检查逻辑
                return RestResult.error("您没有权限撤销管理员角色");
            }
            
            // 检查用户是否存在
            WsUser user = wsUserService.getById(userId);
            if (user == null || user.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                return RestResult.error("用户不存在或已被删除");
            }
            
            // 检查用户是否是团队成员
            LambdaQueryWrapper<WsUserTeams> memberQuery = new LambdaQueryWrapper<>();
            memberQuery.eq(WsUserTeams::getTeamId, teamId);
            memberQuery.eq(WsUserTeams::getUserId, userId);
            memberQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            
            WsUserTeams userTeam = wsUserTeamsService.getOne(memberQuery);
            if (userTeam == null) {
                return RestResult.error("该用户不是团队成员");
            }
            
            // 检查用户是否拥有管理员角色
            if (userTeam.getRoleId() == null || !userTeam.getRoleId().equals("3A0AF79837AE14EDE06386001DAC6B60")) {
                return RestResult.error("该用户不是管理员");
            }
            
            // 将用户角色修改为普通成员（正式工）
            userTeam.setRoleId("3A0B18F5278F1A4CE06386001DAC6B87"); // 正式工角色ID
            
            boolean result = wsUserTeamsService.updateById(userTeam);
            
            if (result) {
                Map<String, Object> resultData = new HashMap<>();
                resultData.put("userId", userId);
                resultData.put("teamId", teamId);
                resultData.put("username", user.getUsername());
                resultData.put("teamName", team.getTeamName());
                return RestResult.success(resultData, "已成功撤销管理员角色");
            } else {
                return RestResult.error("撤销管理员角色失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("撤销管理员角色失败", e);
            return RestResult.error("撤销管理员角色失败：" + e.getMessage());
        }
    }
    
    /**
     * 退出团队（非主管理员可以退出团队）
     *
     * @param teamId 团队ID
     * @param userId 当前用户ID
     * @return 退出结果
     */
    @Override
    @Transactional
    public RestResult<Object> leaveTeam(String teamId, String userId) {
        try {
            // 检查团队是否存在
            WsTeam team = this.getById(teamId);
            if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                return RestResult.error("团队不存在或已被删除");
            }
            
            // 检查用户是否为团队成员
            LambdaQueryWrapper<WsUserTeams> userTeamQuery = new LambdaQueryWrapper<>();
            userTeamQuery.eq(WsUserTeams::getTeamId, teamId);
            userTeamQuery.eq(WsUserTeams::getUserId, userId);
            userTeamQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            WsUserTeams userTeam = wsUserTeamsService.getOne(userTeamQuery);
            
            if (userTeam == null) {
                return RestResult.error("您不是该团队成员");
            }
            
            // 检查用户角色是否为主管理员
            if ("mainAdmin".equals(userTeam.getRoleId())) {
                return RestResult.error("主管理员不能退出团队，请先转让管理员权限");
            }
            
            // 删除用户与团队的关联关系
            boolean result = wsUserTeamsService.removeById(userTeam.getId());
            
            if (result) {
                return RestResult.success( "您已成功退出团队");
            } else {
                return RestResult.error("退出团队失败，请稍后重试");
            }
        } catch (Exception e) {
            log.error("退出团队失败", e);
            return RestResult.error("退出团队失败: " + e.getMessage());
        }
    }

    /**
     * 异步导出团队成员列表
     *
     * @param teamId 团队ID
     * @param keyword 搜索关键词（姓名或手机号）
     * @param userId 用户ID
     * @return 导出任务ID
     */
    @Override
    @Transactional
    public RestResult<String> asyncExportTeamMembers(String teamId, String keyword, String userId) {
        try {
            // 获取用户信息
            WsUser user = wsUserService.getById(userId);
            if (user == null) {
                return RestResult.error("用户不存在");
            }

            // 检查团队是否存在
            WsTeam team = this.getById(teamId);
            if (team == null || team.getDelFlag().compareTo(new BigDecimal(0)) != 0) {
                return RestResult.error("团队不存在或已被删除");
            }

            // 1. 创建导出记录
            WsExportRecord record = new WsExportRecord();
            record.setId(UUID.randomUUID().toString().replace("-", ""));
            record.setApplyTime(new Date());
            record.setStatus(0L); // 0-处理中，1-成功，-1-失败
            record.setCreatedBy(userId);
            record.setCreatedDate(new Date());
            record.setIsDeleted(0L);

            // 设置文件名和类型
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
            String fileName = "成员/部门管理-导出名单_" + team.getTeamName() + "_" + sdf.format(new Date()) + ".xlsx";
            record.setFileName(fileName);
            record.setFileType("成员/部门管理-导出名单");

            // 设置时间范围（当前时间）
            record.setTimeRange(sdf.format(new Date()));

            // 保存记录
            wsExportRecordService.save(record);

            // 2. 异步执行导出
            try {
                // 创建导出目录
                SimpleDateFormat dateDirFormat = new SimpleDateFormat("yyyyMMdd");
                String dateDir = dateDirFormat.format(new Date());
                String userDir = userId;

                // 创建目录：基础路径/日期/用户ID/
                Path exportDir = Paths.get(exportBasePath, dateDir, userDir);
                Files.createDirectories(exportDir);

                // 导出文件的完整路径
                Path exportFilePath = exportDir.resolve(record.getFileName());

                // 执行导出逻辑，将结果保存到文件
                boolean success = exportTeamMembersToFile(teamId, keyword, exportFilePath.toString());

                // 更新记录状态
                if (success) {
                    record.setStatus(1L); // 成功
                    record.setLocalPath(exportFilePath.toString());

                    // 设置导出的成员数量
                    long memberCount = getTeamMemberCount(teamId, keyword);
                    record.setDownloadCount(memberCount);
                    record.setMemberCount(memberCount);

                    log.info("团队成员列表导出成功: {}", exportFilePath);
                } else {
                    record.setStatus(-1L); // 失败
                    log.error("团队成员列表导出失败");
                }

                wsExportRecordService.updateById(record);

            } catch (Exception e) {
                log.error("导出团队成员列表异常", e);
                record.setStatus(-1L); // 失败
                wsExportRecordService.updateById(record);
            }

            return RestResult.success(record.getId(), "导出任务已创建，请稍后在下载记录中查看");

        } catch (Exception e) {
            log.error("创建团队成员导出任务失败", e);
            return RestResult.error("创建导出任务失败: " + e.getMessage());
        }
    }

    /**
     * 将团队成员数据导出到文件
     *
     * @param teamId 团队ID
     * @param keyword 搜索关键词
     * @param filePath 文件路径
     * @return 是否成功
     */
    private boolean exportTeamMembersToFile(String teamId, String keyword, String filePath) {
        try {
            // 获取团队信息
            WsTeam team = this.getById(teamId);
            if (team == null) {
                return false;
            }

            // 查询当前团队的成员（不分页，获取所有数据）
            LambdaQueryWrapper<WsUserTeams> teamMembersQuery = new LambdaQueryWrapper<>();
            teamMembersQuery.eq(WsUserTeams::getTeamId, teamId);
            teamMembersQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            List<WsUserTeams> teamMembers = wsUserTeamsService.list(teamMembersQuery);

            if (teamMembers.isEmpty()) {
                // 创建空文件
                return createEmptyExcelFile(filePath, "团队成员列表");
            }

            // 获取成员ID列表
            List<String> memberIds = teamMembers.stream()
                    .map(WsUserTeams::getUserId)
                    .collect(Collectors.toList());

            // 查询成员信息
            LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.in(WsUser::getUserId, memberIds);
            userQuery.eq(WsUser::getDelFlag, new BigDecimal(0));

            // 如果提供了关键字，进行模糊搜索
            if (keyword != null && !keyword.isEmpty()) {
                userQuery.and(wrapper -> wrapper
                        .like(WsUser::getUsername, keyword)
                        .or()
                        .like(WsUser::getPhoneNumber, keyword));
            }

            userQuery.orderByAsc(WsUser::getUsername);
            List<WsUser> users = wsUserService.list(userQuery);

            // 获取用户ID到团队角色ID的映射
            Map<String, String> userRoleMap = new HashMap<>();
            for (WsUserTeams ut : teamMembers) {
                if (ut.getRoleId() != null) {
                    userRoleMap.put(ut.getUserId(), ut.getRoleId());
                }
            }

            // 获取所有需要的角色信息
            Map<String, String> roleNameMap = new HashMap<>();
            if (!userRoleMap.isEmpty()) {
                LambdaQueryWrapper<WsRole> roleQuery = new LambdaQueryWrapper<>();
                roleQuery.in(WsRole::getRoleId, userRoleMap.values());
                roleQuery.eq(WsRole::getDelFlag, new BigDecimal(0));
                List<WsRole> roles = wsRoleService.list(roleQuery);

                // 构建角色ID到角色名称的映射
                for (WsRole role : roles) {
                    roleNameMap.put(role.getRoleId(), role.getRoleName());
                }
            }

            // 构建导出数据
            List<Map<String, Object>> exportData = new ArrayList<>();
            for (WsUser user : users) {
                Map<String, Object> item = new LinkedHashMap<>();
                item.put("userId", user.getUserId());
                item.put("username", user.getUsername());
                item.put("phoneNumber", user.getPhoneNumber());
                item.put("dept", team.getTeamName());

                // 获取角色名称
                String roleId = userRoleMap.get(user.getUserId());
                String roleName = roleNameMap.getOrDefault(roleId, "普通成员");
                item.put("type", roleName);

                // 格式化上次登录时间
                Date loginDate = user.getLoginDate();
                String lastActive = loginDate != null ? new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(loginDate) : "";
                item.put("lastActive", lastActive);

                exportData.add(item);
            }

            // 创建Excel文件
            return createTeamMembersExcelFile(filePath, exportData, team.getTeamName());

        } catch (Exception e) {
            log.error("导出团队成员到文件失败", e);
            return false;
        }
    }

    /**
     * 创建团队成员Excel文件
     *
     * @param filePath 文件路径
     * @param data 数据列表
     * @param teamName 团队名称
     * @return 是否成功
     */
    private boolean createTeamMembersExcelFile(String filePath, List<Map<String, Object>> data, String teamName) {
        try (XSSFWorkbook workbook = new XSSFWorkbook();
             FileOutputStream fileOut = new FileOutputStream(filePath)) {

            Sheet sheet = workbook.createSheet("团队成员列表");

            // 创建标题样式
            CellStyle titleStyle = workbook.createCellStyle();
            Font titleFont = workbook.createFont();
            titleFont.setBold(true);
            titleFont.setFontHeightInPoints((short) 16);
            titleStyle.setFont(titleFont);
            titleStyle.setAlignment(HorizontalAlignment.CENTER);

            // 创建表头样式
            CellStyle headerStyle = workbook.createCellStyle();
            Font headerFont = workbook.createFont();
            headerFont.setBold(true);
            headerFont.setFontHeightInPoints((short) 12);
            headerStyle.setFont(headerFont);
            headerStyle.setAlignment(HorizontalAlignment.CENTER);
            headerStyle.setFillForegroundColor(IndexedColors.GREY_25_PERCENT.getIndex());
            headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 创建数据样式
            CellStyle dataStyle = workbook.createCellStyle();
            dataStyle.setAlignment(HorizontalAlignment.LEFT);

            // 创建标题行
            Row titleRow = sheet.createRow(0);
            Cell titleCell = titleRow.createCell(0);
            titleCell.setCellValue(teamName + " - 团队成员列表");
            titleCell.setCellStyle(titleStyle);

            // 合并标题行单元格
            sheet.addMergedRegion(new org.apache.poi.ss.util.CellRangeAddress(0, 0, 0, 5));

            // 创建表头
            Row headerRow = sheet.createRow(1);
            String[] headers = {"用户ID", "姓名", "手机号", "部门", "角色", "最后活跃时间"};
            String[] fieldKeys = {"userId", "username", "phoneNumber", "dept", "type", "lastActive"};

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充数据
            for (int i = 0; i < data.size(); i++) {
                Row dataRow = sheet.createRow(i + 2);
                Map<String, Object> rowData = data.get(i);

                for (int j = 0; j < fieldKeys.length; j++) {
                    Cell cell = dataRow.createCell(j);
                    Object value = rowData.get(fieldKeys[j]);
                    cell.setCellValue(value != null ? value.toString() : "");
                    cell.setCellStyle(dataStyle);
                }
            }

            // 自动调整列宽
            for (int i = 0; i < headers.length; i++) {
                sheet.autoSizeColumn(i);
                // 设置最小列宽
                int currentWidth = sheet.getColumnWidth(i);
                if (currentWidth < 256 * 15) {
                    sheet.setColumnWidth(i, 256 * 15);
                }
            }

            workbook.write(fileOut);
            return true;

        } catch (IOException e) {
            log.error("创建团队成员Excel文件失败", e);
            return false;
        }
    }

    /**
     * 创建空的Excel文件
     *
     * @param filePath 文件路径
     * @param title 标题
     * @return 是否成功
     */
    private boolean createEmptyExcelFile(String filePath, String title) {
        try (XSSFWorkbook workbook = new XSSFWorkbook();
             FileOutputStream fileOut = new FileOutputStream(filePath)) {

            Sheet sheet = workbook.createSheet(title);
            Row row = sheet.createRow(0);
            Cell cell = row.createCell(0);
            cell.setCellValue("暂无数据");

            workbook.write(fileOut);
            return true;

        } catch (IOException e) {
            log.error("创建空Excel文件失败", e);
            return false;
        }
    }

    /**
     * 获取团队成员数量
     *
     * @param teamId 团队ID
     * @param keyword 搜索关键词
     * @return 成员数量
     */
    private long getTeamMemberCount(String teamId, String keyword) {
        try {
            // 查询当前团队的成员
            LambdaQueryWrapper<WsUserTeams> teamMembersQuery = new LambdaQueryWrapper<>();
            teamMembersQuery.eq(WsUserTeams::getTeamId, teamId);
            teamMembersQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态
            List<WsUserTeams> teamMembers = wsUserTeamsService.list(teamMembersQuery);

            if (teamMembers.isEmpty()) {
                return 0;
            }

            // 获取成员ID列表
            List<String> memberIds = teamMembers.stream()
                    .map(WsUserTeams::getUserId)
                    .collect(Collectors.toList());

            // 查询成员信息
            LambdaQueryWrapper<WsUser> userQuery = new LambdaQueryWrapper<>();
            userQuery.in(WsUser::getUserId, memberIds);
            userQuery.eq(WsUser::getDelFlag, new BigDecimal(0));

            // 如果提供了关键字，进行模糊搜索
            if (keyword != null && !keyword.isEmpty()) {
                userQuery.and(wrapper -> wrapper
                        .like(WsUser::getUsername, keyword)
                        .or()
                        .like(WsUser::getPhoneNumber, keyword));
            }

            return wsUserService.count(userQuery);

        } catch (Exception e) {
            log.error("获取团队成员数量失败", e);
            return 0;
        }
    }



    /**
     * 获取顶级团队ID
     *
     * @param teamId 团队ID
     * @return 顶级团队ID
     */
    @Override
    public String getTopLevelTeamId(String teamId) {
        WsTeam team = this.getById(teamId);
        if (team == null) {
            return teamId;
        }

        // 如果没有父团队，则当前团队就是顶级团队
        if (team.getParentTeamId() == null || team.getParentTeamId().isEmpty()) {
            return teamId;
        }

        // 递归查找顶级团队
        return getTopLevelTeamId(team.getParentTeamId());
    }

    /**
     * 检查用户是否在指定团队树中
     *
     * @param userId 用户ID
     * @param topLevelTeamId 顶级团队ID
     * @return 是否在团队树中
     */
    @Override
    public boolean isUserInTeamTree(String userId, String topLevelTeamId) {
        // 获取顶级团队及其所有子团队
        List<String> teamIds = getAllTeamIdsInTree(topLevelTeamId);

        // 检查用户是否在这些团队中的任何一个
        LambdaQueryWrapper<WsUserTeams> query = new LambdaQueryWrapper<>();
        query.eq(WsUserTeams::getUserId, userId);
        query.in(WsUserTeams::getTeamId, teamIds);
        query.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态

        return wsUserTeamsService.count(query) > 0;
    }

    /**
     * 获取团队树中的所有团队ID（包含当前团队及其所有子团队）
     *
     * @param teamId 团队ID
     * @return 团队ID列表
     */
    @Override
    public List<String> getAllTeamIdsInTree(String teamId) {
        List<String> teamIds = new ArrayList<>();
        teamIds.add(teamId);

        // 递归获取所有子团队
        addChildTeamIds(teamId, teamIds);

        return teamIds;
    }

    /**
     * 递归添加子团队ID
     *
     * @param parentTeamId 父团队ID
     * @param teamIds 团队ID列表
     */
    private void addChildTeamIds(String parentTeamId, List<String> teamIds) {
        LambdaQueryWrapper<WsTeam> query = new LambdaQueryWrapper<>();
        query.eq(WsTeam::getParentTeamId, parentTeamId);
        query.eq(WsTeam::getDelFlag, new BigDecimal(0));

        List<WsTeam> childTeams = this.list(query);
        for (WsTeam childTeam : childTeams) {
            teamIds.add(childTeam.getTeamId());
            // 递归添加子团队的子团队
            addChildTeamIds(childTeam.getTeamId(), teamIds);
        }
    }

    /**
     * 获取用户在团队中的角色
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 角色代码
     */
    @Override
    public String getUserRoleInTeam(String userId, String teamId) {
        LambdaQueryWrapper<WsUserTeams> query = new LambdaQueryWrapper<>();
        query.eq(WsUserTeams::getUserId, userId);
        query.eq(WsUserTeams::getTeamId, teamId);
        query.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态

        WsUserTeams userTeam = wsUserTeamsService.getOne(query);
        if (userTeam == null) {
            return null;
        }

        if(StringUtils.isBlank(userTeam.getRoleId())){
            throw new BusinessException("您不是该团队的管理员，无法创建子团队");
        }

        // 根据角色ID获取角色代码
        WsRole role = wsRoleService.getById(userTeam.getRoleId());
        return role != null ? role.getRoleCode() : null;
    }

    /**
     * 获取用户在团队中的角色
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 角色代码
     */
    @Override
    public String getUserRoleInTeamForNull(String userId, String teamId) {
        LambdaQueryWrapper<WsUserTeams> query = new LambdaQueryWrapper<>();
        query.eq(WsUserTeams::getUserId, userId);
        query.eq(WsUserTeams::getTeamId, teamId);
        query.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态

        WsUserTeams userTeam = wsUserTeamsService.getOne(query);
        if (userTeam == null) {
            return null;
        }

        if(StringUtils.isBlank(userTeam.getRoleId())){
            return null;
        }

        // 根据角色ID获取角色代码
        WsRole role = wsRoleService.getById(userTeam.getRoleId());
        return role != null ? role.getRoleCode() : null;
    }

    /**
     * 添加用户到团队并分配角色
     *
     * @param teamId 团队ID
     * @param userId 用户ID
     * @param roleCode 角色代码
     * @param assignedBy 分配者ID
     */
    private void addUserToTeam(String teamId, String userId, String roleCode, String assignedBy) {
        // 根据角色代码获取角色ID
        String roleId = getRoleIdByCode(roleCode);
        if (roleId == null) {
            log.error("未找到角色代码对应的角色ID: {}", roleCode);
            return;
        }

        WsUserTeams userTeam = new WsUserTeams();
        userTeam.setId(UUID.randomUUID().toString().replace("-", ""));
        userTeam.setUserId(userId);
        userTeam.setTeamId(teamId);
        userTeam.setAssignedDate(new Date());
        userTeam.setAssignedBy(assignedBy);
        userTeam.setStatus(new BigDecimal(1)); // 已加入状态
        userTeam.setRoleId(roleId);

        wsUserTeamsService.save(userTeam);
    }

    /**
     * 根据角色代码获取角色ID
     *
     * @param roleCode 角色代码
     * @return 角色ID
     */
    private String getRoleIdByCode(String roleCode) {
        LambdaQueryWrapper<WsRole> query = new LambdaQueryWrapper<>();
        query.eq(WsRole::getRoleCode, roleCode);

        WsRole role = wsRoleService.getOne(query);
        return role != null ? role.getRoleId() : null;
    }

    /**
     * 获取用户在团队树中当前所在的团队ID
     *
     * @param userId 用户ID
     * @param topLevelTeamId 顶级团队ID
     * @return 当前团队ID
     */
    @Override
    public String getUserCurrentTeamInTree(String userId, String topLevelTeamId) {
        // 获取团队树中的所有团队ID
        List<String> teamIds = getAllTeamIdsInTree(topLevelTeamId);

        // 查找用户在这些团队中的记录
        LambdaQueryWrapper<WsUserTeams> query = new LambdaQueryWrapper<>();
        query.eq(WsUserTeams::getUserId, userId);
        query.in(WsUserTeams::getTeamId, teamIds);
        query.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 已加入状态

        WsUserTeams userTeam = wsUserTeamsService.getOne(query);
        return userTeam != null ? userTeam.getTeamId() : null;
    }

    /**
     * 检查用户是否在指定团队的上级
     *
     * @param userId 用户ID
     * @param teamId 团队ID
     * @return 是否在上级
     */
    @Override
    public boolean isUserInUpperLevel(String userId, String teamId) {
        // 获取用户当前所在的团队
        String topLevelTeamId = getTopLevelTeamId(teamId);
        String userCurrentTeamId = getUserCurrentTeamInTree(userId, topLevelTeamId);

        if (userCurrentTeamId == null) {
            return false;
        }

        // 检查用户所在团队是否是目标团队的上级
        return isParentTeam(userCurrentTeamId, teamId);
    }

    /**
     * 检查团队A是否是团队B的上级团队
     *
     * @param parentTeamId 可能的父团队ID
     * @param childTeamId 子团队ID
     * @return 是否为上级关系
     */
    private boolean isParentTeam(String parentTeamId, String childTeamId) {
        WsTeam childTeam = this.getById(childTeamId);
        if (childTeam == null) {
            return false;
        }

        // 如果子团队没有父团队，则不存在上级关系
        if (childTeam.getParentTeamId() == null || childTeam.getParentTeamId().isEmpty()) {
            return false;
        }

        // 如果直接父团队就是目标团队，则存在上级关系
        if (parentTeamId.equals(childTeam.getParentTeamId())) {
            return true;
        }

        // 递归检查更上级的团队
        return isParentTeam(parentTeamId, childTeam.getParentTeamId());
    }

    /**
     * 同意团队邀请
     *
     * @param inviteId 邀请记录ID
     * @param userId 用户ID
     * @return 处理结果
     */
    @Override
    @Transactional
    public RestResult<Object> acceptInvite(String inviteId, String userId) {
        // 查找邀请记录
        WsUserTeams invite = wsUserTeamsService.getById(inviteId);
        if (invite == null) {
            return RestResult.error("邀请记录不存在");
        }

        // 验证邀请是否属于当前用户
        if (!userId.equals(invite.getUserId())) {
            return RestResult.error("无权处理此邀请");
        }

        // 验证邀请状态
        if (invite.getStatus().compareTo(new BigDecimal(0)) != 0) {
            return RestResult.error("邀请已处理，无法重复操作");
        }

        try {
            // 获取邀请的团队信息
            String teamId = invite.getTeamId();
            String topLevelTeamId = getTopLevelTeamId(teamId);

            // 删除用户在同一团队树中的其他团队记录
            List<String> treeTeamIds = getAllTeamIdsInTree(topLevelTeamId);
            LambdaQueryWrapper<WsUserTeams> deleteQuery = new LambdaQueryWrapper<>();
            deleteQuery.eq(WsUserTeams::getUserId, userId);
            deleteQuery.in(WsUserTeams::getTeamId, treeTeamIds);
            deleteQuery.eq(WsUserTeams::getStatus, new BigDecimal(1)); // 只删除已加入的记录
            deleteQuery.ne(WsUserTeams::getId, inviteId); // 不删除当前邀请记录

            wsUserTeamsService.remove(deleteQuery);

            // 将邀请记录状态设为已加入
            invite.setStatus(new BigDecimal(1));
            invite.setAssignedDate(new Date()); // 更新加入时间
            wsUserTeamsService.updateById(invite);

            return RestResult.success("成功加入团队");

        } catch (Exception e) {
            log.error("处理团队邀请同意失败", e);
            return RestResult.error("处理邀请失败：" + e.getMessage());
        }
    }

    /**
     * 拒绝团队邀请
     *
     * @param inviteId 邀请记录ID
     * @param userId 用户ID
     * @return 处理结果
     */
    @Override
    @Transactional
    public RestResult<Object> rejectInvite(String inviteId, String userId) {
        // 查找邀请记录
        WsUserTeams invite = wsUserTeamsService.getById(inviteId);
        if (invite == null) {
            return RestResult.error("邀请记录不存在");
        }

        // 验证邀请是否属于当前用户
        if (!userId.equals(invite.getUserId())) {
            return RestResult.error("无权处理此邀请");
        }

        // 验证邀请状态
        if (invite.getStatus().compareTo(new BigDecimal(0)) != 0) {
            return RestResult.error("邀请已处理，无法重复操作");
        }

        try {
            // 直接删除邀请记录
            wsUserTeamsService.removeById(inviteId);

            return RestResult.success("已拒绝团队邀请");

        } catch (Exception e) {
            log.error("处理团队邀请拒绝失败", e);
            return RestResult.error("处理邀请失败：" + e.getMessage());
        }
    }
}
