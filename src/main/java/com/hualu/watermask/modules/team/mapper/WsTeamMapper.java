package com.hualu.watermask.modules.team.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hualu.watermask.modules.team.entity.WsTeam;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface WsTeamMapper extends BaseMapper<WsTeam> {
  String getUserRoleInUpTeam(@Param("userId") String userId, @Param("teamId") String teamId);
  WsTeam getUpUserCurrentTeam(@Param("userId") String userId, @Param("teamId") String teamId);

  List<WsUserTeams> getUserDownTreeByTeamId(String teamId);
}