package com.hualu.watermask.modules.team.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 组织团队信息表，支持多层级团队结构
 */
@TableName(value = "WS_TEAM")
public class WsTeam {
    /**
     * 团队唯一标识符，主键
     */
    @TableId(value = "TEAM_ID")
    private String teamId;

    /**
     * 团队名称，唯一标识团队的显示名称
     */
    @TableField(value = "TEAM_NAME")
    private String teamName;

    /**
     * 团队编码，唯一标识团队的编码数字串16位
     */
    @TableField(value = "TEAM_CODE")
    private String teamCode;

    /**
     * 父团队ID，用于构建团队层级关系
     */
    @TableField(value = "PARENT_TEAM_ID")
    private String parentTeamId;

    /**
     * 团队详细描述信息
     */
    @TableField(value = "TEAM_DESCRIPTION")
    private String teamDescription;

    /**
     * 团队创建者用户名
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 团队创建时间
     */
    @TableField(value = "CREATION_DATE")
    private Date creationDate;

    /**
     * 是否删除1删除0未删除
     */
    @TableField(value = "DEL_FLAG")
    private BigDecimal delFlag;

    /**
     * 图标
     */
    @TableField(value = "ICON")
    private String icon;

    @TableField(exist = false)
    private Integer totalPerson;

    @TableField(exist = false)
    private Integer totalPhoto;
    
    /**
     * 是否同步标志（来自WsUserTeams表的IS_SYN字段）
     */
    @TableField(exist = false)
    private BigDecimal isSyn;

    /**
     * 顶级团队名称（非数据库字段，用于接口返回）
     */
    @TableField(exist = false)
    private String topTeamName;

    /**
     * 用户当前所在团队ID（非数据库字段，用于接口返回）
     */
    @TableField(exist = false)
    private String currentTeamId;

    /**
     * 用户在团队中的角色名称（非数据库字段，用于接口返回）
     */
    @TableField(exist = false)
    private String roleName;

    /**
     * 获取团队唯一标识符，主键
     *
     * @return TEAM_ID - 团队唯一标识符，主键
     */
    public String getTeamId() {
        return teamId;
    }

    /**
     * 设置团队唯一标识符，主键
     *
     * @param teamId 团队唯一标识符，主键
     */
    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    /**
     * 获取团队名称，唯一标识团队的显示名称
     *
     * @return TEAM_NAME - 团队名称，唯一标识团队的显示名称
     */
    public String getTeamName() {
        return teamName;
    }

    /**
     * 设置团队名称，唯一标识团队的显示名称
     *
     * @param teamName 团队名称，唯一标识团队的显示名称
     */
    public void setTeamName(String teamName) {
        this.teamName = teamName;
    }

    /**
     * 获取团队编码，唯一标识团队的编码数字串16位
     *
     * @return TEAM_CODE - 团队编码，唯一标识团队的编码数字串16位
     */
    public String getTeamCode() {
        return teamCode;
    }

    /**
     * 设置团队编码，唯一标识团队的编码数字串16位
     *
     * @param teamCode 团队编码，唯一标识团队的编码数字串16位
     */
    public void setTeamCode(String teamCode) {
        this.teamCode = teamCode;
    }

    /**
     * 获取父团队ID，用于构建团队层级关系
     *
     * @return PARENT_TEAM_ID - 父团队ID，用于构建团队层级关系
     */
    public String getParentTeamId() {
        return parentTeamId;
    }

    /**
     * 设置父团队ID，用于构建团队层级关系
     *
     * @param parentTeamId 父团队ID，用于构建团队层级关系
     */
    public void setParentTeamId(String parentTeamId) {
        this.parentTeamId = parentTeamId;
    }

    /**
     * 获取团队详细描述信息
     *
     * @return TEAM_DESCRIPTION - 团队详细描述信息
     */
    public String getTeamDescription() {
        return teamDescription;
    }

    /**
     * 设置团队详细描述信息
     *
     * @param teamDescription 团队详细描述信息
     */
    public void setTeamDescription(String teamDescription) {
        this.teamDescription = teamDescription;
    }

    /**
     * 获取团队创建者用户名
     *
     * @return CREATED_BY - 团队创建者用户名
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 设置团队创建者用户名
     *
     * @param createdBy 团队创建者用户名
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 获取团队创建时间
     *
     * @return CREATION_DATE - 团队创建时间
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     * 设置团队创建时间
     *
     * @param creationDate 团队创建时间
     */
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    /**
     * 获取是否删除1删除0未删除
     *
     * @return DEL_FLAG - 是否删除1删除0未删除
     */
    public BigDecimal getDelFlag() {
        return delFlag;
    }

    /**
     * 设置是否删除1删除0未删除
     *
     * @param delFlag 是否删除1删除0未删除
     */
    public void setDelFlag(BigDecimal delFlag) {
        this.delFlag = delFlag;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public Integer getTotalPerson() {
        return totalPerson;
    }

    public void setTotalPerson(Integer totalPerson) {
        this.totalPerson = totalPerson;
    }

    public Integer getTotalPhoto() {
        return totalPhoto;
    }

    public void setTotalPhoto(Integer totalPhoto) {
        this.totalPhoto = totalPhoto;
    }
    
    public BigDecimal getIsSyn() {
        return isSyn;
    }

    public void setIsSyn(BigDecimal isSyn) {
        this.isSyn = isSyn;
    }

    public String getTopTeamName() {
        return topTeamName;
    }

    public void setTopTeamName(String topTeamName) {
        this.topTeamName = topTeamName;
    }

    public String getCurrentTeamId() {
        return currentTeamId;
    }

    public void setCurrentTeamId(String currentTeamId) {
        this.currentTeamId = currentTeamId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
}