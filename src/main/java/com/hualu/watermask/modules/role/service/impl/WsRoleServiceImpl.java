package com.hualu.watermask.modules.role.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.cache.constants.CacheNameSpace;
import com.hualu.watermask.modules.role.entity.WsRole;
import com.hualu.watermask.modules.role.mapper.WsRoleMapper;
import com.hualu.watermask.modules.role.service.WsRoleService;
import com.hualu.watermask.modules.user.entity.WsUserTeams;
import com.hualu.watermask.modules.user.service.WsUserTeamsService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

@Service
public class WsRoleServiceImpl extends ServiceImpl<WsRoleMapper, WsRole> implements WsRoleService {

  @Resource
  private WsUserTeamsService userTeamsService;

  /**
   * 获取用户在某个团队中的角色
   * @param loginId 用户id
   * @param teamId  团队id
   * @return 角色集合
   */
  @Override
  @Cacheable(
      value = CacheNameSpace.SA_TOKEN_ROLE,
      key = "'roles:' + #loginId + '_' + #teamId",
      unless = "#result == null || #result.isEmpty()"
  )
  public List<String> getRoles(String loginId, String teamId) {
    List<WsUserTeams> userTeams = userTeamsService.lambdaQuery()
        .eq(WsUserTeams::getUserId, loginId)
        .eq(WsUserTeams::getTeamId, teamId)
        .list();
    if (CollUtil.isEmpty(userTeams)) {
      return new ArrayList<>();
    }

    List<String> roleIds = userTeams.stream()
        .map(WsUserTeams::getRoleId)
        .distinct()
        .collect(Collectors.toList());

    return listByIds(roleIds).stream()
        .map(WsRole::getRoleCode)
        .distinct()
        .collect(Collectors.toList());
  }

  /**
   * 修改用户在某个团队中的角色
   * 需要清掉缓存
   * @param loginId 用户id
   * @param teamId  团队id
   * @param roleId  角色id
   * @return
   */
  @Caching(
      evict = {
          // 清除该用户在某团队的角色缓存
          @CacheEvict(
              value = CacheNameSpace.SA_TOKEN_ROLE,
              key = "'roles:' + #loginId + '_' + #teamId"
          ),
          // 清除该用户在某团队的权限缓存
          @CacheEvict(
              value = CacheNameSpace.SA_TOKEN_PERMISSION,
              key = "'permission:' + #loginId + '_' + #teamId"
          )
      }
  )
  @Override public Boolean updateRoles(String loginId, String teamId, String roleId) {
    return userTeamsService.lambdaUpdate()
        .set(WsUserTeams::getRoleId, roleId)
        .set(WsUserTeams::getAssignedDate, new Date())
        .eq(WsUserTeams::getUserId, loginId)
        .eq(WsUserTeams::getTeamId, teamId)
        .eq(WsUserTeams::getStatus, 1)
        .update();
  }

}
