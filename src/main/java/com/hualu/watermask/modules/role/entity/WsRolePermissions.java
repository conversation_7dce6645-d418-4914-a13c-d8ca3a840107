package com.hualu.watermask.modules.role.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

/**
 * 角色与权限关联表，定义角色拥有的权限
 */
@TableName(value = "WS_ROLE_PERMISSIONS")
public class WsRolePermissions {
  @TableId(value = "ID")
  private String id;

  /**
   * 角色ID，外键关联ROLES表
   */
  @TableField(value = "ROLE_ID")
  private String roleId;

  /**
   * 权限ID，外键关联PERMISSIONS表
   */
  @TableField(value = "PERMISSION_ID")
  private String permissionId;

  /**
   * 权限授予时间
   */
  @TableField(value = "GRANTED_DATE")
  private Date grantedDate;

  /**
   * 权限授予者用户名
   */
  @TableField(value = "GRANTED_BY")
  private String grantedBy;

  /**
   * @return ID
   */
  public String getId() {
    return id;
  }

  /**
   * @param id
   */
  public void setId(String id) {
    this.id = id;
  }

  /**
   * 获取角色ID，外键关联ROLES表
   *
   * @return ROLE_ID - 角色ID，外键关联ROLES表
   */
  public String getRoleId() {
    return roleId;
  }

  /**
   * 设置角色ID，外键关联ROLES表
   *
   * @param roleId 角色ID，外键关联ROLES表
   */
  public void setRoleId(String roleId) {
    this.roleId = roleId;
  }

  /**
   * 获取权限ID，外键关联PERMISSIONS表
   *
   * @return PERMISSION_ID - 权限ID，外键关联PERMISSIONS表
   */
  public String getPermissionId() {
    return permissionId;
  }

  /**
   * 设置权限ID，外键关联PERMISSIONS表
   *
   * @param permissionId 权限ID，外键关联PERMISSIONS表
   */
  public void setPermissionId(String permissionId) {
    this.permissionId = permissionId;
  }

  /**
   * 获取权限授予时间
   *
   * @return GRANTED_DATE - 权限授予时间
   */
  public Date getGrantedDate() {
    return grantedDate;
  }

  /**
   * 设置权限授予时间
   *
   * @param grantedDate 权限授予时间
   */
  public void setGrantedDate(Date grantedDate) {
    this.grantedDate = grantedDate;
  }

  /**
   * 获取权限授予者用户名
   *
   * @return GRANTED_BY - 权限授予者用户名
   */
  public String getGrantedBy() {
    return grantedBy;
  }

  /**
   * 设置权限授予者用户名
   *
   * @param grantedBy 权限授予者用户名
   */
  public void setGrantedBy(String grantedBy) {
    this.grantedBy = grantedBy;
  }
}