package com.hualu.watermask.modules.role.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.role.entity.WsRolePermissions;
import com.hualu.watermask.modules.role.service.WsRolePermissionsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 角色权限关联控制器
 */
@RestController
@RequestMapping("/api/role/permissions")
@CrossOrigin
public class WsRolePermissionsController {

    @Autowired
    private WsRolePermissionsService wsRolePermissionsService;

    /**
     * 添加角色权限关联
     *
     * @param rolePermission 角色权限关联信息
     * @return 创建结果
     */
    @PostMapping("/create")
    @OperationLog(businessType = "角色权限管理", operationContent = "添加角色权限关联", operationType = "INSERT")
    public RestResult<WsRolePermissions> create(@RequestBody WsRolePermissions rolePermission) {
        // 设置初始值
        rolePermission.setId(UUID.randomUUID().toString().replace("-", ""));
        rolePermission.setGrantedDate(new Date());

        boolean result = wsRolePermissionsService.save(rolePermission);
        if (result) {
            return RestResult.success(rolePermission, "添加角色权限关联成功");
        } else {
            return RestResult.error("添加角色权限关联失败");
        }
    }

    /**
     * 批量添加角色权限关联
     *
     * @param rolePermissions 角色权限关联列表
     * @return 创建结果
     */
    @PostMapping("/batch/create")
    @OperationLog(businessType = "角色权限管理", operationContent = "批量添加角色权限关联", operationType = "INSERT")
    public RestResult<List<WsRolePermissions>> createBatch(@RequestBody List<WsRolePermissions> rolePermissions) {
        // 设置初始值
        for (WsRolePermissions permission : rolePermissions) {
            permission.setId(UUID.randomUUID().toString().replace("-", ""));
            permission.setGrantedDate(new Date());
        }

        boolean result = wsRolePermissionsService.saveBatch(rolePermissions);
        if (result) {
            return RestResult.success(rolePermissions, "批量添加角色权限关联成功");
        } else {
            return RestResult.error("批量添加角色权限关联失败");
        }
    }

    /**
     * 删除角色权限关联
     *
     * @param id 关联ID
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @OperationLog(businessType = "角色权限管理", operationContent = "删除角色权限关联", operationType = "DELETE")
    public RestResult<Object> delete(@RequestParam String id) {
        boolean result = wsRolePermissionsService.removeById(id);
        if (result) {
            return RestResult.success(null, "删除角色权限关联成功");
        } else {
            return RestResult.error("删除角色权限关联失败，关联可能不存在");
        }
    }

    /**
     * 获取角色的所有权限关联
     *
     * @param roleId 角色ID
     * @return 权限关联列表
     */
    @GetMapping("/getByRoleId")
    @OperationLog(businessType = "角色权限管理", operationContent = "获取角色的所有权限", operationType = "SELECT")
    public RestResult<List<WsRolePermissions>> getByRoleId(@RequestParam String roleId) {
        LambdaQueryWrapper<WsRolePermissions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsRolePermissions::getRoleId, roleId);

        List<WsRolePermissions> permissions = wsRolePermissionsService.list(queryWrapper);
        return RestResult.success(permissions);
    }

    /**
     * 获取指定权限的所有角色关联
     *
     * @param permissionId 权限ID
     * @return 角色关联列表
     */
    @GetMapping("/getByPermissionId")
    @OperationLog(businessType = "角色权限管理", operationContent = "获取权限的所有角色", operationType = "SELECT")
    public RestResult<List<WsRolePermissions>> getByPermissionId(@RequestParam String permissionId) {
        LambdaQueryWrapper<WsRolePermissions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsRolePermissions::getPermissionId, permissionId);

        List<WsRolePermissions> roles = wsRolePermissionsService.list(queryWrapper);
        return RestResult.success(roles);
    }

    /**
     * 删除角色的所有权限关联
     *
     * @param roleId 角色ID
     * @return 删除结果
     */
    @DeleteMapping("/deleteByRoleId")
    @OperationLog(businessType = "角色权限管理", operationContent = "删除角色的所有权限", operationType = "DELETE")
    public RestResult<Object> deleteByRoleId(@RequestParam String roleId) {
        LambdaQueryWrapper<WsRolePermissions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsRolePermissions::getRoleId, roleId);

        boolean result = wsRolePermissionsService.remove(queryWrapper);
        return RestResult.success(null, "删除角色权限关联成功");
    }

    /**
     * 删除权限的所有角色关联
     *
     * @param permissionId 权限ID
     * @return 删除结果
     */
    @DeleteMapping("/deleteByPermissionId")
    @OperationLog(businessType = "角色权限管理", operationContent = "删除权限的所有角色", operationType = "DELETE")
    public RestResult<Object> deleteByPermissionId(@RequestParam String permissionId) {
        LambdaQueryWrapper<WsRolePermissions> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsRolePermissions::getPermissionId, permissionId);

        boolean result = wsRolePermissionsService.remove(queryWrapper);
        return RestResult.success(null, "删除权限角色关联成功");
    }
} 