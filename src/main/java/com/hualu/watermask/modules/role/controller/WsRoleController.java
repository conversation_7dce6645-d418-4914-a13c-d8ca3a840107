package com.hualu.watermask.modules.role.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.role.entity.WsRole;
import com.hualu.watermask.modules.role.service.WsRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 角色管理控制器
 */
@RestController
@RequestMapping("/api/role")
@CrossOrigin
public class WsRoleController {

    @Autowired
    private WsRoleService wsRoleService;

    /**
     * 创建角色
     *
     * @param role 角色信息
     * @return 创建结果
     */
    @PostMapping("/create")
    @OperationLog(businessType = "角色管理", operationContent = "创建角色", operationType = "INSERT")
    public RestResult<WsRole> create(@RequestBody WsRole role) {
        // 设置初始值
        role.setRoleId(UUID.randomUUID().toString().replace("-", ""));
        role.setCreationDate(new Date());
        role.setDelFlag(new BigDecimal(0));

        boolean result = wsRoleService.save(role);
        if (result) {
            return RestResult.success(role, "创建角色成功");
        } else {
            return RestResult.error("创建角色失败");
        }
    }

    /**
     * 更新角色
     *
     * @param roleId 角色ID
     * @param role 更新的角色信息
     * @return 更新结果
     */
    @PutMapping("/update")
    @OperationLog(businessType = "角色管理", operationContent = "更新角色", operationType = "UPDATE")
    public RestResult<WsRole> update(@RequestParam String roleId, @RequestBody WsRole role) {
        role.setRoleId(roleId);

        boolean result = wsRoleService.updateById(role);
        if (result) {
            return RestResult.success(role, "更新角色成功");
        } else {
            return RestResult.error("更新角色失败，角色可能不存在");
        }
    }

    /**
     * 删除角色（逻辑删除）
     *
     * @param roleId 角色ID
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @OperationLog(businessType = "角色管理", operationContent = "删除角色", operationType = "DELETE")
    public RestResult<Object> delete(@RequestParam String roleId) {
        WsRole role = new WsRole();
        role.setRoleId(roleId);
        role.setDelFlag(new BigDecimal(1));

        boolean result = wsRoleService.updateById(role);
        if (result) {
            return RestResult.success(null, "删除角色成功");
        } else {
            return RestResult.error("删除角色失败，角色可能不存在");
        }
    }

    /**
     * 获取角色详情
     *
     * @param roleId 角色ID
     * @return 角色详情
     */
    @GetMapping("/get")
    @OperationLog(businessType = "角色管理", operationContent = "获取角色详情", operationType = "SELECT")
    public RestResult<WsRole> getById(@RequestParam String roleId) {
        WsRole role = wsRoleService.getById(roleId);
        if (role != null && role.getDelFlag().compareTo(new BigDecimal(0)) == 0) {
            return RestResult.success(role);
        } else {
            return RestResult.error("未找到指定的角色或角色已被删除");
        }
    }

    /**
     * 分页查询角色列表
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @param roleName 角色名称（可选，模糊查询）
     * @param roleCode 角色代码（可选，精确查询）
     * @return 分页结果
     */
    @GetMapping("/list")
    @OperationLog(businessType = "角色管理", operationContent = "查询角色列表", operationType = "SELECT")
    public RestResult<List<WsRole>> list(
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestParam(required = false) String roleName,
            @RequestParam(required = false) String roleCode) {

        Page<WsRole> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<WsRole> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq(WsRole::getDelFlag, new BigDecimal(0));

        // 根据条件过滤
        if (roleName != null && !roleName.isEmpty()) {
            queryWrapper.like(WsRole::getRoleName, roleName);
        }

        if (roleCode != null && !roleCode.isEmpty()) {
            queryWrapper.eq(WsRole::getRoleCode, roleCode);
        }

        // 按角色名称排序
        queryWrapper.orderByAsc(WsRole::getRoleName);

        IPage<WsRole> result = wsRoleService.page(pageParam, queryWrapper);
        return RestResult.success(result.getRecords(), result.getTotal(), page, pageSize);
    }

    /**
     * 通过角色代码查询角色
     *
     * @param roleCode 角色代码
     * @return 角色信息
     */
    @GetMapping("/getByCode")
    @OperationLog(businessType = "角色管理", operationContent = "通过代码查询角色", operationType = "SELECT")
    public RestResult<WsRole> getByCode(@RequestParam String roleCode) {
        LambdaQueryWrapper<WsRole> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WsRole::getRoleCode, roleCode);
        queryWrapper.eq(WsRole::getDelFlag, new BigDecimal(0));

        WsRole role = wsRoleService.getOne(queryWrapper);
        if (role != null) {
            return RestResult.success(role);
        } else {
            return RestResult.error("未找到指定代码的角色");
        }
    }
} 