package com.hualu.watermask.modules.role.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.role.entity.WsRolePermissions;
import com.hualu.watermask.modules.role.mapper.WsRolePermissionsMapper;
import com.hualu.watermask.modules.role.service.WsRolePermissionsService;
@Service
public class WsRolePermissionsServiceImpl extends ServiceImpl<WsRolePermissionsMapper, WsRolePermissions> implements WsRolePermissionsService{

}
