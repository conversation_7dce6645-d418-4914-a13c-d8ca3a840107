package com.hualu.watermask.modules.role.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hualu.watermask.modules.role.entity.WsRole;
import java.util.List;

public interface WsRoleService extends IService<WsRole>{

  /**
   * 获取用户在某个团队中的角色
   * @param loginId 用户id
   * @param teamId  团队id
   * @return 角色集合
   */
  List<String> getRoles(String loginId, String teamId);

  /**
   * 修改用户在某个团队中的角色
   * @param loginId 用户id
   * @param teamId  团队id
   * @param roleId  角色id
   * @return
   */
  Boolean updateRoles(String loginId, String teamId, String roleId);

}
