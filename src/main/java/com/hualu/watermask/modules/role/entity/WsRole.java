package com.hualu.watermask.modules.role.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 系统角色定义表，定义不同权限集合
 */
@TableName(value = "WS_ROLE")
public class WsRole {
    /**
     * 角色唯一标识符，主键
     */
    @TableId(value = "ROLE_ID")
    private String roleId;

    /**
     * 角色代码（ADMIN, DEV, INTERN等），唯一且不可变
     */
    @TableField(value = "ROLE_CODE")
    private String roleCode;

    /**
     * 角色显示名称
     */
    @TableField(value = "ROLE_NAME")
    private String roleName;

    /**
     * 角色详细描述信息
     */
    @TableField(value = "ROLE_DESCRIPTION")
    private String roleDescription;

    /**
     * 角色创建者用户名
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 角色创建时间
     */
    @TableField(value = "CREATION_DATE")
    private Date creationDate;

    /**
     * 是否删除1删除0未删除
     */
    @TableField(value = "DEL_FLAG")
    private BigDecimal delFlag;

    /**
     * 获取角色唯一标识符，主键
     *
     * @return ROLE_ID - 角色唯一标识符，主键
     */
    public String getRoleId() {
        return roleId;
    }

    /**
     * 设置角色唯一标识符，主键
     *
     * @param roleId 角色唯一标识符，主键
     */
    public void setRoleId(String roleId) {
        this.roleId = roleId;
    }

    /**
     * 获取角色代码（ADMIN, DEV, INTERN等），唯一且不可变
     *
     * @return ROLE_CODE - 角色代码（ADMIN, DEV, INTERN等），唯一且不可变
     */
    public String getRoleCode() {
        return roleCode;
    }

    /**
     * 设置角色代码（ADMIN, DEV, INTERN等），唯一且不可变
     *
     * @param roleCode 角色代码（ADMIN, DEV, INTERN等），唯一且不可变
     */
    public void setRoleCode(String roleCode) {
        this.roleCode = roleCode;
    }

    /**
     * 获取角色显示名称
     *
     * @return ROLE_NAME - 角色显示名称
     */
    public String getRoleName() {
        return roleName;
    }

    /**
     * 设置角色显示名称
     *
     * @param roleName 角色显示名称
     */
    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    /**
     * 获取角色详细描述信息
     *
     * @return ROLE_DESCRIPTION - 角色详细描述信息
     */
    public String getRoleDescription() {
        return roleDescription;
    }

    /**
     * 设置角色详细描述信息
     *
     * @param roleDescription 角色详细描述信息
     */
    public void setRoleDescription(String roleDescription) {
        this.roleDescription = roleDescription;
    }

    /**
     * 获取角色创建者用户名
     *
     * @return CREATED_BY - 角色创建者用户名
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 设置角色创建者用户名
     *
     * @param createdBy 角色创建者用户名
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 获取角色创建时间
     *
     * @return CREATION_DATE - 角色创建时间
     */
    public Date getCreationDate() {
        return creationDate;
    }

    /**
     * 设置角色创建时间
     *
     * @param creationDate 角色创建时间
     */
    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    /**
     * 获取是否删除1删除0未删除
     *
     * @return DEL_FLAG - 是否删除1删除0未删除
     */
    public BigDecimal getDelFlag() {
        return delFlag;
    }

    /**
     * 设置是否删除1删除0未删除
     *
     * @param delFlag 是否删除1删除0未删除
     */
    public void setDelFlag(BigDecimal delFlag) {
        this.delFlag = delFlag;
    }
}