package com.hualu.watermask.modules.record.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.record.entity.WsExportRecord;
import com.hualu.watermask.modules.record.service.WsExportRecordService;
import com.hualu.watermask.modules.record.mapper.WsExportRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;

/**
* <AUTHOR>
* @description 针对表【WS_EXPORT_RECORD(导出记录表)】的数据库操作Service实现
* @createDate 2025-07-25 15:58:21
*/
@Service
public class WsExportRecordServiceImpl extends ServiceImpl<WsExportRecordMapper, WsExportRecord>
    implements WsExportRecordService{

    private static final Logger log = LoggerFactory.getLogger(WsExportRecordServiceImpl.class);

    /**
     * 通用下载导出文件
     *
     * @param response HTTP响应对象
     * @param recordId 导出记录ID
     * @param userId 用户ID
     */
    @Override
    public void downloadExportedFile(HttpServletResponse response, String recordId, String userId) {
        try {
            // 获取记录信息
            WsExportRecord record = this.getById(recordId);
            if (record == null) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"导出记录不存在\"}");
                return;
            }

            // 验证是否为记录创建者
            if (!userId.equals(record.getCreatedBy())) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":403,\"message\":\"您无权下载该文件\"}");
                return;
            }

            // 检查文件是否存在
            String filePath = record.getLocalPath();
            if (filePath == null || filePath.isEmpty()) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"文件路径不存在\"}");
                return;
            }

            java.io.File file = new java.io.File(filePath);
            if (!file.exists()) {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"文件不存在\"}");
                return;
            }

            // 根据文件扩展名设置Content-Type
            String fileName = record.getFileName();
            String contentType = getContentType(fileName);
            response.setContentType(contentType);
            response.setCharacterEncoding("utf-8");

            // 设置文件下载头
            String encodedFileName = java.net.URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + encodedFileName);
            response.setHeader("Content-Length", String.valueOf(file.length()));

            // 输出文件
            try (FileInputStream fileIn = new FileInputStream(file);
                 OutputStream out = response.getOutputStream()) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = fileIn.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
                out.flush();
            }

            log.info("文件下载成功: {} (类型: {})", record.getFileName(), record.getFileType());

        } catch (Exception e) {
            log.error("下载文件失败", e);
            try {
                response.setContentType("application/json");
                response.setCharacterEncoding("utf-8");
                response.getWriter().write("{\"code\":500,\"message\":\"下载文件失败: " + e.getMessage() + "\"}");
            } catch (IOException ex) {
                log.error("设置错误响应失败", ex);
            }
        }
    }

    /**
     * 根据文件名获取Content-Type
     *
     * @param fileName 文件名
     * @return Content-Type
     */
    private String getContentType(String fileName) {
        if (fileName == null) {
            return "application/octet-stream";
        }

        String lowerFileName = fileName.toLowerCase();
        if (lowerFileName.endsWith(".xlsx")) {
            return "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
        } else if (lowerFileName.endsWith(".xls")) {
            return "application/vnd.ms-excel";
        } else if (lowerFileName.endsWith(".zip")) {
            return "application/zip";
        } else if (lowerFileName.endsWith(".pdf")) {
            return "application/pdf";
        } else {
            return "application/octet-stream";
        }
    }
}




