package com.hualu.watermask.modules.record.service;

import com.hualu.watermask.modules.record.entity.WsExportRecord;
import com.baomidou.mybatisplus.extension.service.IService;
import javax.servlet.http.HttpServletResponse;

/**
* <AUTHOR>
* @description 针对表【WS_EXPORT_RECORD(导出记录表)】的数据库操作Service
* @createDate 2025-07-25 15:58:21
*/
public interface WsExportRecordService extends IService<WsExportRecord> {

    /**
     * 通用下载导出文件
     *
     * @param response HTTP响应对象
     * @param recordId 导出记录ID
     * @param userId 用户ID
     */
    void downloadExportedFile(HttpServletResponse response, String recordId, String userId);
}
