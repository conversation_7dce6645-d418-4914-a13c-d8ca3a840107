package com.hualu.watermask.modules.record.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.record.entity.WsExportRecord;
import com.hualu.watermask.modules.record.service.WsExportRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.stp.StpUtil;

import javax.servlet.http.HttpServletResponse;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 导出记录控制器
 */
@RestController
@RequestMapping("/api/record/export")
@CrossOrigin
public class WsExportRecordController {

    @Autowired
    private WsExportRecordService wsExportRecordService;

    /**
     * 分页查询下载记录列表
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @param fileType 文件类型（可选，精确查询）
     * @param fileName 文件名称（可选，模糊查询）
     * @param startDate 开始日期（可选，格式：yyyy-MM-dd）
     * @param endDate 结束日期（可选，格式：yyyy-MM-dd）
     * @return 分页结果
     */
    @GetMapping("/list")
    @OperationLog(businessType = "记录管理", operationContent = "查询下载记录", operationType = "SELECT")
    public RestResult<List<WsExportRecord>> list(
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestParam(required = false) String fileType,
            @RequestParam(required = false) String fileName,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        // 获取当前登录用户ID，默认只查看自己的下载记录
        String currentUserId = StpUtil.getLoginIdAsString();

        Page<WsExportRecord> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<WsExportRecord> queryWrapper = new LambdaQueryWrapper<>();

        // 只查询未删除的记录
        queryWrapper.eq(WsExportRecord::getIsDeleted, 0L);
        
        // 根据创建人筛选
        queryWrapper.eq(WsExportRecord::getCreatedBy, currentUserId);

        // 根据条件过滤
        if (fileType != null && !fileType.isEmpty()) {
            queryWrapper.eq(WsExportRecord::getFileType, fileType);
        }

        if (fileName != null && !fileName.isEmpty()) {
            queryWrapper.like(WsExportRecord::getFileName, fileName);
        }

        // 处理日期查询
        Date start = null;
        Date end = null;
        
        if (startDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            start = calendar.getTime();
        }
        
        if (endDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            end = calendar.getTime();
        }
        
        // 应用日期过滤条件
        if (start != null && end != null) {
            queryWrapper.between(WsExportRecord::getApplyTime, start, end);
        } else if (start != null) {
            queryWrapper.ge(WsExportRecord::getApplyTime, start);
        } else if (end != null) {
            queryWrapper.le(WsExportRecord::getApplyTime, end);
        }

        // 按申请时间降序排序（最新的记录排在前面）
        queryWrapper.orderByDesc(WsExportRecord::getApplyTime);

        IPage<WsExportRecord> result = wsExportRecordService.page(pageParam, queryWrapper);
        return RestResult.success(result.getRecords(), result.getTotal(), page, pageSize);
    }
    
    /**
     * 获取所有文件类型（去重）
     * 
     * @return 文件类型列表
     */
    @GetMapping("/file-types")
    @OperationLog(businessType = "记录管理", operationContent = "获取文件类型", operationType = "SELECT")
    public RestResult<List<String>> getFileTypes() {
        // 获取当前登录用户ID
        String currentUserId = StpUtil.getLoginIdAsString();
        
        // 构建查询条件
        LambdaQueryWrapper<WsExportRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(WsExportRecord::getFileType)
                .eq(WsExportRecord::getIsDeleted, 0L)
                .eq(WsExportRecord::getCreatedBy, currentUserId)
                .groupBy(WsExportRecord::getFileType);
                
        // 执行查询
        List<WsExportRecord> records = wsExportRecordService.list(queryWrapper);
        
        // 提取文件类型
        List<String> fileTypes = records.stream()
                .map(WsExportRecord::getFileType)
                .filter(type -> type != null && !type.isEmpty())
                .distinct()
                .collect(java.util.stream.Collectors.toList());
                
        return RestResult.success(fileTypes);
    }

    /**
     * 通用下载导出文件接口
     *
     * @param response HTTP响应对象
     * @param recordId 导出记录ID
     */
    @GetMapping("/download")
    @OperationLog(businessType = "记录管理", operationContent = "下载导出文件", operationType = "SELECT")
    public void downloadExportedFile(
            HttpServletResponse response,
            @RequestParam String recordId) {
        // 获取当前登录用户ID
        String userId = StpUtil.getLoginIdAsString();
        wsExportRecordService.downloadExportedFile(response, recordId, userId);
    }
}