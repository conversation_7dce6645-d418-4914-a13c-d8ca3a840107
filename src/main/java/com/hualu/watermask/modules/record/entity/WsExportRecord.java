package com.hualu.watermask.modules.record.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;

/**
 * 导出记录表
 * @TableName WS_EXPORT_RECORD
 */
@TableName(value ="WS_EXPORT_RECORD")
public class WsExportRecord {
    /**
     * 主键UUID
     */
    @TableId(value = "ID")
    private String id;

    /**
     * 申请时间
     */
    @TableField(value = "APPLY_TIME")
    private Date applyTime;

    /**
     * 下载数量
     */
    @TableField(value = "DOWNLOAD_COUNT")
    private Long downloadCount;

    /**
     * 成员数量
     */
    @TableField(value = "MEMBER_COUNT")
    private Long memberCount;

    /**
     * 时间范围
     */
    @TableField(value = "TIME_RANGE")
    private String timeRange;

    /**
     * 文件名
     */
    @TableField(value = "FILE_NAME")
    private String fileName;

    /**
     * 文件类型
     */
    @TableField(value = "FILE_TYPE")
    private String fileType;

    /**
     * 是否已经删除
     */
    @TableField(value = "IS_DELETED")
    private Long isDeleted;

    /**
     * 状态
     */
    @TableField(value = "STATUS")
    private Long status;

    /**
     * 创建人
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED_DATE")
    private Date createdDate;

    /**
     * 本地文件目录
     */
    @TableField(value = "LOCAL_PATH")
    private String localPath;

    /**
     * 主键UUID
     */
    public String getId() {
        return id;
    }

    /**
     * 主键UUID
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 申请时间
     */
    public Date getApplyTime() {
        return applyTime;
    }

    /**
     * 申请时间
     */
    public void setApplyTime(Date applyTime) {
        this.applyTime = applyTime;
    }

    /**
     * 下载次数
     */
    public Long getDownloadCount() {
        return downloadCount;
    }

    /**
     * 下载数量
     */
    public void setDownloadCount(Long downloadCount) {
        this.downloadCount = downloadCount;
    }

    /**
     * 成员数量
     */
    public Long getMemberCount() {
        return memberCount;
    }

    /**
     * 成员数量
     */
    public void setMemberCount(Long memberCount) {
        this.memberCount = memberCount;
    }

    /**
     * 时间范围
     */
    public String getTimeRange() {
        return timeRange;
    }

    /**
     * 时间范围
     */
    public void setTimeRange(String timeRange) {
        this.timeRange = timeRange;
    }

    /**
     * 文件名
     */
    public String getFileName() {
        return fileName;
    }

    /**
     * 文件名
     */
    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 文件类型
     */
    public String getFileType() {
        return fileType;
    }

    /**
     * 文件类型
     */
    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    /**
     * 是否已经删除
     */
    public Long getIsDeleted() {
        return isDeleted;
    }

    /**
     * 是否已经删除
     */
    public void setIsDeleted(Long isDeleted) {
        this.isDeleted = isDeleted;
    }

    /**
     * 状态
     */
    public Long getStatus() {
        return status;
    }

    /**
     * 状态
     */
    public void setStatus(Long status) {
        this.status = status;
    }

    /**
     * 创建人
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 创建人
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 创建时间
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * 创建时间
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * 本地文件目录
     */
    public String getLocalPath() {
        return localPath;
    }

    /**
     * 本地文件目录
     */
    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        WsExportRecord other = (WsExportRecord) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getApplyTime() == null ? other.getApplyTime() == null : this.getApplyTime().equals(other.getApplyTime()))
            && (this.getDownloadCount() == null ? other.getDownloadCount() == null : this.getDownloadCount().equals(other.getDownloadCount()))
            && (this.getMemberCount() == null ? other.getMemberCount() == null : this.getMemberCount().equals(other.getMemberCount()))
            && (this.getTimeRange() == null ? other.getTimeRange() == null : this.getTimeRange().equals(other.getTimeRange()))
            && (this.getFileName() == null ? other.getFileName() == null : this.getFileName().equals(other.getFileName()))
            && (this.getFileType() == null ? other.getFileType() == null : this.getFileType().equals(other.getFileType()))
            && (this.getIsDeleted() == null ? other.getIsDeleted() == null : this.getIsDeleted().equals(other.getIsDeleted()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedDate() == null ? other.getCreatedDate() == null : this.getCreatedDate().equals(other.getCreatedDate()))
            && (this.getLocalPath() == null ? other.getLocalPath() == null : this.getLocalPath().equals(other.getLocalPath()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getApplyTime() == null) ? 0 : getApplyTime().hashCode());
        result = prime * result + ((getDownloadCount() == null) ? 0 : getDownloadCount().hashCode());
        result = prime * result + ((getMemberCount() == null) ? 0 : getMemberCount().hashCode());
        result = prime * result + ((getTimeRange() == null) ? 0 : getTimeRange().hashCode());
        result = prime * result + ((getFileName() == null) ? 0 : getFileName().hashCode());
        result = prime * result + ((getFileType() == null) ? 0 : getFileType().hashCode());
        result = prime * result + ((getIsDeleted() == null) ? 0 : getIsDeleted().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedDate() == null) ? 0 : getCreatedDate().hashCode());
        result = prime * result + ((getLocalPath() == null) ? 0 : getLocalPath().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", applyTime=").append(applyTime);
        sb.append(", downloadCount=").append(downloadCount);
        sb.append(", memberCount=").append(memberCount);
        sb.append(", timeRange=").append(timeRange);
        sb.append(", fileName=").append(fileName);
        sb.append(", fileType=").append(fileType);
        sb.append(", isDeleted=").append(isDeleted);
        sb.append(", status=").append(status);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", localPath=").append(localPath);
        sb.append("]");
        return sb.toString();
    }
}