package com.hualu.watermask.modules.log.aspect;

import cn.dev33.satoken.stp.StpUtil;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.log.entity.WsOperationLog;
import com.hualu.watermask.modules.log.service.WsOperationLogService;
import com.hualu.watermask.modules.user.entity.WsUser;
import com.hualu.watermask.modules.user.service.WsUserService;
import com.hualu.watermask.modules.util.RequestContextUtil;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.Date;
import java.util.UUID;

/**
 * 操作日志切面，用于记录用户操作日志
 */
@Aspect
@Component
public class OperationLogAspect {

    private static final Logger logger = LoggerFactory.getLogger(OperationLogAspect.class);

    @Autowired
    private WsOperationLogService operationLogService;
    
    @Autowired
    private WsUserService userService;

    /**
     * 定义切点 - 所有标记了OperationLog注解的方法
     */
    @Pointcut("@annotation(com.hualu.watermask.modules.log.annotation.OperationLog)")
    public void operationLogPointCut() {
    }

    /**
     * 方法正常返回后执行的操作
     *
     * @param joinPoint 连接点
     * @param result    返回结果
     */
    @AfterReturning(value = "operationLogPointCut()", returning = "result")
    public void doAfterReturning(JoinPoint joinPoint, Object result) {
        handleLog(joinPoint, result, null);
    }

    /**
     * 方法抛出异常后执行的操作
     *
     * @param joinPoint 连接点
     * @param e         异常
     */
    @AfterThrowing(value = "operationLogPointCut()", throwing = "e")
    public void doAfterThrowing(JoinPoint joinPoint, Exception e) {
        handleLog(joinPoint, null, e);
    }

    /**
     * 处理日志记录
     *
     * @param joinPoint 连接点
     * @param result    返回结果
     * @param e         异常信息
     */
    private void handleLog(JoinPoint joinPoint, Object result, Exception e) {
        try {
            // 获取注解信息
            MethodSignature signature = (MethodSignature) joinPoint.getSignature();
            Method method = signature.getMethod();
            OperationLog operationLog = method.getAnnotation(OperationLog.class);
            
            if (operationLog == null) {
                return;
            }

            // 创建日志对象
            WsOperationLog wsLog = new WsOperationLog();
            wsLog.setId(UUID.randomUUID().toString().replace("-", ""));
            wsLog.setOperationTime(new Date());
            
            // 设置业务类型和操作内容
            wsLog.setBusinessType(operationLog.businessType());
            wsLog.setOperationContent(operationLog.operationContent());
            wsLog.setOperationType(operationLog.operationType());
            
            // 设置操作状态（是否成功）
            wsLog.setStatus(e == null ? new BigDecimal(1) : new BigDecimal(0));
            
            // 获取请求信息
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (attributes != null) {
                HttpServletRequest request = attributes.getRequest();
                // 设置IP地址
                wsLog.setIpAddress(request.getRemoteAddr());
            }
            
            // 设置操作人信息
            try {
                String userId = StpUtil.getLoginIdAsString();
                WsUser user = userService.getById(userId);
                if (user != null) {
                    wsLog.setOperatorCode(user.getUserId());
                    wsLog.setOperatorName(user.getUsername());
                }
            } catch (Exception ex) {
                logger.error("获取操作用户信息失败", ex);
            }
            
            // 保存日志
            operationLogService.save(wsLog);
        } catch (Exception exception) {
            logger.error("记录操作日志失败", exception);
        }
    }
} 