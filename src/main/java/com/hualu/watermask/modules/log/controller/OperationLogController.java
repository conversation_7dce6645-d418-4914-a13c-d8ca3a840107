package com.hualu.watermask.modules.log.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.log.entity.WsOperationLog;
import com.hualu.watermask.modules.log.service.WsOperationLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * 操作日志控制器
 */
@RestController
@RequestMapping("/api/log")
@CrossOrigin
public class OperationLogController {

    @Autowired
    private WsOperationLogService wsOperationLogService;

    /**
     * 分页查询操作日志列表
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @param operatorName 操作人姓名（可选，模糊查询）
     * @param operatorCode 操作人编码（可选，精确查询）
     * @param businessType 业务类型（可选，精确查询）
     * @param operationType 操作类型（可选，精确查询）
     * @param startDate 开始日期（可选，格式：yyyy-MM-dd）
     * @param endDate 结束日期（可选，格式：yyyy-MM-dd）
     * @return 分页结果
     */
    @GetMapping("/list")
    @OperationLog(businessType = "日志管理", operationContent = "查询操作日志", operationType = "SELECT")
    public RestResult<List<WsOperationLog>> list(
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestParam(required = false) String operatorName,
            @RequestParam(required = false) String operatorCode,
            @RequestParam(required = false) String businessType,
            @RequestParam(required = false) String operationType,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date startDate,
            @RequestParam(required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        Page<WsOperationLog> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<WsOperationLog> queryWrapper = new LambdaQueryWrapper<>();

        // 根据条件过滤
        if (operatorName != null && !operatorName.isEmpty()) {
            queryWrapper.like(WsOperationLog::getOperatorName, operatorName);
        }

        if (operatorCode != null && !operatorCode.isEmpty()) {
            queryWrapper.eq(WsOperationLog::getOperatorCode, operatorCode);
        }

        if (businessType != null && !businessType.isEmpty()) {
            queryWrapper.eq(WsOperationLog::getBusinessType, businessType);
        }

        if (operationType != null && !operationType.isEmpty()) {
            queryWrapper.eq(WsOperationLog::getOperationType, operationType);
        }

        // 处理日期查询（优先使用时间精确到秒的参数）
        Date start = null;
        Date end = null;
        
        // 确定结束时间（优先使用endTime）
        if (startDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(startDate);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            start = calendar.getTime();
        }
        if (endDate != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(endDate);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            end = calendar.getTime();
        }
        
        // 应用日期过滤条件
        if (start != null && end != null) {
            queryWrapper.between(WsOperationLog::getOperationTime, start, end);
        } else if (start != null) {
            queryWrapper.ge(WsOperationLog::getOperationTime, start);
        } else if (end != null) {
            queryWrapper.le(WsOperationLog::getOperationTime, end);
        }

        // 按操作时间降序排序（最新的记录排在前面）
        queryWrapper.orderByDesc(WsOperationLog::getOperationTime);

        IPage<WsOperationLog> result = wsOperationLogService.page(pageParam, queryWrapper);
        return RestResult.success(result.getRecords(), result.getTotal(), page, pageSize);
    }
} 