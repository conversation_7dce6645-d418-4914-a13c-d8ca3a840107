package com.hualu.watermask.modules.log.annotation;

import java.lang.annotation.*;

/**
 * 操作日志注解，用于标记需要记录日志的方法
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface OperationLog {

    /**
     * 业务类型
     */
    String businessType() default "";

    /**
     * 操作内容描述
     */
    String operationContent() default "";

    /**
     * 操作类型（INSERT, UPDATE, DELETE, SELECT）
     */
    String operationType() default "";
} 