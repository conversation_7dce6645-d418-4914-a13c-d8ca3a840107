package com.hualu.watermask.modules.log.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 系统操作日志表 - 记录用户关键操作行为
 */
@TableName(value = "PMSDB.WS_OPERATION_LOG")
public class WsOperationLog {
    /**
     * 主键ID（自增序列），唯一标识操作记录
     */
    @TableId(value = "ID", type = IdType.NONE)
    private String id;

    /**
     * 操作时间（精确到秒），示例：2025-07-14 22:50:39
     */
    @TableField(value = "OPERATION_TIME")
    private Date operationTime;

    /**
     * 操作人姓名（如截图中的"唐平安"）
     */
    @TableField(value = "OPERATOR_NAME")
    private String operatorName;

    /**
     * 操作人唯一编码（如工号/账号），用于关联用户表
     */
    @TableField(value = "OPERATOR_CODE")
    private String operatorCode;

    /**
     * 业务分类（如截图中的"水印次数操作"、会员管理等）
     */
    @TableField(value = "BUSINESS_TYPE")
    private String businessType;

    /**
     * 操作描述（如"用户修改订单状态"、"删除账号"等）
     */
    @TableField(value = "OPERATION_CONTENT")
    private String operationContent;

    /**
     * 变更详情（记录修改前后的差异数据，JSON格式）
     */
    @TableField(value = "CHANGE_CONTENT")
    private String changeContent;

    /**
     * 操作类型（CRUD分类）：INSERT-新增/UPDATE-修改/DELETE-删除
     */
    @TableField(value = "OPERATION_TYPE")
    private String operationType;

    /**
     * 操作终端IP地址（记录来源设备）
     */
    @TableField(value = "IP_ADDRESS")
    private String ipAddress;

    /**
     * 执行状态：操作状态（成功1/失败0）
     */
    @TableField(value = "\"STATUS\"")
    private BigDecimal status;

    /**
     * 获取主键ID（自增序列），唯一标识操作记录
     *
     * @return ID - 主键ID（自增序列），唯一标识操作记录
     */
    public String getId() {
        return id;
    }

    /**
     * 设置主键ID（自增序列），唯一标识操作记录
     *
     * @param id 主键ID（自增序列），唯一标识操作记录
     */
    public void setId(String id) {
        this.id = id;
    }

    /**
     * 获取操作时间（精确到秒），示例：2025-07-14 22:50:39
     *
     * @return OPERATION_TIME - 操作时间（精确到秒），示例：2025-07-14 22:50:39
     */
    public Date getOperationTime() {
        return operationTime;
    }

    /**
     * 设置操作时间（精确到秒），示例：2025-07-14 22:50:39
     *
     * @param operationTime 操作时间（精确到秒），示例：2025-07-14 22:50:39
     */
    public void setOperationTime(Date operationTime) {
        this.operationTime = operationTime;
    }

    /**
     * 获取操作人姓名（如截图中的"唐平安"）
     *
     * @return OPERATOR_NAME - 操作人姓名（如截图中的"唐平安"）
     */
    public String getOperatorName() {
        return operatorName;
    }

    /**
     * 设置操作人姓名（如截图中的"唐平安"）
     *
     * @param operatorName 操作人姓名（如截图中的"唐平安"）
     */
    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    /**
     * 获取操作人唯一编码（如工号/账号），用于关联用户表
     *
     * @return OPERATOR_CODE - 操作人唯一编码（如工号/账号），用于关联用户表
     */
    public String getOperatorCode() {
        return operatorCode;
    }

    /**
     * 设置操作人唯一编码（如工号/账号），用于关联用户表
     *
     * @param operatorCode 操作人唯一编码（如工号/账号），用于关联用户表
     */
    public void setOperatorCode(String operatorCode) {
        this.operatorCode = operatorCode;
    }

    /**
     * 获取业务分类（如截图中的"水印次数操作"、会员管理等）
     *
     * @return BUSINESS_TYPE - 业务分类（如截图中的"水印次数操作"、会员管理等）
     */
    public String getBusinessType() {
        return businessType;
    }

    /**
     * 设置业务分类（如截图中的"水印次数操作"、会员管理等）
     *
     * @param businessType 业务分类（如截图中的"水印次数操作"、会员管理等）
     */
    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    /**
     * 获取操作描述（如"用户修改订单状态"、"删除账号"等）
     *
     * @return OPERATION_CONTENT - 操作描述（如"用户修改订单状态"、"删除账号"等）
     */
    public String getOperationContent() {
        return operationContent;
    }

    /**
     * 设置操作描述（如"用户修改订单状态"、"删除账号"等）
     *
     * @param operationContent 操作描述（如"用户修改订单状态"、"删除账号"等）
     */
    public void setOperationContent(String operationContent) {
        this.operationContent = operationContent;
    }

    /**
     * 获取变更详情（记录修改前后的差异数据，JSON格式）
     *
     * @return CHANGE_CONTENT - 变更详情（记录修改前后的差异数据，JSON格式）
     */
    public String getChangeContent() {
        return changeContent;
    }

    /**
     * 设置变更详情（记录修改前后的差异数据，JSON格式）
     *
     * @param changeContent 变更详情（记录修改前后的差异数据，JSON格式）
     */
    public void setChangeContent(String changeContent) {
        this.changeContent = changeContent;
    }

    /**
     * 获取操作类型（CRUD分类）：INSERT-新增/UPDATE-修改/DELETE-删除
     *
     * @return OPERATION_TYPE - 操作类型（CRUD分类）：INSERT-新增/UPDATE-修改/DELETE-删除
     */
    public String getOperationType() {
        return operationType;
    }

    /**
     * 设置操作类型（CRUD分类）：INSERT-新增/UPDATE-修改/DELETE-删除
     *
     * @param operationType 操作类型（CRUD分类）：INSERT-新增/UPDATE-修改/DELETE-删除
     */
    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    /**
     * 获取操作终端IP地址（记录来源设备）
     *
     * @return IP_ADDRESS - 操作终端IP地址（记录来源设备）
     */
    public String getIpAddress() {
        return ipAddress;
    }

    /**
     * 设置操作终端IP地址（记录来源设备）
     *
     * @param ipAddress 操作终端IP地址（记录来源设备）
     */
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }

    /**
     * 获取执行状态：操作状态（成功1/失败0）
     *
     * @return STATUS - 执行状态：操作状态（成功1/失败0）
     */
    public BigDecimal getStatus() {
        return status;
    }

    /**
     * 设置执行状态：操作状态（成功1/失败0）
     *
     * @param status 执行状态：操作状态（成功1/失败0）
     */
    public void setStatus(BigDecimal status) {
        this.status = status;
    }
}