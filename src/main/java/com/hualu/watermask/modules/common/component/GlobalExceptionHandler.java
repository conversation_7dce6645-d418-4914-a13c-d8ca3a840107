package com.hualu.watermask.modules.common.component;

import cn.dev33.satoken.exception.NotLoginException;
import com.hualu.watermask.modules.common.vo.RestResult;
import javax.servlet.http.HttpServletResponse;
import org.springframework.validation.BindException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import static com.hualu.watermask.modules.common.vo.RestResult.OAUTH_FAIL;

/**
 * 全局异常处理
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

  /**
   * 全局异常捕捉处理
   *
   * @param e 异常信息
   * @return 接口统一返回对象
   */
  @ExceptionHandler(Exception.class)
  public RestResult<Object> globalExceptionHandler(Exception e) {
    e.printStackTrace();
    return RestResult.error(e.getMessage());
  }

  @ExceptionHandler(BindException.class)
  public RestResult<Object> globalExceptionHandler(BindException e) {
    e.printStackTrace();
    return RestResult.error(e.getMessage());
  }

  @ExceptionHandler(NotLoginException.class)
  public RestResult<Object> notLoginExceptionHandler(NotLoginException e, HttpServletResponse response) {
    e.printStackTrace();
    response.setHeader("user_not_auth", "0");
    return RestResult.error(OAUTH_FAIL, "用户未登录", null);
  }
}
