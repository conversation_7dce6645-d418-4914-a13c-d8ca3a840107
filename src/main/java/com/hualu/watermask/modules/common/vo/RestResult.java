package com.hualu.watermask.modules.common.vo;

/**
 * <p>
 * restapi返回实体封装类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-10 10:54
 */
public class RestResult<T> {

  /**
   * 返回状态值1表示成功，其他失败
   */
  private int code;

  /**
   * 提示消息
   */
  private String message;

  /**
   * 返回数据体
   */
  private T data;

  /**
   * 总数
   */
  private long total;

  /**
   * 当前页
   */
  private long page;

  /**
   * 每页个数
   */
  private long pageSize;

  private Object extra;

  public Object getExtra() {
    return extra;
  }

  public void setExtra(Object extra) {
    this.extra = extra;
  }

  public long getPageSize() {
    return pageSize;
  }

  public void setPageSize(long pageSize) {
    this.pageSize = pageSize;
  }

  public long getTotal() {
    return total;
  }

  public void setTotal(long total) {
    this.total = total;
  }

  public long getPage() {
    return page;
  }

  public void setPage(long page) {
    this.page = page;
  }

  public int getCode() {
    return code;
  }

  public void setCode(int code) {
    this.code = code;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public T getData() {
    return data;
  }

  public void setData(T data) {
    this.data = data;
  }

  public static final int SUCCESS = 1;
  public static final int ERROR = 0;
  public static final int WARN = -1;
  public static final int OAUTH_FAIL = -2;

  public RestResult() {
  }

  public RestResult(int code) {
    this.code = code;
  }

  public RestResult(int code, String message, T data) {
    this.code = code;
    this.message = message;
    this.data = data;
  }

  public RestResult(int code, String message, T data, long total, long page, long pageSize) {
    this.code = code;
    this.message = message;
    this.data = data;
    this.total = total;
    this.page = page;
    this.pageSize = pageSize;
  }


  public static <T> RestResult<T> success(T data) {
    return success(data, "");
  }

  public static <T> RestResult<T> success(String message) {
    return new RestResult<>(SUCCESS, message, null);
  }

  public static <T> RestResult<T> success(T data, long total, long page, long pageSize) {
    return new RestResult<>(SUCCESS, "", data, total, page, pageSize);
  }

  public static <T> RestResult<T> success(T data, String message) {
    return new RestResult<>(SUCCESS, message, data);
  }

  public static <T> RestResult<T> error(String message) {
    return error(null, message);
  }

  public static <T> RestResult<T> error(T data, String message) {
    return new RestResult<>(ERROR, message, data);
  }

  public static <T> RestResult<T> error(int code, String message, T data) {
    return new RestResult<>(code, message, data);
  }

}
