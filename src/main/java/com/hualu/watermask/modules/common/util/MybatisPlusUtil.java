package com.hualu.watermask.modules.common.util;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * MybatisPlusUtil
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-11 09:17
 */
public class MybatisPlusUtil {

  public static <T> List<T> getAllRecordWithPagination(
      IService<T> service,
      Wrapper<T> ew,
      int pageSize
  ) {
    List<T> allRecords = new ArrayList<>();
    long total = service.count(ew);
    int currentPage = 1;
    int totalPages = (int) Math.ceil((double) total / pageSize);

    while (currentPage <= totalPages) {
      Page<T> page = new Page<>(currentPage, pageSize);
      IPage<T> userPage = service.page(page, ew);
      allRecords.addAll(userPage.getRecords());
      currentPage++;
    }

    return allRecords;
  }

  public static <B, R> List<B> batchIn(
      IService<B> service,
      LambdaQueryWrapper<B> ew,
      SFunction<B, R> func,
      List<R> list,
      int pageSize
  ) {
    int realPageSize = Math.min(pageSize, 500);
    int total = list.size();
    int totalPages = (int) Math.ceil((double) total / realPageSize);

    List<B> totalList = new ArrayList<>();
    for (int i = 0; i < totalPages; i++) {
      ew.in(func, list.subList(i * realPageSize, Math.min(total, (i + 1) * realPageSize)));
      List<B> record = service.list(ew);
      totalList.addAll(record);
    }

    return totalList;
  }
}
