package com.hualu.watermask.modules.common.exception;

/**
 * 业务异常
 */
public class BusinessException extends RuntimeException {

  private String message;

  private int code;

  public BusinessException(String message) {
    super(message);
    this.message = message;
  }

  public BusinessException(String message, int code) {
    super(message);
    this.message = message;
    this.code = code;
  }

  public String getMessage() {
    return message;
  }

  public void setMessage(String message) {
    this.message = message;
  }

  public int getCode() {
    return code;
  }

  public void setCode(int code) {
    this.code = code;
  }
}
