package com.hualu.watermask.modules.common.datastore;

import com.hualu.watermask.modules.util.RequestContextUtil;

/**
 * 数据存储
 *
 * <AUTHOR>
 * @since 2025-07-16 17:02
 */
public class HttpDataStore {

  private static volatile HttpDataStore instance;

  // 2. 私有构造方法（防止外部实例化）
  private HttpDataStore() {
    // 防止反射破坏单例（可选，增强安全性）
    if (instance != null) {
      throw new RuntimeException("HttpDataStore 是单例类，禁止通过反射实例化");
    }
  }

  // 3. 公共静态方法（双重检查锁定，确保线程安全且懒加载）
  public static HttpDataStore getInstance() {
    // 第一次检查：未初始化时才进入同步块（提高效率）
    if (instance == null) {
      // 同步块：保证只有一个线程进入初始化逻辑
      synchronized (HttpDataStore.class) {
        // 第二次检查：防止多线程并发时重复初始化
        if (instance == null) {
          instance = new HttpDataStore();
        }
      }
    }
    return instance;
  }

  public String getTeamId() {
    return (String) RequestContextUtil.getRequestParam("teamId");
  }

}
