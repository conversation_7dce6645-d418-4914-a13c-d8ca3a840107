package com.hualu.watermask.modules.watermasktemplate.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 模板字段定义表
 */
@TableName(value = "WATERMARK_TEMPLATE_CONTENT")
public class WatermarkTemplateContent {
    /**
     * 内容项唯一标识符
     */
    @TableId(value = "CONTENT_ID")
    private String contentId;

    /**
     * 关联模板ID
     */
    @TableField(value = "TEMPLATE_ID")
    private String templateId;

    /**
     * 字段标题
     */
    @TableField(value = "TITLE")
    private String title;

    /**
     * 显示排序序号
     */
    @TableField(value = "SORT_ORDER")
    private Integer sortOrder;
    
    /**
     * 字体颜色（例如：#FFFFFF）
     */
    @TableField(value = "FONT_COLOR")
    private String fontColor;
    
    /**
     * 背景颜色（例如：#000000）
     */
    @TableField(value = "BACKGROUND_COLOR")
    private String backgroundColor;

    @TableField(value = "ENABLE")
    private Integer enable;

    @TableField(value = "INPUT_TYPE")
    private Integer inputType;

    @TableField(value = "CHECKED")
    private Integer checked;

    @TableField(value = "WHETHER_TITLE")
    private Integer whetherTitle;

    @TableField(value = "CONTENT")
    private String content;

    @TableField(value = "ICON")
    private String icon;

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    /**
     * 获取内容项唯一标识符
     *
     * @return CONTENT_ID - 内容项唯一标识符
     */
    public String getContentId() {
        return contentId;
    }

    /**
     * 设置内容项唯一标识符
     *
     * @param contentId 内容项唯一标识符
     */
    public void setContentId(String contentId) {
        this.contentId = contentId;
    }

    /**
     * 获取关联模板ID
     *
     * @return TEMPLATE_ID - 关联模板ID
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     * 设置关联模板ID
     *
     * @param templateId 关联模板ID
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    /**
     * 获取字段标题
     *
     * @return TITLE - 字段标题
     */
    public String getTitle() {
        return title;
    }

    /**
     * 设置字段标题
     *
     * @param title 字段标题
     */
    public void setTitle(String title) {
        this.title = title;
    }

    /**
     * 获取显示排序序号
     *
     * @return SORT_ORDER - 显示排序序号
     */
    public Integer getSortOrder() {
        return sortOrder;
    }

    /**
     * 设置显示排序序号
     *
     * @param sortOrder 显示排序序号
     */
    public void setSortOrder(Integer sortOrder) {
        this.sortOrder = sortOrder;
    }
    
    /**
     * 获取字体颜色
     *
     * @return FONT_COLOR - 字体颜色
     */
    public String getFontColor() {
        return fontColor;
    }

    /**
     * 设置字体颜色
     *
     * @param fontColor 字体颜色
     */
    public void setFontColor(String fontColor) {
        this.fontColor = fontColor;
    }

    /**
     * 获取背景颜色
     *
     * @return BACKGROUND_COLOR - 背景颜色
     */
    public String getBackgroundColor() {
        return backgroundColor;
    }

    /**
     * 设置背景颜色
     *
     * @param backgroundColor 背景颜色
     */
    public void setBackgroundColor(String backgroundColor) {
        this.backgroundColor = backgroundColor;
    }

    public Integer getEnable() {
        return enable;
    }

    public void setEnable(Integer enable) {
        this.enable = enable;
    }

    public Integer getInputType() {
        return inputType;
    }

    public void setInputType(Integer inputType) {
        this.inputType = inputType;
    }

    public Integer getChecked() {
        return checked;
    }

    public void setChecked(Integer checked) {
        this.checked = checked;
    }

    public Integer getWhetherTitle() {
        return whetherTitle;
    }

    public void setWhetherTitle(Integer whetherTitle) {
        this.whetherTitle = whetherTitle;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
}