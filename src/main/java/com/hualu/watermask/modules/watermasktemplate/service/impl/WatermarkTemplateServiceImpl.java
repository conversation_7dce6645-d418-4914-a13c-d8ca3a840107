package com.hualu.watermask.modules.watermasktemplate.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.user.entity.WsPhotoTeams;
import com.hualu.watermask.modules.user.service.WsPhotoTeamsService;
import com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhoto;
import com.hualu.watermask.modules.watermaskphoto.service.WatermarkPhotoService;
import com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplate;
import com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplateContent;
import com.hualu.watermask.modules.watermasktemplate.mapper.WatermarkTemplateMapper;
import com.hualu.watermask.modules.watermasktemplate.service.WatermarkTemplateService;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class WatermarkTemplateServiceImpl extends ServiceImpl<WatermarkTemplateMapper, WatermarkTemplate> implements WatermarkTemplateService{

    @Autowired
    private com.hualu.watermask.modules.watermasktemplate.service.WatermarkTemplateContentService watermarkTemplateContentService;
    @Autowired
    private WatermarkTemplateService watermarkTemplateService;
    @Autowired
    private WsPhotoTeamsService wsPhotoTeamsService;
    @Autowired
    private WatermarkPhotoService watermarkPhotoService;

    /**
     * 创建水印模板
     *
     * @param template 水印模板信息
     * @param userId 创建者ID
     * @return 创建结果
     */
    @Override
    @Transactional
    public RestResult<WatermarkTemplate> createTemplate(WatermarkTemplate template, String userId) {
        // 设置初始值
        template.setTemplateId(UUID.randomUUID().toString().replace("-", ""));
        template.setCreatedTime(new Date());
        template.setUpdatedTime(new Date());
        template.setDelFlag(new BigDecimal(0));
        template.setCreatedBy(userId);
        template.setUpdatedBy(userId);

        boolean result = this.save(template);
        if (result) {
            return RestResult.success(template, "创建水印模板成功");
        } else {
            return RestResult.error("创建水印模板失败");
        }
    }
    
    /**
     * 更新水印模板
     *
     * @param templateId 模板ID
     * @param templateName 更新的模板名称
     * @param imageId 模板图片ID（可选）
     * @param width 模板宽度（可选）
     * @param height 模板高度（可选）
     * @param userId 更新者ID
     * @return 更新结果
     */
    @Override
    @Transactional
    public RestResult<WatermarkTemplate> updateTemplate(String templateId, String templateName, String imageId, Integer width, Integer height, String userId) {
        WatermarkTemplate template = this.getById(templateId);
        if (template == null) {
            return RestResult.error("模板不存在");
        }
        
        template.setUpdatedTime(new Date());
        template.setTemplateName(templateName);
        template.setUpdatedBy(userId);
        
        // 如果提供了图片ID，则更新
        if (imageId != null && !imageId.isEmpty()) {
            template.setImageId(imageId);
        }
        
        // 如果提供了宽度，则更新
        if (width != null) {
            template.setWidth(width);
        }
        
        // 如果提供了高度，则更新
        if (height != null) {
            template.setHeight(height);
        }

        boolean result = this.updateById(template);
        if (result) {
            return RestResult.success(template, "更新水印模板成功");
        } else {
            return RestResult.error("更新水印模板失败，模板可能不存在");
        }
    }
    
    /**
     * 删除水印模板（逻辑删除）
     *
     * @param templateId 模板ID
     * @param userId 删除者ID
     * @return 删除结果
     */
    @Override
    @Transactional
    public RestResult<Object> deleteTemplate(String templateId, String userId) {
        WatermarkTemplate template = this.getById(templateId);
        if (template == null) {
            return RestResult.error("模板不存在");
        }
        
        template.setDelFlag(new BigDecimal(1));
        template.setUpdatedTime(new Date());
        template.setUpdatedBy(userId);

        boolean result = this.updateById(template);
        if (result) {
            return RestResult.success(null, "删除水印模板成功");
        } else {
            return RestResult.error("删除水印模板失败，模板可能不存在");
        }
    }
    
    /**
     * 获取水印模板详情
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    @Override
    public RestResult<WatermarkTemplate> getTemplateById(String templateId) {
        WatermarkTemplate template = this.getById(templateId);
        if (template != null && template.getDelFlag().compareTo(new BigDecimal(0)) == 0) {
            // 查询模板内容列表
            RestResult<List<WatermarkTemplateContent>> contentResult = watermarkTemplateContentService.getContentByTemplateId(templateId);
            if (contentResult != null && contentResult.getCode() == RestResult.SUCCESS) {
                template.setTemplateContents(contentResult.getData());
            }
            return RestResult.success(template);
        } else {
            return RestResult.error("未找到指定的水印模板或模板已被删除");
        }
    }
    
    /**
     * 分页查询水印模板列表
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @param teamId 团队ID（可选）
     * @param templateName 模板名称（可选，模糊查询）
     * @return 分页结果
     */
    @Override
    public RestResult<List<WatermarkTemplate>> getTemplateList(Long page, Long pageSize, String teamId, String templateName) {
        Page<WatermarkTemplate> pageParam = new Page<>(page, pageSize);
        LambdaQueryWrapper<WatermarkTemplate> queryWrapper = new LambdaQueryWrapper<>();
        
        // 只查询未删除的记录
        queryWrapper.eq(WatermarkTemplate::getDelFlag, new BigDecimal(0));

        // 根据条件过滤
        if (teamId != null && !teamId.isEmpty()) {
            queryWrapper.eq(WatermarkTemplate::getTeamId, teamId);
        }

        if (templateName != null && !templateName.isEmpty()) {
            queryWrapper.like(WatermarkTemplate::getTemplateName, templateName);
        }

        // 按更新时间倒序排序
        queryWrapper.orderByDesc(WatermarkTemplate::getUpdatedTime);
        
        IPage<WatermarkTemplate> result = this.page(pageParam, queryWrapper);
        List<WatermarkTemplate> templates = result.getRecords();
        
        if (!templates.isEmpty()) {
            // 收集所有模板ID
            List<String> templateIds = templates.stream()
                .map(WatermarkTemplate::getTemplateId)
                .collect(Collectors.toList());
            
            // 一次性批量查询所有模板内容
            LambdaQueryWrapper<WatermarkTemplateContent> contentQueryWrapper = new LambdaQueryWrapper<>();
            contentQueryWrapper.eq(WatermarkTemplateContent::getEnable, 1);
            contentQueryWrapper.in(WatermarkTemplateContent::getTemplateId, templateIds)
                .orderByAsc(WatermarkTemplateContent::getSortOrder);
            List<WatermarkTemplateContent> allContents = watermarkTemplateContentService.list(contentQueryWrapper);
            
            // 按模板ID分组
            Map<String, List<WatermarkTemplateContent>> contentMap = allContents.stream()
                .collect(Collectors.groupingBy(WatermarkTemplateContent::getTemplateId));
            
            // 关联到对应的模板
            for (WatermarkTemplate template : templates) {
                template.setTemplateContents(contentMap.getOrDefault(template.getTemplateId(), new ArrayList<>()));
            }
        }
        
        return RestResult.success(templates, result.getTotal(), page, pageSize);
    }

    @Override
    public RestResult<List<Map<String,Object>>> getTemplateList(String teamId, String templateName) {
        LambdaQueryWrapper<WatermarkTemplate> queryWrapper = new LambdaQueryWrapper<>();
        // 只查询未删除的记录
        queryWrapper.eq(WatermarkTemplate::getDelFlag, new BigDecimal(0));
        // 根据条件过滤
        if (teamId != null && !teamId.isEmpty()) {
            queryWrapper.eq(WatermarkTemplate::getTeamId, teamId);
        }
        if (templateName != null && !templateName.isEmpty()) {
            queryWrapper.like(WatermarkTemplate::getTemplateName, templateName);
        }
        LambdaQueryWrapper<WsPhotoTeams> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WsPhotoTeams::getTeamId, teamId);
        List<WsPhotoTeams> photoTeams = wsPhotoTeamsService.list(wrapper);
        if(photoTeams == null || photoTeams.isEmpty()){
            return RestResult.success(new ArrayList<>());
        }
        List<String> pids = photoTeams.stream().map(WsPhotoTeams::getPhotoId).collect(Collectors.toList());
        LambdaQueryWrapper<WatermarkPhoto> photoLambdaQueryWrapper = new LambdaQueryWrapper<>();
        photoLambdaQueryWrapper.in(WatermarkPhoto::getPhotoId, pids);
        photoLambdaQueryWrapper.eq(WatermarkPhoto::getDelFlag, 0);
        List<WatermarkPhoto> photos = watermarkPhotoService.list(photoLambdaQueryWrapper);
        Map<String, Date> data = photos.stream()
                // 1. 过滤无效数据
                .filter(photo -> photo != null
                        && photo.getTemplateId() != null
                        && photo.getCaptureTime() != null)
                // 2. 分组并计算每组最大值
                .collect(Collectors.groupingBy(
                        WatermarkPhoto::getTemplateId,
                        Collectors.collectingAndThen(
                                Collectors.maxBy(Comparator.comparing(WatermarkPhoto::getCaptureTime)),
                                optional -> optional.map(WatermarkPhoto::getCaptureTime).orElse(null)
                        )
                ));
        // 按更新时间倒序排序
        queryWrapper.orderByDesc(WatermarkTemplate::getUpdatedTime);
        List<WatermarkTemplate> list = watermarkTemplateService.list(queryWrapper);
        List<Map<String,Object>> result = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        list.forEach(template -> {
            Map<String,Object> map = new HashMap<>();
            String templateId = template.getTemplateId();
            map.put("id", templateId);
            map.put("title",template.getTemplateName());
            map.put("img",template.getImageId());
            map.put("lastUsed",sdf.format(data.get(templateId)));
            result.add(map);
        });
        return RestResult.success(result);
    }
}
