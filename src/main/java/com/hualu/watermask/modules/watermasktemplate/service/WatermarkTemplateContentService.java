package com.hualu.watermask.modules.watermasktemplate.service;

import com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplateContent;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

public interface WatermarkTemplateContentService extends IService<WatermarkTemplateContent>{

    /**
     * 创建水印模板内容
     *
     * @param content 水印模板内容信息
     * @return 创建结果
     */
    RestResult<WatermarkTemplateContent> createContent(WatermarkTemplateContent content);
    
    /**
     * 批量创建水印模板内容
     *
     * @param contentList 水印模板内容列表
     * @return 创建结果
     */
    RestResult<List<WatermarkTemplateContent>> createBatchContent(List<WatermarkTemplateContent> contentList);
    
    /**
     * 更新水印模板内容
     *
     * @param contentId 内容ID
     * @param title 标题
     * @param sortOrder 排序号
     * @param fontColor 字体颜色
     * @param backgroundColor 背景颜色
     * @return 更新结果
     */
    RestResult<WatermarkTemplateContent> updateContent(String contentId, String title, Integer sortOrder, String fontColor, String backgroundColor);
    
    /**
     * 删除水印模板内容
     *
     * @param contentId 内容ID
     * @return 删除结果
     */
    RestResult<Object> deleteContent(String contentId);
    
    /**
     * 获取水印模板内容详情
     *
     * @param contentId 内容ID
     * @return 内容详情
     */
    RestResult<WatermarkTemplateContent> getContentById(String contentId);
    
    /**
     * 根据模板ID获取所有内容项
     *
     * @param templateId 模板ID
     * @return 内容项列表
     */
    RestResult<List<WatermarkTemplateContent>> getContentByTemplateId(String templateId);
    
    /**
     * 删除模板下的所有内容项
     *
     * @param templateId 模板ID
     * @return 删除结果
     */
    RestResult<Object> deleteContentByTemplateId(String templateId);
}
