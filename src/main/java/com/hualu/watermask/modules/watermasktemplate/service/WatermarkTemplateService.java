package com.hualu.watermask.modules.watermasktemplate.service;

import com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplate;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;
import java.util.Map;

public interface WatermarkTemplateService extends IService<WatermarkTemplate>{

    /**
     * 创建水印模板
     *
     * @param template 水印模板信息
     * @param userId 创建者ID
     * @return 创建结果
     */
    RestResult<WatermarkTemplate> createTemplate(WatermarkTemplate template, String userId);
    
    /**
     * 更新水印模板
     *
     * @param templateId 模板ID
     * @param templateName 更新的模板名称
     * @param imageId 模板图片ID（可选）
     * @param width 模板宽度（可选）
     * @param height 模板高度（可选）
     * @param userId 更新者ID
     * @return 更新结果
     */
    RestResult<WatermarkTemplate> updateTemplate(String templateId, String templateName, String imageId, Integer width, Integer height, String userId);
    
    /**
     * 删除水印模板（逻辑删除）
     *
     * @param templateId 模板ID
     * @param userId 删除者ID
     * @return 删除结果
     */
    RestResult<Object> deleteTemplate(String templateId, String userId);
    
    /**
     * 获取水印模板详情
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    RestResult<WatermarkTemplate> getTemplateById(String templateId);
    
    /**
     * 分页查询水印模板列表
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @param teamId 团队ID（可选）
     * @param templateName 模板名称（可选，模糊查询）
     * @return 分页结果
     */
    RestResult<List<WatermarkTemplate>> getTemplateList(Long page, Long pageSize, String teamId, String templateName);

    /**
     * 分页查询水印模板列表
     *
     * @param teamId 团队ID（可选）
     * @param templateName 模板名称（可选，模糊查询）
     * @return 分页结果
     */
    RestResult<List<Map<String,Object>>> getTemplateList(String teamId, String templateName);
}
