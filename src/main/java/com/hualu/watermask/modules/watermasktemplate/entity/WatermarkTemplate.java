package com.hualu.watermask.modules.watermasktemplate.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 水印模板主表
 */
@TableName(value = "WATERMARK_TEMPLATE")
public class WatermarkTemplate {
    /**
     * 模板唯一标识符
     */
    @TableId(value = "TEMPLATE_ID")
    private String templateId;

    /**
     * 所属团队ID
     */
    @TableField(value = "TEAM_ID")
    private String teamId;

    /**
     * 模板名称
     */
    @TableField(value = "TEMPLATE_NAME")
    private String templateName;

    /**
     * 创建人ID
     */
    @TableField(value = "CREATED_BY")
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(value = "CREATED_TIME")
    private Date createdTime;

    /**
     * 最后修改时间
     */
    @TableField(value = "UPDATED_TIME")
    private Date updatedTime;

    /**
     * 最后修改人ID
     */
    @TableField(value = "UPDATED_BY")
    private String updatedBy;

    /**
     * 删除标记(0:未删除 1:已删除)
     */
    @TableField(value = "DEL_FLAG")
    private BigDecimal delFlag;
    
    /**
     * 模板图片ID
     */
    @TableField(value = "IMAGE_ID")
    private String imageId;
    
    /**
     * 模板宽度
     */
    @TableField(value = "WIDTH")
    private Integer width;
    
    /**
     * 模板高度
     */
    @TableField(value = "HEIGHT")
    private Integer height;

    /**
     * 图标
     */
    @TableField(value = "ICON")
    private String icon;
    
    /**
     * 模板内容列表（非数据库字段）
     */
    @TableField(exist = false)
    private List<WatermarkTemplateContent> templateContents;

    /**
     * 获取模板唯一标识符
     *
     * @return TEMPLATE_ID - 模板唯一标识符
     */
    public String getTemplateId() {
        return templateId;
    }

    /**
     * 设置模板唯一标识符
     *
     * @param templateId 模板唯一标识符
     */
    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    /**
     * 获取所属团队ID
     *
     * @return TEAM_ID - 所属团队ID
     */
    public String getTeamId() {
        return teamId;
    }

    /**
     * 设置所属团队ID
     *
     * @param teamId 所属团队ID
     */
    public void setTeamId(String teamId) {
        this.teamId = teamId;
    }

    /**
     * 获取模板名称
     *
     * @return TEMPLATE_NAME - 模板名称
     */
    public String getTemplateName() {
        return templateName;
    }

    /**
     * 设置模板名称
     *
     * @param templateName 模板名称
     */
    public void setTemplateName(String templateName) {
        this.templateName = templateName;
    }

    /**
     * 获取创建人ID
     *
     * @return CREATED_BY - 创建人ID
     */
    public String getCreatedBy() {
        return createdBy;
    }

    /**
     * 设置创建人ID
     *
     * @param createdBy 创建人ID
     */
    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    /**
     * 获取创建时间
     *
     * @return CREATED_TIME - 创建时间
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * 设置创建时间
     *
     * @param createdTime 创建时间
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * 获取最后修改时间
     *
     * @return UPDATED_TIME - 最后修改时间
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * 设置最后修改时间
     *
     * @param updatedTime 最后修改时间
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * 获取最后修改人ID
     *
     * @return UPDATED_BY - 最后修改人ID
     */
    public String getUpdatedBy() {
        return updatedBy;
    }

    /**
     * 设置最后修改人ID
     *
     * @param updatedBy 最后修改人ID
     */
    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    /**
     * 获取删除标记(0:未删除 1:已删除)
     *
     * @return DEL_FLAG - 删除标记(0:未删除 1:已删除)
     */
    public BigDecimal getDelFlag() {
        return delFlag;
    }

    /**
     * 设置删除标记(0:未删除 1:已删除)
     *
     * @param delFlag 删除标记(0:未删除 1:已删除)
     */
    public void setDelFlag(BigDecimal delFlag) {
        this.delFlag = delFlag;
    }
    
    /**
     * 获取模板图片ID
     *
     * @return IMAGE_ID - 模板图片ID
     */
    public String getImageId() {
        return imageId;
    }

    /**
     * 设置模板图片ID
     *
     * @param imageId 模板图片ID
     */
    public void setImageId(String imageId) {
        this.imageId = imageId;
    }
    
    /**
     * 获取模板宽度
     *
     * @return WIDTH - 模板宽度
     */
    public Integer getWidth() {
        return width;
    }

    /**
     * 设置模板宽度
     *
     * @param width 模板宽度
     */
    public void setWidth(Integer width) {
        this.width = width;
    }

    /**
     * 获取模板高度
     *
     * @return HEIGHT - 模板高度
     */
    public Integer getHeight() {
        return height;
    }

    /**
     * 设置模板高度
     *
     * @param height 模板高度
     */
    public void setHeight(Integer height) {
        this.height = height;
    }
    
    /**
     * 获取模板内容列表
     *
     * @return 模板内容列表
     */
    public List<WatermarkTemplateContent> getTemplateContents() {
        return templateContents;
    }

    /**
     * 设置模板内容列表
     *
     * @param templateContents 模板内容列表
     */
    public void setTemplateContents(List<WatermarkTemplateContent> templateContents) {
        this.templateContents = templateContents;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }
}