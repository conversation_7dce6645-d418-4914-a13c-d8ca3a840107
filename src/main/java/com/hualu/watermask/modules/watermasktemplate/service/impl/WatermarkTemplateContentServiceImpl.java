package com.hualu.watermask.modules.watermasktemplate.service.impl;

import org.springframework.stereotype.Service;
import org.springframework.beans.factory.annotation.Autowired;
import java.util.List;
import java.util.UUID;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hualu.watermask.modules.watermasktemplate.mapper.WatermarkTemplateContentMapper;
import com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplateContent;
import com.hualu.watermask.modules.watermasktemplate.service.WatermarkTemplateContentService;
import com.hualu.watermask.modules.common.vo.RestResult;
import org.apache.commons.lang3.StringUtils;
import org.springframework.transaction.annotation.Transactional;

@Service
public class WatermarkTemplateContentServiceImpl extends ServiceImpl<WatermarkTemplateContentMapper, WatermarkTemplateContent> implements WatermarkTemplateContentService{

    /**
     * 创建水印模板内容
     *
     * @param content 水印模板内容信息
     * @return 创建结果
     */
    @Override
    @Transactional
    public RestResult<WatermarkTemplateContent> createContent(WatermarkTemplateContent content) {
        if(StringUtils.isBlank(content.getTemplateId())){
            return RestResult.error("模板ID不能为空");
        }
        // 设置初始值
        content.setContentId(UUID.randomUUID().toString().replace("-", ""));
        boolean result = this.save(content);
        if (result) {
            return RestResult.success(content, "创建水印模板内容成功");
        } else {
            return RestResult.error("创建水印模板内容失败");
        }
    }
    
    /**
     * 批量创建水印模板内容
     *
     * @param contentList 水印模板内容列表
     * @return 创建结果
     */
    @Override
    @Transactional
    public RestResult<List<WatermarkTemplateContent>> createBatchContent(List<WatermarkTemplateContent> contentList) {
        // 设置每个内容项的ID
        for (WatermarkTemplateContent content : contentList) {
            if(StringUtils.isBlank(content.getTemplateId())){
                return RestResult.error("模板ID不能为空");
            }
            content.setContentId(UUID.randomUUID().toString().replace("-", ""));
        }
        
        boolean result = this.saveBatch(contentList);
        if (result) {
            return RestResult.success(contentList, "批量创建水印模板内容成功");
        } else {
            return RestResult.error("批量创建水印模板内容失败");
        }
    }
    
    /**
     * 更新水印模板内容
     *
     * @param contentId 内容ID
     * @param title 标题
     * @param sortOrder 排序号
     * @param fontColor 字体颜色
     * @param backgroundColor 背景颜色
     * @return 更新结果
     */
    @Override
    @Transactional
    public RestResult<WatermarkTemplateContent> updateContent(String contentId, String title, Integer sortOrder, String fontColor, String backgroundColor) {
        if(StringUtils.isBlank(contentId)){
            return RestResult.error("内容ID不能为空");
        }
        WatermarkTemplateContent content = this.getById(contentId);
        if (content == null) {
            return RestResult.error("内容项不存在");
        }
        content.setTitle(title);
        content.setSortOrder(sortOrder);
        
        // 设置字体颜色和背景颜色
        if (StringUtils.isNotBlank(fontColor)) {
            content.setFontColor(fontColor);
        }
        if (StringUtils.isNotBlank(backgroundColor)) {
            content.setBackgroundColor(backgroundColor);
        }
        
        boolean result = this.updateById(content);
        if (result) {
            return RestResult.success(content, "更新水印模板内容成功");
        } else {
            return RestResult.error("更新水印模板内容失败，内容项可能不存在");
        }
    }
    
    /**
     * 删除水印模板内容
     *
     * @param contentId 内容ID
     * @return 删除结果
     */
    @Override
    @Transactional
    public RestResult<Object> deleteContent(String contentId) {
        boolean result = this.removeById(contentId);
        if (result) {
            return RestResult.success(null, "删除水印模板内容成功");
        } else {
            return RestResult.error("删除水印模板内容失败，内容项可能不存在");
        }
    }
    
    /**
     * 获取水印模板内容详情
     *
     * @param contentId 内容ID
     * @return 内容详情
     */
    @Override
    public RestResult<WatermarkTemplateContent> getContentById(String contentId) {
        WatermarkTemplateContent content = this.getById(contentId);
        if (content != null) {
            return RestResult.success(content);
        } else {
            return RestResult.error("未找到指定的水印模板内容");
        }
    }
    
    /**
     * 根据模板ID获取所有内容项
     *
     * @param templateId 模板ID
     * @return 内容项列表
     */
    @Override
    public RestResult<List<WatermarkTemplateContent>> getContentByTemplateId(String templateId) {
        LambdaQueryWrapper<WatermarkTemplateContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WatermarkTemplateContent::getTemplateId, templateId);
        queryWrapper.orderByAsc(WatermarkTemplateContent::getSortOrder); // 按排序号升序
        
        List<WatermarkTemplateContent> contentList = this.list(queryWrapper);
        return RestResult.success(contentList);
    }
    
    /**
     * 删除模板下的所有内容项
     *
     * @param templateId 模板ID
     * @return 删除结果
     */
    @Override
    @Transactional
    public RestResult<Object> deleteContentByTemplateId(String templateId) {
        LambdaQueryWrapper<WatermarkTemplateContent> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WatermarkTemplateContent::getTemplateId, templateId);
        
        boolean result = this.remove(queryWrapper);
        if (result) {
            return RestResult.success(null, "删除模板内容项成功");
        } else {
            return RestResult.error("删除模板内容项失败");
        }
    }
}
