package com.hualu.watermask.modules.watermasktemplate.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplate;
import com.hualu.watermask.modules.watermasktemplate.service.WatermarkTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.ArrayList;
import java.util.Map;

/**
 * 水印模板控制器
 */
@RestController
@RequestMapping("/api/template")
@CrossOrigin
public class WatermarkTemplateController {

    @Autowired
    private WatermarkTemplateService watermarkTemplateService;

    /**
     * 创建水印模板
     *
     * @param template 水印模板信息
     * @return 创建结果
     */
    @PostMapping("/create")
    @OperationLog(businessType = "水印模板管理", operationContent = "创建水印模板", operationType = "INSERT")
    public RestResult<WatermarkTemplate> create(@RequestBody WatermarkTemplate template) {
        String userId = StpUtil.getLoginIdAsString();
        return watermarkTemplateService.createTemplate(template, userId);
    }

    /**
     * 创建水印模板并上传图片
     *
     * @param templateName 模板名称
     * @param teamId 团队ID
     * @param imageFile 图片文件
     * @return 创建结果
     */
    @PostMapping("/create-with-image")
    @OperationLog(businessType = "水印模板管理", operationContent = "创建水印模板并上传图片", operationType = "INSERT")
    public RestResult<WatermarkTemplate> createWithImage(
            @RequestParam String templateName,
            @RequestParam String teamId,
            @RequestParam(required = false) org.springframework.web.multipart.MultipartFile imageFile,
            @RequestParam(required = false) org.springframework.web.multipart.MultipartFile icon) {
        String userId = StpUtil.getLoginIdAsString();
        try {
            // 创建模板
            WatermarkTemplate template = new WatermarkTemplate();
            template.setTemplateName(templateName);
            template.setTeamId(teamId);

            // 如果提供了图片，则上传
            if (imageFile != null && !imageFile.isEmpty()) {
                // 获取图片宽高
                try {
                    java.io.InputStream inputStream = imageFile.getInputStream();
                    java.awt.image.BufferedImage bufferedImage = javax.imageio.ImageIO.read(inputStream);
                    if (bufferedImage != null) {
                        // 获取宽高并取整
                        int width = bufferedImage.getWidth();
                        int height = bufferedImage.getHeight();

                        // 设置到模板中
                        template.setWidth(width);
                        template.setHeight(height);
                    }
                } catch (Exception e) {
                    // 获取图片尺寸失败，记录错误但继续执行
                    System.err.println("获取图片尺寸失败: " + e.getMessage());
                }

                List<org.springframework.web.multipart.MultipartFile> images = new ArrayList<>();
                images.add(imageFile);
                List<String> imageIds = com.hualu.watermask.modules.watermaskphoto.utils.FileUtils.uploadImage(images);

                if (imageIds != null && !imageIds.isEmpty()) {
                    template.setImageId(imageIds.get(0));
                }
            }

            // 如果提供了图标，则上传
            if (icon != null && !icon.isEmpty()) {
                List<org.springframework.web.multipart.MultipartFile> icons = new ArrayList<>();
                icons.add(icon);
                List<String> iconIds = com.hualu.watermask.modules.watermaskphoto.utils.FileUtils.uploadImage(icons);

                if (iconIds != null && !iconIds.isEmpty()) {
                    template.setIcon(iconIds.get(0));
                }
            }

            return watermarkTemplateService.createTemplate(template, userId);
        } catch (Exception e) {
            return RestResult.error("创建模板失败：" + e.getMessage());
        }
    }

    /**
     * 更新水印模板
     *
     * @param templateId 模板ID
     * @param templateName 更新的模板名称
     * @param imageId 模板图片ID（可选）
     * @param width 模板宽度（可选）
     * @param height 模板高度（可选）
     * @return 更新结果
     */
    @PutMapping("/update")
    @OperationLog(businessType = "水印模板管理", operationContent = "更新水印模板", operationType = "UPDATE")
    public RestResult<WatermarkTemplate> update(
            @RequestParam String templateId,
            @RequestParam String templateName,
            @RequestParam(required = false) String imageId,
            @RequestParam(required = false) Integer width,
            @RequestParam(required = false) Integer height) {
        String userId = StpUtil.getLoginIdAsString();
        return watermarkTemplateService.updateTemplate(templateId, templateName, imageId, width, height, userId);
    }

    /**
     * 删除水印模板（逻辑删除）
     *
     * @param templateId 模板ID
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @OperationLog(businessType = "水印模板管理", operationContent = "删除水印模板", operationType = "DELETE")
    public RestResult<Object> delete(@RequestParam String templateId) {
        String userId = StpUtil.getLoginIdAsString();
        return watermarkTemplateService.deleteTemplate(templateId, userId);
    }

    /**
     * 获取水印模板详情
     *
     * @param templateId 模板ID
     * @return 模板详情
     */
    @GetMapping("/get")
    public RestResult<WatermarkTemplate> getById(@RequestParam String templateId) {
        return watermarkTemplateService.getTemplateById(templateId);
    }

    /**
     * 分页查询水印模板列表
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @param teamId 团队ID（可选）
     * @param templateName 模板名称（可选，模糊查询）
     * @return 分页结果
     */
    @GetMapping("/page")
    public RestResult<List<WatermarkTemplate>> list(
            @RequestParam(defaultValue = "1") Long page,
            @RequestParam(defaultValue = "10") Long pageSize,
            @RequestParam(required = false) String teamId,
            @RequestParam(required = false) String templateName) {

        return watermarkTemplateService.getTemplateList(page, pageSize, teamId, templateName);
    }

    /**
     * 查询水印模板列表
     *
     * @param teamId 团队ID（可选）
     * @param templateName 模板名称（可选，模糊查询）
     * @return 分页结果
     */
    @GetMapping("/list")
    public RestResult<List<Map<String,Object>>> list(
            @RequestParam(required = false) String teamId,
            @RequestParam(required = false) String templateName) {

        return watermarkTemplateService.getTemplateList(teamId, templateName);
    }

    /**
     * 为模板上传图片
     *
     * @param templateId 模板ID
     * @param imageFile 图片文件
     * @return 上传结果
     */
    @PostMapping("/upload-image")
    @OperationLog(businessType = "水印模板管理", operationContent = "上传模板图片", operationType = "UPDATE")
    public RestResult<WatermarkTemplate> uploadImage(
            @RequestParam String templateId,
            @RequestParam org.springframework.web.multipart.MultipartFile imageFile) {
        String userId = StpUtil.getLoginIdAsString();
        try {
            if (imageFile == null || imageFile.isEmpty()) {
                return RestResult.error("请上传有效的图片文件");
            }

            // 获取图片宽高
            Integer width = null;
            Integer height = null;
            try {
                java.io.InputStream inputStream = imageFile.getInputStream();
                java.awt.image.BufferedImage bufferedImage = javax.imageio.ImageIO.read(inputStream);
                if (bufferedImage != null) {
                    // 获取宽高并取整
                    width = bufferedImage.getWidth();
                    height = bufferedImage.getHeight();
                }
            } catch (Exception e) {
                // 获取图片尺寸失败，记录错误但继续执行
                System.err.println("获取图片尺寸失败: " + e.getMessage());
            }

            List<org.springframework.web.multipart.MultipartFile> images = new ArrayList<>();
            images.add(imageFile);
            List<String> imageIds = com.hualu.watermask.modules.watermaskphoto.utils.FileUtils.uploadImage(images);

            if (imageIds == null || imageIds.isEmpty()) {
                return RestResult.error("图片上传失败");
            }

            String imageId = imageIds.get(0);

            // 更新模板的图片ID和尺寸信息
            RestResult<WatermarkTemplate> updateResult = watermarkTemplateService.updateTemplate(
                    templateId,
                    watermarkTemplateService.getTemplateById(templateId).getData().getTemplateName(),
                    imageId,
                    width, // 更新宽度
                    height, // 更新高度
                    userId
            );

            if (updateResult.getCode() == 200) {
                return RestResult.success(updateResult.getData(), "图片上传成功");
            } else {
                return RestResult.error("更新模板图片ID失败：" + updateResult.getMessage());
            }
        } catch (Exception e) {
            return RestResult.error("图片上传失败：" + e.getMessage());
        }
    }
} 