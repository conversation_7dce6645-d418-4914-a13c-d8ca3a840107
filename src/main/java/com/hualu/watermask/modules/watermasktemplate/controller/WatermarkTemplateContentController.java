package com.hualu.watermask.modules.watermasktemplate.controller;

import com.hualu.watermask.modules.common.vo.RestResult;
import com.hualu.watermask.modules.log.annotation.OperationLog;
import com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplateContent;
import com.hualu.watermask.modules.watermasktemplate.service.WatermarkTemplateContentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 水印模板内容控制器
 */
@RestController
@RequestMapping("/api/template/content")
@CrossOrigin
public class WatermarkTemplateContentController {

    @Autowired
    private WatermarkTemplateContentService watermarkTemplateContentService;

    /**
     * 创建水印模板内容
     *
     * @param content 水印模板内容信息（包含字体颜色和背景颜色）
     * @return 创建结果
     */
    @PostMapping("/create")
    @OperationLog(businessType = "水印模板管理", operationContent = "创建模板内容", operationType = "INSERT")
    public RestResult<WatermarkTemplateContent> create(@RequestBody WatermarkTemplateContent content) {
        return watermarkTemplateContentService.createContent(content);
    }

    /**
     * 批量创建水印模板内容
     *
     * @param contentList 水印模板内容列表（每项包含字体颜色和背景颜色）
     * @return 创建结果
     */
    @PostMapping("/batch")
    @OperationLog(businessType = "水印模板管理", operationContent = "批量创建模板内容", operationType = "INSERT")
    public RestResult<List<WatermarkTemplateContent>> createBatch(@RequestBody List<WatermarkTemplateContent> contentList) {
        return watermarkTemplateContentService.createBatchContent(contentList);
    }

    /**
     * 更新水印模板内容
     *
     * @param contentId 内容ID
     * @param title 标题
     * @param sortOrder 排序号
     * @param fontColor 字体颜色
     * @param backgroundColor 背景颜色
     * @return 更新结果
     */
    @PutMapping("/update")
    @OperationLog(businessType = "水印模板管理", operationContent = "更新模板内容", operationType = "UPDATE")
    public RestResult<WatermarkTemplateContent> update(
            @RequestParam String contentId,
            @RequestParam String title,
            @RequestParam Integer sortOrder,
            @RequestParam(required = false) String fontColor,
            @RequestParam(required = false) String backgroundColor) {
        return watermarkTemplateContentService.updateContent(contentId, title, sortOrder, fontColor, backgroundColor);
    }

    /**
     * 删除水印模板内容
     *
     * @param contentId 内容ID
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @OperationLog(businessType = "水印模板管理", operationContent = "删除模板内容", operationType = "DELETE")
    public RestResult<Object> delete(@RequestParam String contentId) {
        return watermarkTemplateContentService.deleteContent(contentId);
    }

    /**
     * 获取水印模板内容详情
     *
     * @param contentId 内容ID
     * @return 内容详情
     */
    @GetMapping("/get")
    public RestResult<WatermarkTemplateContent> getById(@RequestParam String contentId) {
        return watermarkTemplateContentService.getContentById(contentId);
    }

    /**
     * 根据模板ID获取所有内容项
     *
     * @param templateId 模板ID
     * @return 内容项列表
     */
    @GetMapping("/getByTemplateId")
    public RestResult<List<WatermarkTemplateContent>> getByTemplateId(@RequestParam String templateId) {
        return watermarkTemplateContentService.getContentByTemplateId(templateId);
    }

    /**
     * 删除模板下的所有内容项
     *
     * @param templateId 模板ID
     * @return 删除结果
     */
    @DeleteMapping("/deleteByTemplateId")
    @OperationLog(businessType = "水印模板管理", operationContent = "删除模板下所有内容项", operationType = "DELETE")
    public RestResult<Object> deleteByTemplateId(@RequestParam String templateId) {
        return watermarkTemplateContentService.deleteContentByTemplateId(templateId);
    }
} 