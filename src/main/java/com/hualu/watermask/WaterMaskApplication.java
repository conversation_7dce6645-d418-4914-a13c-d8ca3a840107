package com.hualu.watermask;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

@SpringBootApplication
@MapperScan("com.hualu.watermask.**.mapper")
@EnableTransactionManagement
public class WaterMaskApplication {

  public static void main(String[] args) {
    SpringApplication.run(WaterMaskApplication.class, args);
  }
}
