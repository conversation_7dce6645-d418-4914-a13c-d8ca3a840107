spring:
  # redis配置
  redis:
    # Redis数据库索引（默认为0）
    database: 1
    # Redis服务器地址
    host: 127.0.0.1
    # Redis服务器连接端口
    port: 6379
    # Redis服务器连接密码（默认为空）
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池最大连接数
        max-active: 200
        # 连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
        # 连接池中的最大空闲连接
        max-idle: 10
        # 连接池中的最小空闲连接
        min-idle: 0
  datasource:
    druid:
      # 基本配置
      initial-size: 5        # 初始化连接数
      min-idle: 15            # 最小空闲连接数
      max-active: 200         # 最大活跃连接数
      max-wait: 60000        # 获取连接最大等待时间（毫秒）

      # 超时配置
      connect-timeout: 30000 # 连接超时时间（毫秒）
      socket-timeout: 60000  # 数据库操作超时时间（毫秒）
    dynamic:
      primary: master
      datasource:
        master:
          username: PMSDB
          password: HQXlsl663
          url: *******************************************
          type: com.alibaba.druid.pool.DruidDataSource

        mtmsdb:
          username: mtmsdb
          password: Mtms!@#1234
          url: ******************************************
          type: com.alibaba.druid.pool.DruidDataSource

        gdgs:
          username: gdgs
          password: Gdgs!@#1234
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ******************************************
        gis:
          username: gisdata
          password: gisdata2015
          url: ********************************************
          type: com.alibaba.druid.pool.DruidDataSource

        memsdb:
          username: MEMSDB
          password: Mems!@#1234
          driver-class-name: oracle.jdbc.driver.OracleDriver
          url: ******************************************
  main:
    allow-circular-references: true
  servlet:
    multipart:
      #      单个上传文件的最大大小
      max-file-size: 200MB
      #        整个请求（包含所有文件）的最大大小
      max-request-size: 400MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    serialization:
      write-dates-as-timestamps: false
mybatis-plus:
  configuration:
    #    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    default-fetch-size: 1000
  mapper-locations: classpath:mapper/**/*.xml
server:
  port: 9126
  servlet:
    context-path: /watermask

sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: satoken
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: -1
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: false
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: true
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true


