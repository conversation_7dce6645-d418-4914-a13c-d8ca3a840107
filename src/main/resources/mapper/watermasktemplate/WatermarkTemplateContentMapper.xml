<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.watermasktemplate.mapper.WatermarkTemplateContentMapper">
  <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplateContent">
    <!--@mbg.generated-->
    <!--@Table WATERMARK_TEMPLATE_CONTENT-->
    <id column="CONTENT_ID" jdbcType="VARCHAR" property="contentId" />
    <result column="TEMPLATE_ID" jdbcType="VARCHAR" property="templateId" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="SORT_ORDER" jdbcType="DECIMAL" property="sortOrder" />
    <result column="FONT_COLOR" jdbcType="VARCHAR" property="fontColor" />
    <result column="BACKGROUND_COLOR" jdbcType="VARCHAR" property="backgroundColor" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CONTENT_ID, TEMPLATE_ID, TITLE, SORT_ORDER, FONT_COLOR, BACKGROUND_COLOR
  </sql>
</mapper>