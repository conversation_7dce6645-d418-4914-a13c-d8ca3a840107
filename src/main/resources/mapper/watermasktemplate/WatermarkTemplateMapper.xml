<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.watermasktemplate.mapper.WatermarkTemplateMapper">
  <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.watermasktemplate.entity.WatermarkTemplate">
    <!--@mbg.generated-->
    <!--@Table WATERMARK_TEMPLATE-->
    <id column="TEMPLATE_ID" jdbcType="VARCHAR" property="templateId" />
    <result column="TEAM_ID" jdbcType="VARCHAR" property="teamId" />
    <result column="TEMPLATE_NAME" jdbcType="VARCHAR" property="templateName" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="UPDATED_TIME" jdbcType="TIMESTAMP" property="updatedTime" />
    <result column="UPDATED_BY" jdbcType="VARCHAR" property="updatedBy" />
    <result column="DEL_FLAG" jdbcType="DECIMAL" property="delFlag" />
    <result column="IMAGE_ID" jdbcType="VARCHAR" property="imageId" />
    <result column="WIDTH" jdbcType="INTEGER" property="width" />
    <result column="HEIGHT" jdbcType="INTEGER" property="height" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    TEMPLATE_ID, TEAM_ID, TEMPLATE_NAME, CREATED_BY, CREATED_TIME, UPDATED_TIME, UPDATED_BY, 
    DEL_FLAG, IMAGE_ID, WIDTH, HEIGHT
  </sql>
</mapper>