<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.user.mapper.WsUserTeamsMapper">
  <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.user.entity.WsUserTeams">
    <!--@mbg.generated-->
    <!--@Table WS_USER_TEAMS-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="TEAM_ID" jdbcType="VARCHAR" property="teamId" />
    <result column="ROLE_ID" jdbcType="VARCHAR" property="roleId" />
    <result column="ASSIGNED_DATE" jdbcType="TIMESTAMP" property="assignedDate" />
    <result column="ASSIGNED_BY" jdbcType="VARCHAR" property="assignedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, USER_ID, TEAM_ID, ROLE_ID, ASSIGNED_DATE, ASSIGNED_BY
  </sql>

    <!-- 获取今日团队拍照人数统计 -->
    <select id="getPhotoPersonCounts" resultType="java.util.Map">
        SELECT 
            pt.TEAM_ID, 
            COUNT(DISTINCT p.CREATED_BY) as CNT 
        FROM 
            WS_PHOTO_TEAMS pt
        JOIN 
            WATERMARK_PHOTO p ON pt.PHOTO_ID = p.PHOTO_ID
        WHERE 
            pt.TEAM_ID IN 
            <foreach item="item" collection="teamIds" open="(" separator="," close=")">
                #{item}
            </foreach>
            AND p.CAPTURE_TIME BETWEEN #{startDate} AND #{endDate}
            AND p.DEL_FLAG = 0
        GROUP BY 
            pt.TEAM_ID
    </select>
    
    <!-- 获取今日团队拍照数量统计 -->
    <select id="getTeamPhotoCountsToday" resultType="java.util.Map">
        SELECT 
            pt.TEAM_ID, 
            COUNT(*) as CNT 
        FROM 
            WS_PHOTO_TEAMS pt
        JOIN 
            WATERMARK_PHOTO p ON pt.PHOTO_ID = p.PHOTO_ID
        WHERE 
            pt.TEAM_ID IN 
            <foreach item="teamId" collection="teamIds" open="(" separator="," close=")">
                #{teamId}
            </foreach>
            AND p.CAPTURE_TIME BETWEEN #{startDate} AND #{endDate}
            AND p.DEL_FLAG = 0
        GROUP BY 
            pt.TEAM_ID
    </select>

    <!-- 获取单个团队的今日拍照人数 -->
    <select id="getTeamPhotographerCount" resultType="java.lang.Integer">
        SELECT 
            COUNT(DISTINCT p.CREATED_BY) 
        FROM 
            WS_PHOTO_TEAMS pt
        JOIN 
            WATERMARK_PHOTO p ON pt.PHOTO_ID = p.PHOTO_ID
        WHERE 
            pt.TEAM_ID = #{teamId}
            AND p.CAPTURE_TIME BETWEEN #{startDate} AND #{endDate}
            AND p.DEL_FLAG = 0
    </select>
    
    <!-- 获取单个团队的今日拍照数量 -->
    <select id="getTeamPhotoCount" resultType="java.lang.Integer">
        SELECT 
            COUNT(*) 
        FROM 
            WS_PHOTO_TEAMS pt
        JOIN 
            WATERMARK_PHOTO p ON pt.PHOTO_ID = p.PHOTO_ID
        WHERE 
            pt.TEAM_ID = #{teamId}
            AND p.CAPTURE_TIME BETWEEN #{startDate} AND #{endDate}
            AND p.DEL_FLAG = 0
    </select>

</mapper>