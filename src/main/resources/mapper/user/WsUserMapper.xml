<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.user.mapper.WsUserMapper">
  <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.user.entity.WsUser">
    <!--@mbg.generated-->
    <!--@Table WS_USER-->
    <id column="USER_ID" jdbcType="VARCHAR" property="userId" />
    <result column="USER_CODE" jdbcType="VARCHAR" property="userCode" />
    <result column="USERNAME" jdbcType="VARCHAR" property="username" />
    <result column="EMAIL" jdbcType="VARCHAR" property="email" />
    <result column="PASSWORD" jdbcType="VARCHAR" property="password" />
    <result column="PHONE_NUMBER" jdbcType="VARCHAR" property="phoneNumber" />
    <result column="ENABLED" jdbcType="DECIMAL" property="enabled" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="LOGIN_DATE" jdbcType="TIMESTAMP" property="loginDate" />
    <result column="DEL_FLAG" jdbcType="DECIMAL" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    USER_ID, USER_CODE, USERNAME, EMAIL, "PASSWORD", PHONE_NUMBER, ENABLED, CREATED_BY, 
    CREATION_DATE, LOGIN_DATE, DEL_FLAG
  </sql>
</mapper>