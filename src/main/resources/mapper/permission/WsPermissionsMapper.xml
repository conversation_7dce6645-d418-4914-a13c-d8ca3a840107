<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.permission.mapper.WsPermissionsMapper">
  <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.permission.entity.WsPermissions">
    <!--@mbg.generated-->
    <!--@Table WS_PERMISSIONS-->
    <id column="PERMISSION_ID" jdbcType="VARCHAR" property="permissionId" />
    <result column="PERMISSION_CODE" jdbcType="VARCHAR" property="permissionCode" />
    <result column="PERMISSION_NAME" jdbcType="VARCHAR" property="permissionName" />
    <result column="PERMISSION_DESCRIPTION" jdbcType="VARCHAR" property="permissionDescription" />
    <result column="CATEGORY" jdbcType="VARCHAR" property="category" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="DEL_FLAG" jdbcType="DECIMAL" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PERMISSION_ID, PERMISSION_CODE, PERMISSION_NAME, PERMISSION_DESCRIPTION, CATEGORY, 
    CREATED_BY, CREATION_DATE, DEL_FLAG
  </sql>
</mapper>