<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.watermaskphoto.mapper.WatermarkPhotoCustomMapper">
  <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhotoCustom">
    <!--@mbg.generated-->
    <!--@Table WATERMARK_PHOTO_CUSTOM-->
    <id column="CUSTOM_ID" jdbcType="VARCHAR" property="customId" />
    <result column="PHOTO_ID" jdbcType="VARCHAR" property="photoId" />
    <result column="TITLE" jdbcType="VARCHAR" property="title" />
    <result column="CONTENT" jdbcType="VARCHAR" property="content" />
    <result column="SORT_ORDER" jdbcType="DECIMAL" property="sortOrder" />
    <result column="FONT_COLOR" jdbcType="VARCHAR" property="fontColor" />
    <result column="BACKGROUND_COLOR" jdbcType="VARCHAR" property="backgroundColor" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    CUSTOM_ID, PHOTO_ID, TITLE, CONTENT, SORT_ORDER, FONT_COLOR, BACKGROUND_COLOR
  </sql>
</mapper>