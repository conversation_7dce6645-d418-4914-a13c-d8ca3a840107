<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.watermaskphoto.mapper.WatermarkPhotoMapper">
  <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.watermaskphoto.entity.WatermarkPhoto">
    <!--@mbg.generated-->
    <!--@Table WATERMARK_PHOTO-->
    <id column="PHOTO_ID" jdbcType="VARCHAR" property="photoId" />
    <result column="PHOTO_CODE" jdbcType="VARCHAR" property="photoCode" />
    <result column="CAPTURE_TIME" jdbcType="TIMESTAMP" property="captureTime" />
    <result column="WEATHER" jdbcType="VARCHAR" property="weather" />
    <result column="LOCATION" jdbcType="VARCHAR" property="location" />
    <result column="ELEVATION" jdbcType="DECIMAL" property="elevation" />
    <result column="AZIMUTH" jdbcType="DECIMAL" property="azimuth" />
    <result column="SPEED" jdbcType="DECIMAL" property="speed" />
    <result column="LONGITUDE" jdbcType="DECIMAL" property="longitude" />
    <result column="LATITUDE" jdbcType="DECIMAL" property="latitude" />
    <result column="PHOTOGRAPHER" jdbcType="VARCHAR" property="photographer" />
    <result column="REFERENCE_TEXT" jdbcType="VARCHAR" property="referenceText" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATED_TIME" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="DEL_FLAG" jdbcType="DECIMAL" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    PHOTO_ID, PHOTO_CODE, CAPTURE_TIME, WEATHER, "LOCATION", ELEVATION, AZIMUTH, SPEED, 
    LONGITUDE, LATITUDE, PHOTOGRAPHER, REFERENCE_TEXT, CREATED_BY, CREATED_TIME, DEL_FLAG
  </sql>
</mapper>