<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.record.mapper.WsExportRecordMapper">

    <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.record.entity.WsExportRecord">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="applyTime" column="APPLY_TIME" jdbcType="TIMESTAMP"/>
            <result property="downloadCount" column="DOWNLOAD_COUNT" jdbcType="DECIMAL"/>
            <result property="memberCount" column="MEMBER_COUNT" jdbcType="DECIMAL"/>
            <result property="timeRange" column="TIME_RANGE" jdbcType="VARCHAR"/>
            <result property="fileName" column="FILE_NAME" jdbcType="VARCHAR"/>
            <result property="fileType" column="FILE_TYPE" jdbcType="VARCHAR"/>
            <result property="isDeleted" column="IS_DELETED" jdbcType="DECIMAL"/>
            <result property="status" column="STATUS" jdbcType="DECIMAL"/>
            <result property="createdBy" column="CREATED_BY" jdbcType="VARCHAR"/>
            <result property="createdDate" column="CREATED_DATE" jdbcType="TIMESTAMP"/>
            <result property="localPath" column="LOCAL_PATH" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,APPLY_TIME,DOWNLOAD_COUNT,
        MEMBER_COUNT,TIME_RANGE,FILE_NAME,
        FILE_TYPE,IS_DELETED,STATUS,
        CREATED_BY,CREATED_DATE,LOCAL_PATH
    </sql>
</mapper>
