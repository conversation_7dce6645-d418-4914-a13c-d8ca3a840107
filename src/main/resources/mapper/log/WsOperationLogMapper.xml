<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.log.mapper.WsOperationLogMapper">
  <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.log.entity.WsOperationLog">
    <!--@mbg.generated-->
    <!--@Table WS_OPERATION_LOG-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="OPERATION_TIME" jdbcType="TIMESTAMP" property="operationTime" />
    <result column="OPERATOR_NAME" jdbcType="VARCHAR" property="operatorName" />
    <result column="OPERATOR_CODE" jdbcType="VARCHAR" property="operatorCode" />
    <result column="BUSINESS_TYPE" jdbcType="VARCHAR" property="businessType" />
    <result column="OPERATION_CONTENT" jdbcType="VARCHAR" property="operationContent" />
    <result column="CHANGE_CONTENT" jdbcType="VARCHAR" property="changeContent" />
    <result column="OPERATION_TYPE" jdbcType="VARCHAR" property="operationType" />
    <result column="IP_ADDRESS" jdbcType="VARCHAR" property="ipAddress" />
    <result column="STATUS" jdbcType="DECIMAL" property="status" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, OPERATION_TIME, OPERATOR_NAME, OPERATOR_CODE, BUSINESS_TYPE, OPERATION_CONTENT, 
    CHANGE_CONTENT, OPERATION_TYPE, IP_ADDRESS, "STATUS"
  </sql>
</mapper>