<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.role.mapper.WsRolePermissionsMapper">
  <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.role.entity.WsRolePermissions">
    <!--@mbg.generated-->
    <!--@Table WS_ROLE_PERMISSIONS-->
    <id column="ID" jdbcType="VARCHAR" property="id" />
    <result column="ROLE_ID" jdbcType="VARCHAR" property="roleId" />
    <result column="PERMISSION_ID" jdbcType="VARCHAR" property="permissionId" />
    <result column="GRANTED_DATE" jdbcType="TIMESTAMP" property="grantedDate" />
    <result column="GRANTED_BY" jdbcType="VARCHAR" property="grantedBy" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ID, ROLE_ID, PERMISSION_ID, GRANTED_DATE, GRANTED_BY
  </sql>
</mapper>