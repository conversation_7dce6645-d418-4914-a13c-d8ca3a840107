<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.role.mapper.WsRoleMapper">
  <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.role.entity.WsRole">
    <!--@mbg.generated-->
    <!--@Table WS_ROLE-->
    <id column="ROLE_ID" jdbcType="VARCHAR" property="roleId" />
    <result column="ROLE_CODE" jdbcType="VARCHAR" property="roleCode" />
    <result column="ROLE_NAME" jdbcType="VARCHAR" property="roleName" />
    <result column="ROLE_DESCRIPTION" jdbcType="VARCHAR" property="roleDescription" />
    <result column="CREATED_BY" jdbcType="VARCHAR" property="createdBy" />
    <result column="CREATION_DATE" jdbcType="TIMESTAMP" property="creationDate" />
    <result column="DEL_FLAG" jdbcType="DECIMAL" property="delFlag" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ROLE_ID, ROLE_CODE, ROLE_NAME, ROLE_DESCRIPTION, CREATED_BY, CREATION_DATE, DEL_FLAG
  </sql>
</mapper>