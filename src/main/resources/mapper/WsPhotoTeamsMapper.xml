<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.user.mapper.WsPhotoTeamsMapper">

    <resultMap id="BaseResultMap" type="com.hualu.watermask.modules.user.entity.WsPhotoTeams">
            <id property="id" column="ID" jdbcType="VARCHAR"/>
            <result property="photoId" column="PHOTO_ID" jdbcType="VARCHAR"/>
            <result property="teamId" column="TEAM_ID" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="Base_Column_List">
        ID,PHOTO_ID,TEAM_ID
    </sql>
</mapper>
