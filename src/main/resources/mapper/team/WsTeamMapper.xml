<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hualu.watermask.modules.team.mapper.WsTeamMapper">
  <select id="getUserRoleInUpTeam" resultType="java.lang.String">
    select r.ROLE_CODE from WS_USER_TEAMS s
    inner join WS_ROLE r on s.ROLE_ID = r.ROLE_ID
    where r.DEL_FLAG = 0 and s.USER_ID = #{userId} and s.TEAM_ID in (
    select t.TEAM_ID from WS_TEAM t
    where t.DEL_FLAG = 0
    start with t.TEAM_ID = #{teamId}
    connect by prior t.PARENT_TEAM_ID = t.TEAM_ID)
  </select>

  <select id="getUpUserCurrentTeam" resultType="com.hualu.watermask.modules.team.entity.WsTeam">
    select s.*, r.ROLE_CODE roleCode, r.ROLE_NAME roleName, s.TEAM_ID currentTeamId  from WS_USER_TEAMS s
    left join WS_ROLE r on s.ROLE_ID = r.ROLE_ID and r.DEL_FLAG = 0
    where s.USER_ID = #{userId} and s.TEAM_ID in (
    select t.TEAM_ID from WS_TEAM t
    where t.DEL_FLAG = 0
    start with t.TEAM_ID = #{teamId}
    connect by prior t.PARENT_TEAM_ID = t.TEAM_ID)
  </select>

  <select id="getUserDownTreeByTeamId"
    resultType="com.hualu.watermask.modules.user.entity.WsUserTeams">
    select s.*, r.ROLE_CODE roleCode, r.ROLE_NAME roleName from WS_USER_TEAMS s
                                         left join WS_ROLE r on s.ROLE_ID = r.ROLE_ID and r.DEL_FLAG = 0
    where s.TEAM_ID in (
      select t.TEAM_ID from WS_TEAM t
      where t.DEL_FLAG = 0
      start with t.TEAM_ID = #{teamId}
      connect by prior t.TEAM_ID = t.PARENT_TEAM_ID)
  </select>

</mapper>