plugins {
  id 'org.springframework.boot' version '2.7.4'
  id 'io.spring.dependency-management' version '1.0.14.RELEASE'
  id 'java'
}

group = 'com.hualu.gis'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = "1.8"

configurations {
  compileOnly {
    extendsFrom annotationProcessor
  }
}

repositories {
  flatDir {
    dirs 'lib'
  }
  mavenCentral()
}

dependencies {
  implementation 'org.springframework.boot:spring-boot-starter-web'
  developmentOnly 'org.springframework.boot:spring-boot-devtools'
  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
  testImplementation 'org.springframework.boot:spring-boot-starter-test'
  testRuntimeOnly 'org.junit.platform:junit-platform-launcher'


  //数据库配置
  implementation 'com.alibaba:druid-spring-boot-starter:1.2.16'
  implementation group: 'com.oracle.database.nls', name: 'orai18n', version: '21.7.0.0'
  runtimeOnly group: 'com.oracle.database.jdbc', name: 'ojdbc8', version: '21.1.0.0'

  implementation 'com.baomidou:mybatis-plus-boot-starter:3.5.4.1'
  implementation 'com.baomidou:mybatis-plus-core:3.5.4.1'
  implementation 'com.baomidou:dynamic-datasource-spring-boot-starter:4.2.0'

  implementation 'org.locationtech.jts:jts-core:1.19.0'
  implementation 'org.apache.commons:commons-math3:3.6.1'
  // https://mvnrepository.com/artifact/cn.hutool/hutool-core
  implementation 'cn.hutool:hutool-all:5.8.24'
  // Sa-Token 权限认证，在线文档：https://sa-token.cc
  implementation 'cn.dev33:sa-token-spring-boot-starter:1.44.0'
  implementation 'org.springframework.boot:spring-boot-starter-validation'

  // Caffeine 缓存
  implementation 'com.github.ben-manes.caffeine:caffeine:2.9.3'

  // Spring Cache 集成（可选，如果你需要使用 @Cacheable 等注解）
  implementation 'org.springframework.boot:spring-boot-starter-cache'

  implementation ("com.alibaba:easyexcel:4.0.3")
  implementation 'com.deepoove:poi-tl:1.12.2'

  implementation 'cn.dev33:sa-token-redis-template:1.44.0'
  implementation 'org.apache.commons:commons-pool2'



  implementation 'com.squareup.okhttp3:okhttp:3.8.1'
  implementation group: 'com.alibaba.fastjson2', name: 'fastjson2', version: '2.0.57'
  testImplementation 'org.springframework:spring-test:5.3.18'
}

tasks.named('test') {
  useJUnitPlatform()
}
